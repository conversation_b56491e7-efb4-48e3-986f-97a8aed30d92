import { 
  Scenario, 
  ScenarioRule, 
  WeatherEvent, 
  Faction, 
  WeatherType, 
  TerrainType,
  Position 
} from '../types';

/**
 * 剧本管理器
 * 管理不同的游戏剧本，包括三国演义、赤壁之战等
 */

// 三国演义剧本
export const SCENARIO_THREE_KINGDOMS: Scenario = {
  id: 'three_kingdoms',
  name: '三国演义',
  description: '经典三国题材，群雄逐鹿，身份暗战',
  mapSize: { width: 16, height: 8 },
  maxTurns: 120,
  specialRules: [
    {
      id: 'emperor_bonus',
      name: '天子威仪',
      description: '主公每回合额外获得1金币',
      type: 'resource_bonus',
      conditions: { identity: 'emperor' },
      effects: { goldPerTurn: 1 }
    },
    {
      id: 'rebel_unity',
      name: '反贼联盟',
      description: '反贼之间不会因为相邻而触发战斗',
      type: 'unit_bonus',
      conditions: { identity: 'rebel' },
      effects: { noFriendlyFire: true }
    }
  ],
  initialResources: { gold: 50, population: 100 },
  availableFactions: [Faction.SHU, Faction.WEI, Faction.WU, Faction.QUN],
  weatherEvents: [
    { turn: 15, weather: WeatherType.RAIN, duration: 3 },
    { turn: 30, weather: WeatherType.SANDSTORM, duration: 2 },
    { turn: 60, weather: WeatherType.WINTER, duration: 5 },
    { turn: 90, weather: WeatherType.THUNDER, duration: 2 }
  ]
};

// 赤壁之战剧本
export const SCENARIO_RED_CLIFF: Scenario = {
  id: 'red_cliff',
  name: '赤壁之战',
  description: '火攻连环，以少胜多的经典战役',
  mapSize: { width: 12, height: 10 },
  maxTurns: 80,
  specialRules: [
    {
      id: 'fire_attack_bonus',
      name: '火攻战术',
      description: '火系法术伤害提升50%',
      type: 'terrain_effect',
      conditions: { spellType: 'fire' },
      effects: { damageMultiplier: 1.5 }
    },
    {
      id: 'naval_combat',
      name: '水战精通',
      description: '吴国武将在河流地形攻击力+20%',
      type: 'unit_bonus',
      conditions: { faction: 'wu', terrain: 'river' },
      effects: { attackBonus: 0.2 }
    },
    {
      id: 'chain_ships',
      name: '连环船',
      description: '河流地形的单位受到火焰伤害时，伤害会传播到相邻单位',
      type: 'terrain_effect',
      conditions: { terrain: 'river', damageType: 'fire' },
      effects: { spreadDamage: true }
    }
  ],
  initialResources: { gold: 40, population: 80 },
  availableFactions: [Faction.SHU, Faction.WEI, Faction.WU],
  weatherEvents: [
    { turn: 10, weather: WeatherType.CLEAR, duration: 5 },
    { turn: 25, weather: WeatherType.RAIN, duration: 3 },
    { turn: 50, weather: WeatherType.CLEAR, duration: 10 }
  ]
};

// 官渡之战剧本
export const SCENARIO_GUANDU: Scenario = {
  id: 'guandu',
  name: '官渡之战',
  description: '曹操vs袁绍，智谋与实力的较量',
  mapSize: { width: 14, height: 6 },
  maxTurns: 60,
  specialRules: [
    {
      id: 'supply_line',
      name: '补给线',
      description: '控制更多粮草地块的一方，全军攻击力+10%',
      type: 'victory_condition',
      conditions: { terrainControl: 'farm' },
      effects: { armyBonus: 0.1 }
    },
    {
      id: 'night_raid',
      name: '夜袭',
      description: '每10回合可以发动一次夜袭，无视地形移动',
      type: 'unit_bonus',
      conditions: { turnMod: 10 },
      effects: { ignoreTerrainMovement: true }
    }
  ],
  initialResources: { gold: 60, population: 120 },
  availableFactions: [Faction.WEI, Faction.QUN],
  weatherEvents: [
    { turn: 20, weather: WeatherType.SANDSTORM, duration: 4 },
    { turn: 40, weather: WeatherType.WINTER, duration: 3 }
  ]
};

// 夷陵之战剧本
export const SCENARIO_YILING: Scenario = {
  id: 'yiling',
  name: '夷陵之战',
  description: '刘备伐吴，陆逊火烧连营',
  mapSize: { width: 18, height: 6 },
  maxTurns: 100,
  specialRules: [
    {
      id: 'forest_fire',
      name: '火烧连营',
      description: '森林地形容易起火，火焰会蔓延',
      type: 'terrain_effect',
      conditions: { terrain: 'forest' },
      effects: { fireSpread: true, fireDamage: 1.5 }
    },
    {
      id: 'revenge_fury',
      name: '复仇之怒',
      description: '蜀军初期攻击力+30%，但每回合递减5%',
      type: 'unit_bonus',
      conditions: { faction: 'shu', turnLimit: 10 },
      effects: { attackBonus: 0.3, decay: 0.05 }
    }
  ],
  initialResources: { gold: 45, population: 90 },
  availableFactions: [Faction.SHU, Faction.WU],
  weatherEvents: [
    { turn: 5, weather: WeatherType.CLEAR, duration: 15 },
    { turn: 35, weather: WeatherType.RAIN, duration: 5 },
    { turn: 70, weather: WeatherType.THUNDER, duration: 3 }
  ]
};

/**
 * 剧本管理器类
 */
export class ScenarioManager {
  private static scenarios: Map<string, Scenario> = new Map();

  static {
    // 初始化所有剧本
    this.scenarios.set(SCENARIO_THREE_KINGDOMS.id, SCENARIO_THREE_KINGDOMS);
    this.scenarios.set(SCENARIO_RED_CLIFF.id, SCENARIO_RED_CLIFF);
    this.scenarios.set(SCENARIO_GUANDU.id, SCENARIO_GUANDU);
    this.scenarios.set(SCENARIO_YILING.id, SCENARIO_YILING);
  }

  /**
   * 获取所有可用剧本
   */
  public static getAllScenarios(): Scenario[] {
    return Array.from(this.scenarios.values());
  }

  /**
   * 根据ID获取剧本
   */
  public static getScenario(id: string): Scenario | null {
    return this.scenarios.get(id) || null;
  }

  /**
   * 获取推荐剧本（根据玩家数量）
   */
  public static getRecommendedScenario(playerCount: number): Scenario {
    if (playerCount === 8) {
      return SCENARIO_THREE_KINGDOMS; // 8人身份局
    } else if (playerCount <= 4) {
      return SCENARIO_RED_CLIFF; // 小规模战斗
    } else if (playerCount === 2) {
      return SCENARIO_GUANDU; // 1v1对决
    } else {
      return SCENARIO_YILING; // 中等规模
    }
  }

  /**
   * 验证剧本规则
   */
  public static validateScenarioRules(scenario: Scenario, gameState: any): boolean {
    for (const rule of scenario.specialRules) {
      if (!this.checkRuleConditions(rule, gameState)) {
        return false;
      }
    }
    return true;
  }

  /**
   * 应用剧本规则效果
   */
  public static applyScenarioEffects(scenario: Scenario, gameState: any): any {
    let modifiedState = { ...gameState };

    for (const rule of scenario.specialRules) {
      if (this.checkRuleConditions(rule, gameState)) {
        modifiedState = this.applyRuleEffects(rule, modifiedState);
      }
    }

    return modifiedState;
  }

  /**
   * 检查规则条件
   */
  private static checkRuleConditions(rule: ScenarioRule, gameState: any): boolean {
    const { conditions } = rule;

    // 检查身份条件
    if (conditions.identity && gameState.currentPlayer?.identity !== conditions.identity) {
      return false;
    }

    // 检查阵营条件
    if (conditions.faction && gameState.currentPlayer?.faction !== conditions.faction) {
      return false;
    }

    // 检查地形条件
    if (conditions.terrain && gameState.currentTerrain !== conditions.terrain) {
      return false;
    }

    // 检查回合条件
    if (conditions.turnMod && gameState.currentTurn % conditions.turnMod !== 0) {
      return false;
    }

    return true;
  }

  /**
   * 应用规则效果
   */
  private static applyRuleEffects(rule: ScenarioRule, gameState: any): any {
    const { effects } = rule;
    const newState = { ...gameState };

    // 应用资源加成
    if (effects.goldPerTurn && newState.currentPlayer) {
      newState.currentPlayer.resources.gold += effects.goldPerTurn;
    }

    // 应用攻击力加成
    if (effects.attackBonus && newState.currentPlayer?.armies) {
      for (const army of newState.currentPlayer.armies) {
        for (const card of army.cards) {
          if (card.type === 'hero') {
            card.stats.attack *= (1 + effects.attackBonus);
          }
        }
      }
    }

    // 应用伤害倍数
    if (effects.damageMultiplier && newState.combatState) {
      newState.combatState.damageMultiplier = effects.damageMultiplier;
    }

    return newState;
  }

  /**
   * 获取剧本的天气事件
   */
  public static getWeatherEvents(scenarioId: string, currentTurn: number): WeatherEvent[] {
    const scenario = this.getScenario(scenarioId);
    if (!scenario) return [];

    return scenario.weatherEvents.filter(event => 
      event.turn <= currentTurn && 
      event.turn + event.duration > currentTurn
    );
  }

  /**
   * 检查剧本胜利条件
   */
  public static checkScenarioVictory(scenario: Scenario, gameState: any): { winner: string | null; reason: string } {
    // 检查特殊胜利条件
    for (const rule of scenario.specialRules) {
      if (rule.type === 'victory_condition') {
        const result = this.checkVictoryRule(rule, gameState);
        if (result.winner) {
          return result;
        }
      }
    }

    // 默认胜利条件（回合数限制）
    if (gameState.currentTurn >= scenario.maxTurns) {
      return this.calculateFinalScore(gameState);
    }

    return { winner: null, reason: '' };
  }

  /**
   * 检查胜利规则
   */
  private static checkVictoryRule(rule: ScenarioRule, gameState: any): { winner: string | null; reason: string } {
    // 这里可以实现具体的胜利条件检查逻辑
    // 例如：控制特定地块数量、消灭特定目标等
    return { winner: null, reason: '' };
  }

  /**
   * 计算最终得分
   */
  private static calculateFinalScore(gameState: any): { winner: string | null; reason: string } {
    // 根据领土、资源、存活单位等计算得分
    let maxScore = 0;
    let winner = null;

    for (const player of gameState.players) {
      if (!player.isAlive) continue;

      const score = 
        player.territories.length * 10 + 
        player.resources.gold * 1 + 
        player.armies.length * 20;

      if (score > maxScore) {
        maxScore = score;
        winner = player.id;
      }
    }

    return { 
      winner, 
      reason: winner ? '回合结束，根据综合得分获胜' : '平局' 
    };
  }
}
