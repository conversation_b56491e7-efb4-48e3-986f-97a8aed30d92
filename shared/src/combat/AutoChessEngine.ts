import { 
  HeroCard, 
  Position, 
  UnitType, 
  SkillEffect, 
  UNIT_COUNTER_MAP,
  GAME_CONFIG 
} from '../types';
import { SynergyManager } from '../faction/FactionSystem';

/**
 * 战斗单位状态
 */
export interface CombatUnit {
  id: string;
  heroCard: HeroCard;
  position: Position;
  currentHealth: number;
  currentEnergy: number;
  buffs: CombatBuff[];
  debuffs: CombatBuff[];
  isAlive: boolean;
  ownerId: string;
  target?: CombatUnit;
  lastActionTime: number;
}

/**
 * 战斗增益/减益效果
 */
export interface CombatBuff {
  id: string;
  type: 'attack' | 'defense' | 'speed' | 'heal' | 'energy';
  value: number;
  duration: number;
  source: string;
}

/**
 * 战斗事件
 */
export interface CombatEvent {
  type: 'damage' | 'heal' | 'skill' | 'death' | 'move';
  timestamp: number;
  source?: CombatUnit;
  target?: CombatUnit;
  value?: number;
  description: string;
}

/**
 * 战斗结果
 */
export interface CombatResult {
  winner: string | null;
  duration: number;
  events: CombatEvent[];
  finalState: CombatUnit[];
  experience: Record<string, number>;
}

/**
 * 自走棋战斗引擎
 */
export class AutoChessEngine {
  private units: CombatUnit[] = [];
  private events: CombatEvent[] = [];
  private startTime: number = 0;
  private isRunning: boolean = false;
  private boardWidth: number = GAME_CONFIG.COMBAT_BOARD_WIDTH;
  private boardHeight: number = GAME_CONFIG.COMBAT_BOARD_HEIGHT;

  constructor() {
    this.startTime = Date.now();
  }

  /**
   * 初始化战斗
   */
  public initializeCombat(playerUnits: Map<string, HeroCard[]>): void {
    this.units = [];
    this.events = [];
    this.startTime = Date.now();

    let unitId = 0;
    
    // 为每个玩家部署单位
    for (const [playerId, heroCards] of playerUnits) {
      const isPlayerOne = Array.from(playerUnits.keys()).indexOf(playerId) === 0;
      
      heroCards.forEach((heroCard, index) => {
        const position = this.getDeploymentPosition(index, isPlayerOne);
        
        const unit: CombatUnit = {
          id: `unit_${unitId++}`,
          heroCard: { ...heroCard },
          position,
          currentHealth: heroCard.stats.health,
          currentEnergy: heroCard.stats.energy,
          buffs: [],
          debuffs: [],
          isAlive: true,
          ownerId: playerId,
          lastActionTime: 0
        };

        this.units.push(unit);
      });
    }

    // 应用羁绊效果
    this.applySynergies();
  }

  /**
   * 获取部署位置
   */
  private getDeploymentPosition(index: number, isPlayerOne: boolean): Position {
    const row = Math.floor(index / 4);
    const col = index % 4;
    
    if (isPlayerOne) {
      // 玩家1在左侧部署
      return { x: col, y: row + 2 };
    } else {
      // 玩家2在右侧部署
      return { x: this.boardWidth - 1 - col, y: row + 2 };
    }
  }

  /**
   * 应用羁绊效果
   */
  private applySynergies(): void {
    const playerUnits = new Map<string, CombatUnit[]>();
    
    // 按玩家分组单位
    for (const unit of this.units) {
      if (!playerUnits.has(unit.ownerId)) {
        playerUnits.set(unit.ownerId, []);
      }
      playerUnits.get(unit.ownerId)!.push(unit);
    }

    // 为每个玩家计算羁绊
    for (const [playerId, units] of playerUnits) {
      const heroCards = units.map(u => u.heroCard);
      const synergies = SynergyManager.calculateFactionSynergies(heroCards);
      const tagSynergies = SynergyManager.calculateTagSynergies(heroCards);

      // 应用羁绊效果到单位
      for (const unit of units) {
        this.applySynergiesToUnit(unit, synergies);
        this.applySynergiesToUnit(unit, tagSynergies);
      }
    }
  }

  /**
   * 应用羁绊效果到单位
   */
  private applySynergiesToUnit(unit: CombatUnit, synergies: Map<string, SkillEffect[]>): void {
    for (const [synergyId, effects] of synergies) {
      for (const effect of effects) {
        const buff: CombatBuff = {
          id: `synergy_${synergyId}`,
          type: this.getBuffType(effect.type),
          value: effect.value || 0,
          duration: effect.duration || -1,
          source: 'synergy'
        };

        if (effect.target === 'self' || effect.target === 'ally') {
          unit.buffs.push(buff);
        }
      }
    }
  }

  /**
   * 获取增益类型
   */
  private getBuffType(effectType: string): 'attack' | 'defense' | 'speed' | 'heal' | 'energy' {
    switch (effectType) {
      case 'damage': return 'attack';
      case 'buff': return 'attack';
      case 'heal': return 'heal';
      default: return 'attack';
    }
  }

  /**
   * 开始自动战斗
   */
  public startAutoCombat(): Promise<CombatResult> {
    return new Promise((resolve) => {
      this.isRunning = true;
      
      const combatLoop = () => {
        if (!this.isRunning) {
          resolve(this.getCombatResult());
          return;
        }

        // 更新所有单位
        this.updateUnits();

        // 检查战斗结束条件
        if (this.checkCombatEnd()) {
          this.isRunning = false;
          resolve(this.getCombatResult());
          return;
        }

        // 继续战斗循环
        setTimeout(combatLoop, 100); // 每100ms更新一次
      };

      combatLoop();
    });
  }

  /**
   * 更新所有单位
   */
  private updateUnits(): void {
    const currentTime = Date.now() - this.startTime;

    for (const unit of this.units) {
      if (!unit.isAlive) continue;

      // 更新增益/减益效果
      this.updateBuffs(unit);

      // 单位AI行为
      this.updateUnitAI(unit, currentTime);
    }
  }

  /**
   * 更新增益/减益效果
   */
  private updateBuffs(unit: CombatUnit): void {
    // 更新增益效果
    unit.buffs = unit.buffs.filter(buff => {
      if (buff.duration > 0) {
        buff.duration--;
      }
      return buff.duration !== 0;
    });

    // 更新减益效果
    unit.debuffs = unit.debuffs.filter(debuff => {
      if (debuff.duration > 0) {
        debuff.duration--;
      }
      return debuff.duration !== 0;
    });
  }

  /**
   * 更新单位AI
   */
  private updateUnitAI(unit: CombatUnit, currentTime: number): void {
    const actionCooldown = 1000 / (unit.heroCard.stats.speed / 100); // 基于速度的行动间隔
    
    if (currentTime - unit.lastActionTime < actionCooldown) {
      return; // 还在冷却中
    }

    // 寻找目标
    if (!unit.target || !unit.target.isAlive) {
      unit.target = this.findTarget(unit);
    }

    if (!unit.target) return;

    // 计算距离
    const distance = this.getDistance(unit.position, unit.target.position);
    const attackRange = this.getAttackRange(unit.heroCard.unitType);

    if (distance <= attackRange) {
      // 在攻击范围内，进行攻击
      this.performAttack(unit, unit.target);
    } else {
      // 移动向目标
      this.moveTowardsTarget(unit, unit.target);
    }

    unit.lastActionTime = currentTime;
  }

  /**
   * 寻找攻击目标
   */
  private findTarget(unit: CombatUnit): CombatUnit | undefined {
    const enemies = this.units.filter(u => 
      u.isAlive && 
      u.ownerId !== unit.ownerId
    );

    if (enemies.length === 0) return undefined;

    // 寻找最近的敌人
    let closestEnemy = enemies[0];
    let closestDistance = this.getDistance(unit.position, closestEnemy.position);

    for (const enemy of enemies) {
      const distance = this.getDistance(unit.position, enemy.position);
      if (distance < closestDistance) {
        closestDistance = distance;
        closestEnemy = enemy;
      }
    }

    return closestEnemy;
  }

  /**
   * 执行攻击
   */
  private performAttack(attacker: CombatUnit, target: CombatUnit): void {
    let damage = attacker.heroCard.stats.attack;

    // 应用增益效果
    for (const buff of attacker.buffs) {
      if (buff.type === 'attack') {
        damage += buff.value;
      }
    }

    // 计算兵种相克
    const counterMultiplier = this.getCounterMultiplier(attacker.heroCard.unitType, target.heroCard.unitType);
    damage = Math.floor(damage * counterMultiplier);

    // 计算护甲减免
    const armor = target.heroCard.stats.armor;
    const finalDamage = Math.max(1, damage - armor);

    // 造成伤害
    target.currentHealth -= finalDamage;

    // 记录战斗事件
    this.addEvent({
      type: 'damage',
      timestamp: Date.now() - this.startTime,
      source: attacker,
      target: target,
      value: finalDamage,
      description: `${attacker.heroCard.heroName} 对 ${target.heroCard.heroName} 造成 ${finalDamage} 点伤害`
    });

    // 检查目标是否死亡
    if (target.currentHealth <= 0) {
      target.isAlive = false;
      this.addEvent({
        type: 'death',
        timestamp: Date.now() - this.startTime,
        target: target,
        description: `${target.heroCard.heroName} 阵亡`
      });
    }

    // 尝试释放技能
    this.tryUseSkill(attacker);
  }

  /**
   * 尝试释放技能
   */
  private tryUseSkill(unit: CombatUnit): void {
    const skill = unit.heroCard.skill;
    if (!skill || !skill.energyCost) return;

    if (unit.currentEnergy >= skill.energyCost) {
      unit.currentEnergy -= skill.energyCost;
      
      // 执行技能效果
      for (const effect of skill.effects) {
        this.applySkillEffect(unit, effect);
      }

      this.addEvent({
        type: 'skill',
        timestamp: Date.now() - this.startTime,
        source: unit,
        description: `${unit.heroCard.heroName} 释放技能 ${skill.name}`
      });
    }
  }

  /**
   * 应用技能效果
   */
  private applySkillEffect(caster: CombatUnit, effect: SkillEffect): void {
    const targets = this.getSkillTargets(caster, effect);

    for (const target of targets) {
      switch (effect.type) {
        case 'damage':
          target.currentHealth -= effect.value || 0;
          if (target.currentHealth <= 0) {
            target.isAlive = false;
          }
          break;
        case 'heal':
          target.currentHealth = Math.min(
            target.currentHealth + (effect.value || 0),
            target.heroCard.stats.health
          );
          break;
        case 'buff':
          target.buffs.push({
            id: `skill_${caster.id}`,
            type: 'attack',
            value: effect.value || 0,
            duration: effect.duration || 3,
            source: caster.id
          });
          break;
      }
    }
  }

  /**
   * 获取技能目标
   */
  private getSkillTargets(caster: CombatUnit, effect: SkillEffect): CombatUnit[] {
    switch (effect.target) {
      case 'self':
        return [caster];
      case 'ally':
        return this.units.filter(u => u.isAlive && u.ownerId === caster.ownerId);
      case 'enemy':
        return this.units.filter(u => u.isAlive && u.ownerId !== caster.ownerId);
      case 'all':
        return this.units.filter(u => u.isAlive);
      default:
        return [];
    }
  }

  /**
   * 移动向目标
   */
  private moveTowardsTarget(unit: CombatUnit, target: CombatUnit): void {
    const dx = target.position.x - unit.position.x;
    const dy = target.position.y - unit.position.y;

    const moveX = dx > 0 ? 1 : dx < 0 ? -1 : 0;
    const moveY = dy > 0 ? 1 : dy < 0 ? -1 : 0;

    const newPosition = {
      x: Math.max(0, Math.min(this.boardWidth - 1, unit.position.x + moveX)),
      y: Math.max(0, Math.min(this.boardHeight - 1, unit.position.y + moveY))
    };

    // 检查位置是否被占用
    const occupied = this.units.some(u => 
      u !== unit && 
      u.isAlive && 
      u.position.x === newPosition.x && 
      u.position.y === newPosition.y
    );

    if (!occupied) {
      unit.position = newPosition;
    }
  }

  /**
   * 获取兵种相克倍数
   */
  private getCounterMultiplier(attackerType: UnitType, defenderType: UnitType): number {
    const counters = UNIT_COUNTER_MAP[attackerType];
    if (counters.includes(defenderType)) {
      return 1.5; // 相克时伤害增加50%
    }
    return 1.0;
  }

  /**
   * 获取攻击范围
   */
  private getAttackRange(unitType: UnitType): number {
    switch (unitType) {
      case UnitType.ARCHER:
        return 3;
      case UnitType.MAGE:
        return 2;
      default:
        return 1;
    }
  }

  /**
   * 计算距离
   */
  private getDistance(pos1: Position, pos2: Position): number {
    return Math.abs(pos1.x - pos2.x) + Math.abs(pos1.y - pos2.y);
  }

  /**
   * 检查战斗结束
   */
  private checkCombatEnd(): boolean {
    const alivePlayers = new Set(
      this.units.filter(u => u.isAlive).map(u => u.ownerId)
    );
    
    return alivePlayers.size <= 1;
  }

  /**
   * 添加战斗事件
   */
  private addEvent(event: CombatEvent): void {
    this.events.push(event);
  }

  /**
   * 获取战斗结果
   */
  private getCombatResult(): CombatResult {
    const aliveUnits = this.units.filter(u => u.isAlive);
    const winner = aliveUnits.length > 0 ? aliveUnits[0].ownerId : null;
    
    return {
      winner,
      duration: Date.now() - this.startTime,
      events: this.events,
      finalState: this.units,
      experience: this.calculateExperience()
    };
  }

  /**
   * 计算经验值奖励
   */
  private calculateExperience(): Record<string, number> {
    const experience: Record<string, number> = {};
    
    for (const unit of this.units) {
      if (!experience[unit.ownerId]) {
        experience[unit.ownerId] = 0;
      }
      
      // 存活单位获得经验
      if (unit.isAlive) {
        experience[unit.ownerId] += 10;
      }
      
      // 根据造成的伤害获得经验
      experience[unit.ownerId] += 5;
    }
    
    return experience;
  }

  /**
   * 停止战斗
   */
  public stopCombat(): void {
    this.isRunning = false;
  }

  /**
   * 获取当前战斗状态
   */
  public getCurrentState(): CombatUnit[] {
    return [...this.units];
  }
}
