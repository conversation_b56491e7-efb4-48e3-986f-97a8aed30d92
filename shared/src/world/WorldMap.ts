/**
 * 世界地图系统
 * 固定的大地图，支持多人同时在线
 */

export interface WorldTile {
  id: string;
  x: number;
  y: number;
  terrain: TerrainType;
  resources: {
    gold: number;
    population: number;
    special?: string; // 特殊资源：水晶、遗迹等
  };
  building?: Building;
  occupiedBy?: string; // 玩家ID
  army?: WorldArmy;
  adjacentTiles: string[]; // 相邻地块ID列表
}

export interface WorldArmy {
  id: string;
  playerId: string;
  playerName: string;
  cards: HeroCard[];
  strength: number;
  movementPoints: number; // 剩余移动点数
  level: number;
  experience: number;
}

export interface WorldPlayer {
  id: string;
  username: string;
  level: number;
  experience: number;
  totalGold: number;
  totalPopulation: number;
  territories: string[]; // 控制的地块ID
  armies: WorldArmy[];
  isOnline: boolean;
  lastActionTime: number;
}

export enum TerrainType {
  PLAIN = 'plain',
  FOREST = 'forest',
  MOUNTAIN = 'mountain',
  RIVER = 'river',
  DESERT = 'desert',
  SWAMP = 'swamp',
  CRYSTAL = 'crystal',
  RUINS = 'ruins'
}

// 玩家行动类型
export interface PlayerAction {
  id: string;
  type: 'move' | 'build' | 'recruit' | 'attack';
  playerId: string;
  data: any;
  priority: number; // 行动优先级，用于解决冲突
}

// 移动行动
export interface MoveAction extends PlayerAction {
  type: 'move';
  data: {
    armyId: string;
    fromTileId: string;
    toTileId: string;
    path: string[]; // 移动路径
  };
}

// 建造行动
export interface BuildAction extends PlayerAction {
  type: 'build';
  data: {
    tileId: string;
    buildingType: string;
  };
}

// 招募行动
export interface RecruitAction extends PlayerAction {
  type: 'recruit';
  data: {
    tileId: string;
    unitType: string;
    quantity: number;
  };
}

/**
 * 世界地图管理器
 * 管理固定的大地图和所有玩家状态
 */
export class WorldMapManager {
  private tiles: Map<string, WorldTile> = new Map();
  private players: Map<string, WorldPlayer> = new Map();
  private currentTurn: number = 1;
  private turnTimeLimit: number = 60; // 每回合60秒
  private turnStartTime: number = 0;

  // 同时回合制相关
  private turnPhase: 'planning' | 'execution' | 'resolution' = 'planning';
  private playerActions: Map<string, PlayerAction[]> = new Map(); // 玩家本回合的行动
  private playersReady: Set<string> = new Set(); // 已确认的玩家

  constructor() {
    this.generateWorldMap();
  }

  /**
   * 生成固定的世界地图
   */
  private generateWorldMap(): void {
    const MAP_WIDTH = 25;
    const MAP_HEIGHT = 20;
    
    console.log('🌍 生成世界地图...');

    // 生成地块
    for (let x = 0; x < MAP_WIDTH; x++) {
      for (let y = 0; y < MAP_HEIGHT; y++) {
        const tileId = `${x}_${y}`;
        
        // 根据位置决定地形（固定算法，确保每次生成相同）
        const terrain = this.getTerrainByPosition(x, y, MAP_WIDTH, MAP_HEIGHT);
        
        const tile: WorldTile = {
          id: tileId,
          x,
          y,
          terrain,
          resources: this.generateTileResources(terrain),
          adjacentTiles: this.calculateAdjacentTiles(x, y, MAP_WIDTH, MAP_HEIGHT),
        };

        this.tiles.set(tileId, tile);
      }
    }

    console.log(`✅ 世界地图生成完成: ${this.tiles.size} 个地块`);
  }

  /**
   * 根据位置确定地形（固定算法）
   */
  private getTerrainByPosition(x: number, y: number, width: number, height: number): TerrainType {
    // 使用固定的伪随机算法，确保地图每次都相同
    const seed = x * 1000 + y;
    const random = Math.sin(seed) * 10000;
    const value = random - Math.floor(random);

    // 边缘多山脉
    if (x === 0 || x === width - 1 || y === 0 || y === height - 1) {
      return value > 0.7 ? TerrainType.MOUNTAIN : TerrainType.FOREST;
    }

    // 中心区域多平原
    const centerDistance = Math.abs(x - width/2) + Math.abs(y - height/2);
    if (centerDistance < 5) {
      return value > 0.8 ? TerrainType.FOREST : TerrainType.PLAIN;
    }

    // 其他区域按概率分布
    if (value < 0.4) return TerrainType.PLAIN;
    if (value < 0.6) return TerrainType.FOREST;
    if (value < 0.75) return TerrainType.MOUNTAIN;
    if (value < 0.85) return TerrainType.RIVER;
    if (value < 0.95) return TerrainType.DESERT;
    if (value < 0.98) return TerrainType.SWAMP;
    if (value < 0.995) return TerrainType.CRYSTAL;
    return TerrainType.RUINS;
  }

  /**
   * 生成地块资源
   */
  private generateTileResources(terrain: TerrainType): WorldTile['resources'] {
    const baseResources = {
      [TerrainType.PLAIN]: { gold: 3, population: 5 },
      [TerrainType.FOREST]: { gold: 2, population: 3 },
      [TerrainType.MOUNTAIN]: { gold: 5, population: 1 },
      [TerrainType.RIVER]: { gold: 4, population: 4 },
      [TerrainType.DESERT]: { gold: 1, population: 1 },
      [TerrainType.SWAMP]: { gold: 1, population: 2 },
      [TerrainType.CRYSTAL]: { gold: 10, population: 1, special: 'crystal' },
      [TerrainType.RUINS]: { gold: 8, population: 2, special: 'ancient_knowledge' }
    };

    return baseResources[terrain];
  }

  /**
   * 计算相邻地块
   */
  private calculateAdjacentTiles(x: number, y: number, width: number, height: number): string[] {
    const adjacent: string[] = [];
    const directions = [
      [-1, 0], [1, 0], [0, -1], [0, 1], // 四方向
      [-1, -1], [-1, 1], [1, -1], [1, 1] // 八方向（可选）
    ];

    for (const [dx, dy] of directions) {
      const newX = x + dx;
      const newY = y + dy;
      
      if (newX >= 0 && newX < width && newY >= 0 && newY < height) {
        adjacent.push(`${newX}_${newY}`);
      }
    }

    return adjacent;
  }

  /**
   * 玩家加入游戏
   */
  public addPlayer(playerId: string, username: string): boolean {
    if (this.players.has(playerId)) {
      return false;
    }

    const player: WorldPlayer = {
      id: playerId,
      username,
      level: 1,
      experience: 0,
      totalGold: 100,
      totalPopulation: 50,
      territories: [],
      armies: [],
      isOnline: true,
      lastActionTime: Date.now()
    };

    // 为新玩家分配起始位置和军队
    this.assignStartingPosition(player);
    
    this.players.set(playerId, player);
    this.turnOrder.push(playerId);

    console.log(`👤 玩家 ${username} 加入世界，当前玩家数: ${this.players.size}`);
    return true;
  }

  /**
   * 分配起始位置
   */
  private assignStartingPosition(player: WorldPlayer): void {
    // 找到空闲的平原地块作为起始位置
    const availableTiles = Array.from(this.tiles.values()).filter(tile => 
      tile.terrain === TerrainType.PLAIN && 
      !tile.occupiedBy && 
      !tile.army
    );

    if (availableTiles.length === 0) {
      throw new Error('没有可用的起始位置');
    }

    // 选择一个起始位置
    const startTile = availableTiles[Math.floor(Math.random() * availableTiles.length)];
    
    // 创建起始军队
    const startingArmy: WorldArmy = {
      id: `army_${player.id}_1`,
      playerId: player.id,
      playerName: player.username,
      cards: this.generateStartingCards(),
      strength: 3,
      movementPoints: 2, // 每回合2点移动力
      level: 1,
      experience: 0
    };

    // 占领起始地块
    startTile.occupiedBy = player.id;
    startTile.army = startingArmy;
    
    player.territories.push(startTile.id);
    player.armies.push(startingArmy);

    console.log(`🏠 玩家 ${player.username} 起始位置: (${startTile.x}, ${startTile.y})`);
  }

  /**
   * 生成起始卡牌
   */
  private generateStartingCards(): HeroCard[] {
    // 简化的起始卡牌
    return [
      {
        id: 'starter_1',
        heroName: '新兵',
        cost: 1,
        rarity: 'common',
        faction: 'neutral',
        unitType: 'infantry',
        stats: { attack: 3, health: 5, armor: 1, speed: 2 },
        skills: []
      },
      {
        id: 'starter_2', 
        heroName: '弓手',
        cost: 2,
        rarity: 'common',
        faction: 'neutral',
        unitType: 'archer',
        stats: { attack: 4, health: 3, armor: 0, speed: 3 },
        skills: []
      }
    ];
  }

  /**
   * 添加玩家行动（计划阶段）
   */
  public addPlayerAction(playerId: string, action: PlayerAction): {
    success: boolean;
    message: string;
  } {
    const player = this.players.get(playerId);
    if (!player) {
      return { success: false, message: '玩家不存在' };
    }

    // 只能在计划阶段添加行动
    if (this.turnPhase !== 'planning') {
      return { success: false, message: '当前不是计划阶段' };
    }

    // 验证行动的合法性
    const validation = this.validateAction(action);
    if (!validation.valid) {
      return { success: false, message: validation.reason };
    }

    // 添加到玩家行动列表
    if (!this.playerActions.has(playerId)) {
      this.playerActions.set(playerId, []);
    }

    const actions = this.playerActions.get(playerId)!;

    // 检查是否已有相同类型的行动（某些行动类型可能有限制）
    if (action.type === 'move') {
      const existingMoveIndex = actions.findIndex(a =>
        a.type === 'move' &&
        (a.data as MoveAction['data']).armyId === (action.data as MoveAction['data']).armyId
      );

      if (existingMoveIndex !== -1) {
        // 替换现有的移动行动
        actions[existingMoveIndex] = action;
      } else {
        actions.push(action);
      }
    } else {
      actions.push(action);
    }

    return { success: true, message: '行动已添加到计划中' };
  }

  /**
   * 验证行动的合法性
   */
  private validateAction(action: PlayerAction): { valid: boolean; reason?: string } {
    const player = this.players.get(action.playerId);
    if (!player) {
      return { valid: false, reason: '玩家不存在' };
    }

    switch (action.type) {
      case 'move':
        return this.validateMoveAction(action as MoveAction);
      case 'build':
        return this.validateBuildAction(action as BuildAction);
      case 'recruit':
        return this.validateRecruitAction(action as RecruitAction);
      default:
        return { valid: false, reason: '未知行动类型' };
    }
  }

  /**
   * 验证移动行动
   */
  private validateMoveAction(action: MoveAction): { valid: boolean; reason?: string } {
    const player = this.players.get(action.playerId);
    if (!player) {
      return { valid: false, reason: '玩家不存在' };
    }

    const army = player.armies.find(a => a.id === action.data.armyId);
    if (!army) {
      return { valid: false, reason: '军队不存在' };
    }

    if (army.movementPoints <= 0) {
      return { valid: false, reason: '移动点数不足' };
    }

    const targetTile = this.tiles.get(action.data.toTileId);
    if (!targetTile) {
      return { valid: false, reason: '目标地块不存在' };
    }

    const currentTile = this.tiles.get(action.data.fromTileId);
    if (!currentTile || currentTile.army?.id !== action.data.armyId) {
      return { valid: false, reason: '军队位置不正确' };
    }

    // 检查是否相邻
    if (!currentTile.adjacentTiles.includes(action.data.toTileId)) {
      return { valid: false, reason: '只能移动到相邻地块' };
    }

    return { valid: true };
  }

  /**
   * 验证建造行动
   */
  private validateBuildAction(action: BuildAction): { valid: boolean; reason?: string } {
    // 简化实现
    return { valid: true };
  }

  /**
   * 验证招募行动
   */
  private validateRecruitAction(action: RecruitAction): { valid: boolean; reason?: string } {
    // 简化实现
    return { valid: true };
  }

  /**
   * 玩家确认回合（表示完成计划）
   */
  public playerReady(playerId: string): boolean {
    if (this.turnPhase !== 'planning') {
      return false;
    }

    this.playersReady.add(playerId);
    console.log(`✅ 玩家 ${playerId} 确认回合，已确认: ${this.playersReady.size}/${this.players.size}`);

    // 检查是否所有玩家都已确认
    if (this.playersReady.size >= this.players.size) {
      this.executeAllActions();
    }

    return true;
  }

  /**
   * 强制结束计划阶段（时间到）
   */
  public forceTurnEnd(): void {
    if (this.turnPhase === 'planning') {
      console.log('⏰ 计划时间到，强制执行所有行动');
      this.executeAllActions();
    }
  }

  /**
   * 执行所有玩家的行动
   */
  private executeAllActions(): void {
    console.log(`🎬 执行第 ${this.currentTurn} 回合的所有行动`);

    this.turnPhase = 'execution';

    // 收集所有行动并按优先级排序
    const allActions: PlayerAction[] = [];
    for (const [playerId, actions] of this.playerActions) {
      allActions.push(...actions);
    }

    // 按优先级和类型排序行动
    allActions.sort((a, b) => {
      // 优先级高的先执行
      if (a.priority !== b.priority) {
        return b.priority - a.priority;
      }

      // 相同优先级按类型排序：移动 > 建造 > 招募
      const typeOrder = { move: 3, build: 2, recruit: 1 };
      return typeOrder[b.type] - typeOrder[a.type];
    });

    // 执行所有行动
    const results: any[] = [];
    for (const action of allActions) {
      const result = this.executeAction(action);
      results.push(result);
    }

    // 进入结算阶段
    this.turnPhase = 'resolution';
    this.resolveTurn(results);
  }

  /**
   * 执行单个行动
   */
  private executeAction(action: PlayerAction): any {
    switch (action.type) {
      case 'move':
        return this.executeMoveAction(action as MoveAction);
      case 'build':
        return this.executeBuildAction(action as BuildAction);
      case 'recruit':
        return this.executeRecruitAction(action as RecruitAction);
      default:
        return { success: false, message: '未知行动类型' };
    }
  }

  /**
   * 执行移动行动
   */
  private executeMoveAction(action: MoveAction): any {
    const player = this.players.get(action.playerId);
    if (!player) {
      return { success: false, message: '玩家不存在' };
    }

    const army = player.armies.find(a => a.id === action.data.armyId);
    if (!army) {
      return { success: false, message: '军队不存在' };
    }

    const fromTile = this.tiles.get(action.data.fromTileId);
    const toTile = this.tiles.get(action.data.toTileId);

    if (!fromTile || !toTile) {
      return { success: false, message: '地块不存在' };
    }

    // 检查目标地块是否有敌军
    if (toTile.army && toTile.army.playerId !== action.playerId) {
      // 触发战斗
      return {
        success: true,
        type: 'battle',
        message: '遭遇敌军，触发战斗',
        battleData: {
          attacker: army,
          defender: toTile.army,
          battleTile: toTile
        }
      };
    }

    // 执行移动
    fromTile.army = null;
    toTile.army = army;
    toTile.occupiedBy = action.playerId;

    // 更新玩家领土
    if (!player.territories.includes(toTile.id)) {
      player.territories.push(toTile.id);
    }

    // 消耗移动点数
    army.movementPoints--;

    return {
      success: true,
      type: 'move',
      message: `${army.playerName}的军队移动成功`,
      fromTile: fromTile.id,
      toTile: toTile.id,
      armyId: army.id
    };
  }

  /**
   * 执行建造行动
   */
  private executeBuildAction(action: BuildAction): any {
    // 简化实现
    return { success: true, type: 'build', message: '建造成功' };
  }

  /**
   * 执行招募行动
   */
  private executeRecruitAction(action: RecruitAction): any {
    // 简化实现
    return { success: true, type: 'recruit', message: '招募成功' };
  }

  /**
   * 结算回合
   */
  private resolveTurn(results: any[]): void {
    console.log(`📊 结算第 ${this.currentTurn} 回合`);

    // 恢复所有军队的移动点数
    for (const player of this.players.values()) {
      player.armies.forEach(army => {
        army.movementPoints = 2; // 每回合恢复2点移动力
      });

      // 收集资源
      this.collectResources(player);
    }

    // 清理本回合数据
    this.playerActions.clear();
    this.playersReady.clear();

    // 进入下一回合
    this.currentTurn++;
    this.turnPhase = 'planning';
    this.turnStartTime = Date.now();

    console.log(`🔄 进入第 ${this.currentTurn} 回合计划阶段`);
  }

  /**
   * 收集资源
   */
  private collectResources(player: WorldPlayer): void {
    let goldGain = 0;
    let populationGain = 0;

    for (const territoryId of player.territories) {
      const tile = this.tiles.get(territoryId);
      if (tile) {
        goldGain += tile.resources.gold;
        populationGain += tile.resources.population;
      }
    }

    player.totalGold += goldGain;
    player.totalPopulation += populationGain;

    console.log(`💰 ${player.username} 收集资源: +${goldGain}金币, +${populationGain}人口`);
  }

  /**
   * 获取世界状态
   */
  public getWorldState(): {
    tiles: WorldTile[];
    players: WorldPlayer[];
    currentTurn: number;
    turnPhase: string;
    turnTimeLeft: number;
    playersReady: string[];
    totalPlayers: number;
  } {
    const turnTimeLeft = Math.max(0, this.turnTimeLimit - Math.floor((Date.now() - this.turnStartTime) / 1000));

    return {
      tiles: Array.from(this.tiles.values()),
      players: Array.from(this.players.values()),
      currentTurn: this.currentTurn,
      turnPhase: this.turnPhase,
      turnTimeLeft,
      playersReady: Array.from(this.playersReady),
      totalPlayers: this.players.size
    };
  }

  /**
   * 获取玩家的计划行动
   */
  public getPlayerActions(playerId: string): PlayerAction[] {
    return this.playerActions.get(playerId) || [];
  }

  /**
   * 移除玩家行动
   */
  public removePlayerAction(playerId: string, actionId: string): boolean {
    const actions = this.playerActions.get(playerId);
    if (!actions) return false;

    const index = actions.findIndex(a => a.id === actionId);
    if (index === -1) return false;

    actions.splice(index, 1);
    return true;
  }

  /**
   * 清除玩家所有行动
   */
  public clearPlayerActions(playerId: string): void {
    this.playerActions.set(playerId, []);
    this.playersReady.delete(playerId);
  }

  /**
   * 获取玩家可见的地图区域
   */
  public getVisibleMap(playerId: string, centerX: number, centerY: number, radius: number = 10): WorldTile[] {
    const visibleTiles: WorldTile[] = [];
    
    for (let x = centerX - radius; x <= centerX + radius; x++) {
      for (let y = centerY - radius; y <= centerY + radius; y++) {
        const tileId = `${x}_${y}`;
        const tile = this.tiles.get(tileId);
        if (tile) {
          visibleTiles.push(tile);
        }
      }
    }

    return visibleTiles;
  }
}
