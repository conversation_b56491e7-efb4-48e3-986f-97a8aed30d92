import { 
  <PERSON>Room, 
  Player, 
  GameState, 
  StrategicPhase,
  HeroCard,
  Position,
  PlayerIdentity,
  GAME_CONFIG 
} from '../types';
import { StrategicGameManager } from './StrategicGameManager';
import { AutoChessEngine, CombatResult } from '../combat/AutoChessEngine';
import { IdentitySkillManager, IdentityAssigner } from '../identity/IdentitySkills';
import { CardManager, BackpackManager } from '../cards/CardDatabase';

/**
 * 游戏主控制器
 * 统一管理SLG战略层和自走棋战斗层的游戏逻辑
 */
export class GameController {
  private room: GameRoom;
  private strategicManager: StrategicGameManager;
  private combatEngine: AutoChessEngine | null = null;
  private identityManager: IdentitySkillManager;
  private cardManager: CardManager;
  private playerBackpacks: Map<string, BackpackManager> = new Map();

  constructor(room: GameRoom) {
    this.room = room;
    this.strategicManager = new StrategicGameManager(room);
    this.identityManager = new IdentitySkillManager();
    this.cardManager = CardManager.getInstance();
    
    this.initializeGame();
  }

  /**
   * 初始化游戏
   */
  private initializeGame(): void {
    console.log('🎮 初始化游戏控制器');
    
    // 分配身份
    this.assignIdentities();
    
    // 初始化玩家背包
    this.initializePlayerBackpacks();
    
    // 分配初始卡牌
    this.distributeInitialCards();
    
    // 设置游戏状态
    this.room.gameState = GameState.STRATEGIC;
    this.room.strategicPhase = StrategicPhase.RESOURCE_COLLECTION;
    
    console.log('✅ 游戏初始化完成');
  }

  /**
   * 分配身份
   */
  private assignIdentities(): void {
    const playerIds = this.room.players.map(p => p.id);
    const identityAssignments = IdentityAssigner.assignIdentities(playerIds);
    
    for (const player of this.room.players) {
      const identity = identityAssignments.get(player.id);
      if (identity) {
        player.identity = identity;
        player.identityRevealed = identity === PlayerIdentity.EMPEROR; // 主公身份公开
        
        // 初始化身份技能
        this.identityManager.initializePlayerSkills(player.id, identity);
        player.identitySkills = this.identityManager.getPlayerSkills(player.id);
        
        console.log(`👑 ${player.username} 获得身份: ${identity}`);
      }
    }
  }

  /**
   * 初始化玩家背包
   */
  private initializePlayerBackpacks(): void {
    for (const player of this.room.players) {
      const backpack = new BackpackManager(player.id, player.backpackCapacity);
      this.playerBackpacks.set(player.id, backpack);
    }
  }

  /**
   * 分配初始卡牌
   */
  private distributeInitialCards(): void {
    for (const player of this.room.players) {
      const backpack = this.playerBackpacks.get(player.id);
      if (!backpack) continue;
      
      // 根据阵营分配初始英雄卡
      const factionCards = this.cardManager.getHeroCardsByFaction(player.faction);
      const initialCards = factionCards.slice(0, 3); // 每个玩家3张初始卡牌
      
      for (const card of initialCards) {
        if (backpack.canAddCard(card)) {
          backpack.addCard(card);
          player.cardCollection.push(card);
        }
      }
      
      console.log(`🎴 ${player.username} 获得初始卡牌: ${initialCards.map(c => c.heroName).join(', ')}`);
    }
  }

  /**
   * 开始游戏
   */
  public startGame(): void {
    console.log('🚀 游戏开始！');
    this.strategicManager.startTurn();
  }

  /**
   * 处理玩家行动
   */
  public handlePlayerAction(playerId: string, action: string, data: any): boolean {
    const player = this.room.players.find(p => p.id === playerId);
    if (!player || !player.isAlive) {
      return false;
    }

    switch (action) {
      case 'move_army':
        return this.handleMoveArmy(playerId, data);
      case 'use_identity_skill':
        return this.handleUseIdentitySkill(playerId, data);
      case 'buy_card':
        return this.handleBuyCard(playerId, data);
      case 'deploy_cards':
        return this.handleDeployCards(playerId, data);
      case 'end_turn':
        return this.handleEndTurn(playerId);
      default:
        return false;
    }
  }

  /**
   * 处理军队移动
   */
  private handleMoveArmy(playerId: string, data: { armyId: string; targetPosition: Position }): boolean {
    return this.strategicManager.moveArmy(playerId, data.armyId, data.targetPosition);
  }

  /**
   * 处理身份技能使用
   */
  private handleUseIdentitySkill(playerId: string, data: { skillId: string; target?: string }): boolean {
    const player = this.room.players.find(p => p.id === playerId);
    if (!player) return false;

    // 检查技能是否可用
    if (!this.identityManager.canUseSkill(playerId, data.skillId)) {
      return false;
    }

    // 检查资源消耗
    const skill = player.identitySkills.find(s => s.id === data.skillId);
    if (skill?.cost) {
      if (skill.cost.gold && player.resources.gold < skill.cost.gold) {
        return false;
      }
      if (skill.cost.population && player.resources.population < skill.cost.population) {
        return false;
      }
    }

    // 使用技能
    if (this.identityManager.useSkill(playerId, data.skillId)) {
      // 扣除资源
      if (skill?.cost) {
        if (skill.cost.gold) {
          player.resources.gold -= skill.cost.gold;
        }
        if (skill.cost.population) {
          player.resources.population -= skill.cost.population;
        }
      }

      console.log(`⚡ ${player.username} 使用身份技能: ${skill?.name}`);
      return true;
    }

    return false;
  }

  /**
   * 处理购买卡牌
   */
  private handleBuyCard(playerId: string, data: { cardId: string }): boolean {
    const player = this.room.players.find(p => p.id === playerId);
    const backpack = this.playerBackpacks.get(playerId);
    if (!player || !backpack) return false;

    const card = this.cardManager.getCard(data.cardId);
    if (!card) return false;

    const price = this.cardManager.getCardPrice(card);
    
    // 检查金币是否足够
    if (player.resources.gold < price) {
      return false;
    }

    // 检查背包容量
    if (!backpack.canAddCard(card)) {
      return false;
    }

    // 购买卡牌
    player.resources.gold -= price;
    backpack.addCard(card);
    player.cardCollection.push(card);

    console.log(`💰 ${player.username} 购买卡牌: ${card.name} (${price}金币)`);
    return true;
  }

  /**
   * 处理卡牌部署
   */
  private handleDeployCards(playerId: string, data: { deployments: Array<{ cardId: string; position: Position }> }): boolean {
    if (this.room.gameState !== GameState.COMBAT_DEPLOY) {
      return false;
    }

    const player = this.room.players.find(p => p.id === playerId);
    if (!player) return false;

    // 验证部署的卡牌
    const deployedCards: HeroCard[] = [];
    for (const deployment of data.deployments) {
      const card = player.cardCollection.find(c => c.id === deployment.cardId);
      if (card && card.type === 'hero') {
        deployedCards.push(card as HeroCard);
      }
    }

    // 存储部署信息，等待战斗开始
    (player as any).deployedCards = deployedCards;
    
    console.log(`⚔️ ${player.username} 完成部署: ${deployedCards.map(c => c.heroName).join(', ')}`);
    return true;
  }

  /**
   * 处理结束回合
   */
  private handleEndTurn(playerId: string): boolean {
    // 更新身份技能冷却
    this.identityManager.updateCooldowns(playerId);
    
    // 委托给战略管理器
    this.strategicManager.endTurn();
    return true;
  }

  /**
   * 触发战斗
   */
  public triggerCombat(position: Position): void {
    console.log(`⚔️ 在位置 (${position.x}, ${position.y}) 触发战斗`);
    
    // 切换到战斗初始化状态
    this.room.gameState = GameState.COMBAT_INIT;
    
    // 找到参与战斗的玩家
    const combatPlayers = this.findCombatPlayers(position);
    
    if (combatPlayers.length >= 2) {
      // 开始战斗部署阶段
      this.startCombatDeployment(combatPlayers);
    }
  }

  /**
   * 找到参与战斗的玩家
   */
  private findCombatPlayers(position: Position): Player[] {
    const tile = this.room.map.find(t => t.position.x === position.x && t.position.y === position.y);
    if (!tile || !tile.units) return [];

    const playerIds = new Set(tile.units.map(army => army.ownerId));
    return this.room.players.filter(p => playerIds.has(p.id));
  }

  /**
   * 开始战斗部署阶段
   */
  private startCombatDeployment(players: Player[]): void {
    this.room.gameState = GameState.COMBAT_DEPLOY;
    
    console.log(`🎯 开始战斗部署阶段，参与玩家: ${players.map(p => p.username).join(', ')}`);
    
    // 设置部署倒计时
    setTimeout(() => {
      this.startAutoCombat(players);
    }, GAME_CONFIG.DEPLOY_TIME_LIMIT);
  }

  /**
   * 开始自动战斗
   */
  private async startAutoCombat(players: Player[]): Promise<void> {
    this.room.gameState = GameState.COMBAT_AUTO;
    
    // 创建战斗引擎
    this.combatEngine = new AutoChessEngine();
    
    // 准备战斗单位
    const playerUnits = new Map<string, HeroCard[]>();
    for (const player of players) {
      const deployedCards = (player as any).deployedCards || [];
      playerUnits.set(player.id, deployedCards);
    }
    
    // 初始化战斗
    this.combatEngine.initializeCombat(playerUnits);
    
    console.log('⚔️ 自动战斗开始');
    
    // 开始战斗
    const result = await this.combatEngine.startAutoCombat();
    
    // 处理战斗结果
    this.handleCombatResult(result, players);
  }

  /**
   * 处理战斗结果
   */
  private handleCombatResult(result: CombatResult, players: Player[]): void {
    this.room.gameState = GameState.COMBAT_RESULT;
    
    console.log(`🏆 战斗结束，获胜者: ${result.winner || '平局'}`);
    
    // 分配经验值
    for (const [playerId, exp] of Object.entries(result.experience)) {
      const player = this.room.players.find(p => p.id === playerId);
      if (player) {
        player.resources.experience += exp;
        console.log(`📈 ${player.username} 获得 ${exp} 经验值`);
      }
    }
    
    // 检查玩家死亡
    if (result.winner) {
      for (const player of players) {
        if (player.id !== result.winner) {
          // 失败的玩家可能死亡（根据具体规则）
          // 这里简化处理
        }
      }
    }
    
    // 检查游戏结束条件
    const victoryResult = this.identityManager.checkVictoryConditions(this.room.players);
    if (victoryResult.winner) {
      this.endGame(victoryResult.winner, victoryResult.reason);
    } else {
      // 返回战略地图
      setTimeout(() => {
        this.room.gameState = GameState.STRATEGIC;
        this.room.strategicPhase = StrategicPhase.PLAYER_ACTION;
      }, 3000);
    }
  }

  /**
   * 结束游戏
   */
  private endGame(winnerId: string, reason: string): void {
    this.room.gameState = GameState.GAME_END;
    
    const winner = this.room.players.find(p => p.id === winnerId);
    console.log(`🎉 游戏结束！获胜者: ${winner?.username} - ${reason}`);
    
    // 记录游戏结果
    // 可以在这里添加统计和排行榜逻辑
  }

  /**
   * 获取游戏状态
   */
  public getGameState(): GameRoom {
    return this.room;
  }

  /**
   * 获取玩家背包状态
   */
  public getPlayerBackpack(playerId: string) {
    return this.playerBackpacks.get(playerId)?.getStatus();
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    if (this.combatEngine) {
      this.combatEngine.stopCombat();
    }
    console.log('🧹 游戏控制器已清理');
  }
}
