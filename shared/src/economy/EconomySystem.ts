import { 
  Player, 
  HexTile, 
  Building, 
  BuildingEffect, 
  TerrainType,
  Card,
  HeroCard 
} from '../types';
import { EnvironmentSystem } from '../environment/EnvironmentSystem';

/**
 * 经济系统
 * 管理资源生产、建筑建设、贸易等经济活动
 */

// 建筑类型定义
export interface BuildingType {
  id: string;
  name: string;
  description: string;
  cost: { gold: number; population: number };
  buildTime: number;
  maxLevel: number;
  effects: BuildingEffect[];
  requirements: {
    terrain?: TerrainType[];
    adjacentBuildings?: string[];
    playerLevel?: number;
  };
}

// 建筑数据库
export const BUILDING_TYPES: Record<string, BuildingType> = {
  fortress: {
    id: 'fortress',
    name: '要塞',
    description: '军事要塞，提供防御加成和兵力上限',
    cost: { gold: 100, population: 50 },
    buildTime: 3,
    maxLevel: 5,
    effects: [
      { type: 'defense_bonus', value: 20 },
      { type: 'unit_bonus', value: 2 }
    ],
    requirements: {
      terrain: [TerrainType.PLAIN, TerrainType.MOUNTAIN]
    }
  },

  tower: {
    id: 'tower',
    name: '箭塔',
    description: '防御塔楼，增强射程和攻击力',
    cost: { gold: 60, population: 20 },
    buildTime: 2,
    maxLevel: 3,
    effects: [
      { type: 'defense_bonus', value: 15 },
      { type: 'unit_bonus', value: 1 }
    ],
    requirements: {
      terrain: [TerrainType.PLAIN, TerrainType.FOREST, TerrainType.MOUNTAIN]
    }
  },

  mine: {
    id: 'mine',
    name: '金矿',
    description: '开采金矿，增加金币产出',
    cost: { gold: 80, population: 30 },
    buildTime: 4,
    maxLevel: 4,
    effects: [
      { type: 'resource_bonus', value: 5 }
    ],
    requirements: {
      terrain: [TerrainType.MOUNTAIN, TerrainType.CRYSTAL]
    }
  },

  farm: {
    id: 'farm',
    name: '农田',
    description: '农业设施，增加人口产出',
    cost: { gold: 40, population: 10 },
    buildTime: 2,
    maxLevel: 4,
    effects: [
      { type: 'resource_bonus', value: 3 }
    ],
    requirements: {
      terrain: [TerrainType.PLAIN, TerrainType.RIVER]
    }
  },

  temple: {
    id: 'temple',
    name: '神庙',
    description: '宗教建筑，提供特殊加成',
    cost: { gold: 120, population: 40 },
    buildTime: 5,
    maxLevel: 3,
    effects: [
      { type: 'resource_bonus', value: 2 },
      { type: 'unit_bonus', value: 1 }
    ],
    requirements: {
      terrain: [TerrainType.PLAIN, TerrainType.MOUNTAIN],
      playerLevel: 3
    }
  },

  market: {
    id: 'market',
    name: '市场',
    description: '贸易中心，提供金币加成和卡牌交易',
    cost: { gold: 70, population: 25 },
    buildTime: 3,
    maxLevel: 3,
    effects: [
      { type: 'resource_bonus', value: 4 }
    ],
    requirements: {
      terrain: [TerrainType.PLAIN, TerrainType.RIVER]
    }
  },

  academy: {
    id: 'academy',
    name: '学院',
    description: '研究机构，提供经验值加成',
    cost: { gold: 150, population: 60 },
    buildTime: 6,
    maxLevel: 3,
    effects: [
      { type: 'resource_bonus', value: 3 }
    ],
    requirements: {
      terrain: [TerrainType.PLAIN],
      adjacentBuildings: ['temple'],
      playerLevel: 5
    }
  }
};

// 贸易路线
export interface TradeRoute {
  id: string;
  from: string;
  to: string;
  resource: 'gold' | 'population' | 'cards';
  amount: number;
  cost: number;
  duration: number;
}

/**
 * 经济系统管理器
 */
export class EconomySystem {
  private environmentSystem: EnvironmentSystem;
  private tradeRoutes: Map<string, TradeRoute> = new Map();

  constructor(environmentSystem: EnvironmentSystem) {
    this.environmentSystem = environmentSystem;
  }

  /**
   * 计算地块资源产出
   */
  public calculateTileProduction(tile: HexTile): { gold: number; population: number; experience: number } {
    // 基础产出
    let baseProduction = { ...tile.resources };
    
    // 应用环境效果
    const environmentProduction = this.environmentSystem.calculateResourceProduction(
      tile.terrainType, 
      baseProduction
    );

    // 应用建筑加成
    let buildingBonus = { gold: 0, population: 0, experience: 0 };
    if (tile.buildings) {
      for (const building of tile.buildings) {
        const bonus = this.calculateBuildingBonus(building);
        buildingBonus.gold += bonus.gold;
        buildingBonus.population += bonus.population;
        buildingBonus.experience += bonus.experience;
      }
    }

    return {
      gold: environmentProduction.gold + buildingBonus.gold,
      population: environmentProduction.population + buildingBonus.population,
      experience: buildingBonus.experience
    };
  }

  /**
   * 计算建筑加成
   */
  private calculateBuildingBonus(building: Building): { gold: number; population: number; experience: number } {
    const buildingType = BUILDING_TYPES[building.type];
    let bonus = { gold: 0, population: 0, experience: 0 };

    for (const effect of building.effects) {
      const value = effect.value * building.level;
      
      switch (effect.type) {
        case 'resource_bonus':
          if (building.type === 'mine' || building.type === 'market') {
            bonus.gold += value;
          } else if (building.type === 'farm') {
            bonus.population += value;
          } else if (building.type === 'academy') {
            bonus.experience += value;
          }
          break;
      }
    }

    return bonus;
  }

  /**
   * 检查是否可以建造建筑
   */
  public canBuildBuilding(
    player: Player, 
    tile: HexTile, 
    buildingTypeId: string
  ): { canBuild: boolean; reason?: string } {
    const buildingType = BUILDING_TYPES[buildingTypeId];
    if (!buildingType) {
      return { canBuild: false, reason: '未知建筑类型' };
    }

    // 检查资源是否足够
    if (player.resources.gold < buildingType.cost.gold) {
      return { canBuild: false, reason: '金币不足' };
    }
    if (player.resources.population < buildingType.cost.population) {
      return { canBuild: false, reason: '人口不足' };
    }

    // 检查地形要求
    if (buildingType.requirements.terrain && 
        !buildingType.requirements.terrain.includes(tile.terrainType)) {
      return { canBuild: false, reason: '地形不适合' };
    }

    // 检查玩家等级要求
    if (buildingType.requirements.playerLevel && 
        player.level < buildingType.requirements.playerLevel) {
      return { canBuild: false, reason: '玩家等级不足' };
    }

    // 检查是否已有同类建筑
    if (tile.buildings && tile.buildings.some(b => b.type === buildingTypeId)) {
      return { canBuild: false, reason: '已有同类建筑' };
    }

    // 检查相邻建筑要求
    if (buildingType.requirements.adjacentBuildings) {
      // 这里需要检查相邻地块的建筑
      // 简化实现，假设总是满足
    }

    return { canBuild: true };
  }

  /**
   * 建造建筑
   */
  public buildBuilding(
    player: Player, 
    tile: HexTile, 
    buildingTypeId: string
  ): boolean {
    const canBuild = this.canBuildBuilding(player, tile, buildingTypeId);
    if (!canBuild.canBuild) {
      return false;
    }

    const buildingType = BUILDING_TYPES[buildingTypeId];
    
    // 扣除资源
    player.resources.gold -= buildingType.cost.gold;
    player.resources.population -= buildingType.cost.population;

    // 创建建筑
    const newBuilding: Building = {
      id: `building_${Date.now()}`,
      type: buildingTypeId as Building['type'],
      level: 1,
      effects: [...buildingType.effects]
    };

    // 添加到地块
    if (!tile.buildings) {
      tile.buildings = [];
    }
    tile.buildings.push(newBuilding);

    console.log(`🏗️ ${player.username} 在 (${tile.position.x}, ${tile.position.y}) 建造了 ${buildingType.name}`);
    return true;
  }

  /**
   * 升级建筑
   */
  public upgradeBuilding(
    player: Player, 
    building: Building
  ): boolean {
    const buildingType = BUILDING_TYPES[building.type];
    
    if (building.level >= buildingType.maxLevel) {
      return false;
    }

    const upgradeCost = {
      gold: buildingType.cost.gold * building.level,
      population: buildingType.cost.population * building.level
    };

    if (player.resources.gold < upgradeCost.gold || 
        player.resources.population < upgradeCost.population) {
      return false;
    }

    // 扣除资源
    player.resources.gold -= upgradeCost.gold;
    player.resources.population -= upgradeCost.population;

    // 升级建筑
    building.level++;

    console.log(`⬆️ ${player.username} 升级了 ${buildingType.name} 到 ${building.level} 级`);
    return true;
  }

  /**
   * 计算卡牌购买价格
   */
  public calculateCardPrice(card: Card, player: Player): number {
    let basePrice = card.cost * 10; // 基础价格

    // 稀有度加成
    const rarityMultiplier = {
      'common': 1.0,
      'rare': 1.5,
      'epic': 2.5,
      'legendary': 4.0
    };
    basePrice *= rarityMultiplier[card.rarity] || 1.0;

    // 玩家等级折扣
    const levelDiscount = Math.min(0.2, player.level * 0.02);
    basePrice *= (1 - levelDiscount);

    // 市场建筑折扣
    const marketDiscount = this.getMarketDiscount(player);
    basePrice *= (1 - marketDiscount);

    return Math.floor(basePrice);
  }

  /**
   * 获取市场折扣
   */
  private getMarketDiscount(player: Player): number {
    let discount = 0;
    
    // 检查玩家控制的地块中是否有市场
    for (const territoryId of player.territories) {
      // 这里需要从游戏状态中获取地块信息
      // 简化实现，假设有10%折扣
      discount = 0.1;
      break;
    }

    return discount;
  }

  /**
   * 创建贸易路线
   */
  public createTradeRoute(
    fromPlayerId: string, 
    toPlayerId: string, 
    resource: 'gold' | 'population' | 'cards',
    amount: number
  ): string | null {
    const routeId = `trade_${Date.now()}`;
    
    const tradeRoute: TradeRoute = {
      id: routeId,
      from: fromPlayerId,
      to: toPlayerId,
      resource,
      amount,
      cost: Math.floor(amount * 0.1), // 10%手续费
      duration: 3 // 3回合完成
    };

    this.tradeRoutes.set(routeId, tradeRoute);
    console.log(`💰 创建贸易路线: ${fromPlayerId} → ${toPlayerId} (${resource} x${amount})`);
    
    return routeId;
  }

  /**
   * 处理贸易路线
   */
  public processTradeRoutes(players: Map<string, Player>): void {
    for (const [routeId, route] of this.tradeRoutes) {
      route.duration--;
      
      if (route.duration <= 0) {
        // 完成贸易
        const fromPlayer = players.get(route.from);
        const toPlayer = players.get(route.to);
        
        if (fromPlayer && toPlayer) {
          this.executeTradeRoute(fromPlayer, toPlayer, route);
        }
        
        this.tradeRoutes.delete(routeId);
      }
    }
  }

  /**
   * 执行贸易路线
   */
  private executeTradeRoute(fromPlayer: Player, toPlayer: Player, route: TradeRoute): void {
    switch (route.resource) {
      case 'gold':
        if (fromPlayer.resources.gold >= route.amount + route.cost) {
          fromPlayer.resources.gold -= (route.amount + route.cost);
          toPlayer.resources.gold += route.amount;
        }
        break;
      case 'population':
        if (fromPlayer.resources.population >= route.amount + route.cost) {
          fromPlayer.resources.population -= (route.amount + route.cost);
          toPlayer.resources.population += route.amount;
        }
        break;
      case 'cards':
        // 卡牌交易逻辑
        if (fromPlayer.cardCollection.length >= route.amount) {
          const tradedCards = fromPlayer.cardCollection.splice(0, route.amount);
          toPlayer.cardCollection.push(...tradedCards);
          fromPlayer.resources.gold -= route.cost;
        }
        break;
    }

    console.log(`✅ 贸易完成: ${fromPlayer.username} → ${toPlayer.username}`);
  }

  /**
   * 计算玩家总资产
   */
  public calculatePlayerWealth(player: Player): number {
    let wealth = 0;
    
    // 资源价值
    wealth += player.resources.gold;
    wealth += player.resources.population * 0.5;
    wealth += player.resources.experience * 2;

    // 领土价值
    wealth += player.territories.length * 50;

    // 卡牌价值
    for (const card of player.cardCollection) {
      wealth += this.calculateCardPrice(card, player) * 0.8; // 二手价格
    }

    // 军队价值
    for (const army of player.armies) {
      wealth += army.cards.length * 30;
    }

    return Math.floor(wealth);
  }

  /**
   * 获取经济报告
   */
  public getEconomyReport(player: Player): {
    totalWealth: number;
    incomePerTurn: number;
    expenses: number;
    netIncome: number;
    recommendations: string[];
  } {
    const totalWealth = this.calculatePlayerWealth(player);
    
    // 计算每回合收入（简化）
    const incomePerTurn = player.territories.length * 3;
    
    // 计算支出（军队维护费等）
    const expenses = player.armies.length * 2;
    
    const netIncome = incomePerTurn - expenses;
    
    // 生成建议
    const recommendations: string[] = [];
    if (netIncome < 0) {
      recommendations.push('收入不足，考虑扩张领土或减少军队');
    }
    if (player.territories.length < 3) {
      recommendations.push('领土太少，需要积极扩张');
    }
    if (player.resources.gold > 200) {
      recommendations.push('金币充足，可以考虑建造建筑或购买卡牌');
    }

    return {
      totalWealth,
      incomePerTurn,
      expenses,
      netIncome,
      recommendations
    };
  }
}
