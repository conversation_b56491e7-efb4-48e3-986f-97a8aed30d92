// ==================== 用户认证系统 ====================
export interface User {
  id: string;
  username: string;
  isOnline: boolean;
  currentRoomId?: string;
  level?: number;
  experience?: number;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  password: string;
}

export interface AuthResponse {
  success: boolean;
  user?: User;
  token?: string;
  message?: string;
}

// ==================== 游戏核心状态 ====================

// 游戏主状态（嵌套状态机）
export enum GameState {
  LOBBY = 'lobby',                    // 大厅等待
  STRATEGIC = 'strategic',            // 战略地图模式
  COMBAT_INIT = 'combat_init',        // 战斗初始化
  COMBAT_DEPLOY = 'combat_deploy',    // 战斗部署阶段
  COMBAT_AUTO = 'combat_auto',        // 自动战斗阶段
  COMBAT_RESULT = 'combat_result',    // 战斗结算
  GAME_END = 'game_end'              // 游戏结束
}

// 战略回合状态
export enum StrategicPhase {
  RESOURCE_COLLECTION = 'resource_collection',  // 资源收集
  PLAYER_ACTION = 'player_action',              // 玩家行动
  EVENT_PROCESSING = 'event_processing',        // 事件处理
  TURN_END = 'turn_end'                        // 回合结束
}

// ==================== 身份系统 ====================

// 玩家身份
export enum PlayerIdentity {
  EMPEROR = 'emperor',      // 主公
  LOYALIST = 'loyalist',    // 忠臣
  REBEL = 'rebel',          // 反贼
  TRAITOR = 'traitor'       // 内奸
}

// 身份技能
export interface IdentitySkill {
  id: string;
  name: string;
  type: 'active' | 'passive' | 'locked';
  description: string;
  cooldown?: number;
  cost?: { gold?: number; population?: number };
}

// ==================== 阵营系统 ====================

// 阵营（剧本相关）
export enum Faction {
  SHU = 'shu',      // 蜀
  WEI = 'wei',      // 魏
  WU = 'wu',        // 吴
  QUN = 'qun',      // 群
  JIN = 'jin',      // 晋
  HAN = 'han'       // 汉
}

// 五行属性
export enum Element {
  METAL = 'metal',    // 金
  WOOD = 'wood',      // 木
  WATER = 'water',    // 水
  FIRE = 'fire',      // 火
  EARTH = 'earth',    // 土
  THUNDER = 'thunder' // 雷
}

// ==================== 兵种系统 ====================

// 兵种类型
export enum UnitType {
  INFANTRY = 'infantry',    // 步兵 (Shield 盾)
  CAVALRY = 'cavalry',      // 骑兵 (Sword 剑)
  ARCHER = 'archer',        // 弓兵 (Bow 弓)
  CHARIOT = 'chariot',      // 战车 (Spear 枪)
  MAGE = 'mage'            // 法师 (Staff 杖)
}

// 兵种相克关系
export const UNIT_COUNTER_MAP: Record<UnitType, UnitType[]> = {
  [UnitType.INFANTRY]: [UnitType.CAVALRY, UnitType.CHARIOT, UnitType.MAGE],
  [UnitType.CAVALRY]: [UnitType.ARCHER, UnitType.MAGE],
  [UnitType.ARCHER]: [UnitType.INFANTRY, UnitType.CHARIOT, UnitType.MAGE],
  [UnitType.CHARIOT]: [UnitType.CAVALRY, UnitType.MAGE],
  [UnitType.MAGE]: [] // 法师被所有兵种克制，但也克制所有兵种
};

// 单位定位
export enum UnitRole {
  TANK = 'tank',        // 坦克
  WARRIOR = 'warrior',  // 战士
  MAGE = 'mage',       // 法师
  CONTROL = 'control',  // 控制
  ARCHER = 'archer',    // 射手
  SUPPORT = 'support'   // 辅助
}

// ==================== 卡牌系统 ====================

// 卡牌类型
export enum CardType {
  HERO = 'hero',              // 英雄卡
  SPELL = 'spell',            // 法术卡
  STRATEGIC = 'strategic'     // 战略指令卡
}

// 卡牌稀有度
export enum CardRarity {
  COMMON = 'common',      // 普通 (黄)
  RARE = 'rare',         // 精良 (玄)
  EPIC = 'epic',         // 传奇 (地)
  LEGENDARY = 'legendary' // 史诗 (天)
}

// 基础卡牌接口
export interface BaseCard {
  id: string;
  name: string;
  type: CardType;
  rarity: CardRarity;
  cost: number;           // 背包占用值
  description: string;
  imageUrl?: string;
}

// 英雄卡属性
export interface HeroStats {
  health: number;         // 生命值（兵力）
  energy: number;         // 能量
  attack: number;         // 攻击力
  armor: number;          // 护甲
  strategy: number;       // 谋略
  magicResist: number;    // 魔抗
  speed: number;          // 速度
}

// 技能类型
export enum SkillType {
  ACTIVE = 'active',      // 主动技
  PASSIVE = 'passive'     // 被动技
}

// 技能接口
export interface Skill {
  id: string;
  name: string;
  type: SkillType;
  description: string;
  cooldown?: number;
  energyCost?: number;
  effects: SkillEffect[];
}

// 技能效果
export interface SkillEffect {
  type: 'damage' | 'heal' | 'buff' | 'debuff' | 'control' | 'summon' | 'terrain';
  value?: number;
  duration?: number;
  target: 'self' | 'ally' | 'enemy' | 'all' | 'area' | 'archer' | 'mage' | 'infantry' | 'cavalry';
  range?: number;
}

// 英雄卡
export interface HeroCard extends BaseCard {
  type: CardType.HERO;
  heroName: string;       // 武将名称
  faction: Faction;       // 阵营
  unitType: UnitType;     // 兵种类型
  element: Element;       // 五行属性
  role: UnitRole;         // 定位
  stats: HeroStats;       // 属性
  skill: Skill;          // 技能（每个英雄只有一个技能）
  synergies: string[];    // 羁绊标签
}

// 法术系统
export enum SpellSchool {
  MILITARY = 'military',    // 兵家权谋
  MYSTIC = 'mystic',       // 玄门道法
  MECHANISM = 'mechanism'   // 墨家机关
}

// 法术卡
export interface SpellCard extends BaseCard {
  type: CardType.SPELL;
  school: SpellSchool;      // 术法类型
  energyCost: number;       // 能量消耗
  effects: SkillEffect[];   // 法术效果
  targetType: 'unit' | 'area' | 'global' | 'terrain';
}

// 战略指令卡
export interface StrategicCard extends BaseCard {
  type: CardType.STRATEGIC;
  combatEffect: SkillEffect[];    // 自走棋战斗效果（微观）
  globalEffect: GlobalEffect;     // SLG全局效果（宏观）
  duration?: number;              // 持续回合数
}

// 全局效果
export interface GlobalEffect {
  type: 'terrain_change' | 'resource_bonus' | 'movement_block' | 'weather_control' | 'building_create';
  description: string;
  value?: number;
  area?: Position[];
  duration: number;
}

// 卡牌联合类型
export type Card = HeroCard | SpellCard | StrategicCard;

// ==================== 地形与天气系统 ====================

// 地形类型
export enum TerrainType {
  PLAIN = 'plain',        // 平原
  MOUNTAIN = 'mountain',  // 山脉
  FOREST = 'forest',      // 森林
  RIVER = 'river',        // 河流
  DESERT = 'desert',      // 沙漠
  SWAMP = 'swamp',        // 沼泽
  CRYSTAL = 'crystal',    // 水晶矿脉
  SCORCHED = 'scorched'   // 焦土
}

// 天气类型
export enum WeatherType {
  CLEAR = 'clear',        // 晴天
  RAIN = 'rain',          // 暴雨
  SANDSTORM = 'sandstorm', // 沙暴
  AURORA = 'aurora',      // 极光
  WINTER = 'winter',      // 严冬
  THUNDER = 'thunder'     // 雷暴
}

// 位置坐标
export interface Position {
  x: number;
  y: number;
}

// ==================== 地图系统 ====================

// 六边形地块
export interface HexTile {
  id: string;
  position: Position;     // 六边形坐标
  terrainType: TerrainType;
  ownerId?: string;       // 控制者ID
  resources: {
    gold: number;         // 金币产出
    population: number;   // 人口产出
  };
  buildings?: Building[];
  units?: Army[];         // 驻扎的军队
}

// 建筑
export interface Building {
  id: string;
  type: 'fortress' | 'tower' | 'mine' | 'farm' | 'temple' | 'market' | 'academy';
  level: number;
  effects: BuildingEffect[];
}

// 建筑效果
export interface BuildingEffect {
  type: 'resource_bonus' | 'defense_bonus' | 'unit_bonus';
  value: number;
}

// 军队
export interface Army {
  id: string;
  ownerId: string;
  name: string;
  position: Position;
  cards: Card[];          // 携带的卡牌
  fatigue: number;        // 疲劳度
  morale: number;         // 士气
}

// ==================== 玩家系统 ====================

// 战斗状态
export enum BattleStance {
  ATTACK = 'attack',      // 攻击
  DEFEND = 'defend',      // 防守
  SUPPORT = 'support',    // 支援
  IGNORE = 'ignore'       // 无视
}

// 玩家状态
export interface Player {
  id: string;
  username: string;
  identity: PlayerIdentity;
  identityRevealed: boolean;
  faction: Faction;
  level: number;

  // 资源
  resources: {
    gold: number;
    population: number;
    experience: number;
  };

  // 领地和军队
  territories: string[];   // 控制的地块ID
  armies: Army[];         // 军队列表

  // 卡牌
  cardCollection: Card[]; // 所有拥有的卡牌
  backpackCapacity: number; // 背包容量

  // 状态
  isReady: boolean;
  isAlive: boolean;
  battleStance: BattleStance;

  // 技能
  identitySkills: IdentitySkill[];
  skillCooldowns: Record<string, number>;
}

// ==================== 剧本系统 ====================

// 剧本类型
export interface Scenario {
  id: string;
  name: string;
  description: string;
  mapSize: { width: number; height: number };
  maxTurns: number;
  specialRules: ScenarioRule[];
  initialResources: { gold: number; population: number };
  availableFactions: Faction[];
  weatherEvents: WeatherEvent[];
}

// 剧本规则
export interface ScenarioRule {
  id: string;
  name: string;
  description: string;
  type: 'resource_bonus' | 'unit_bonus' | 'terrain_effect' | 'victory_condition';
  conditions: any;
  effects: any;
}

// 天气事件
export interface WeatherEvent {
  turn: number;
  weather: WeatherType;
  duration: number;
  affectedAreas?: Position[];
}

// ==================== 游戏房间 ====================

// 房间状态
export interface GameRoom {
  id: string;
  name: string;
  hostId: string;
  scenario: Scenario;
  players: Player[];
  maxPlayers: number;
  gameState: GameState;
  strategicPhase: StrategicPhase;
  currentTurn: number;
  currentPlayer: string;
  map: HexTile[];
  weather: {
    current: WeatherType;
    forecast: WeatherType[];
  };
  createdAt: Date;
  startedAt?: Date;
}

// 房间操作
export interface CreateRoomRequest {
  name: string;
  scenarioId: string;
  maxPlayers?: number;
  isPrivate?: boolean;
}

export interface JoinRoomRequest {
  roomId: string;
  password?: string;
}

// ==================== 消息系统 ====================

// 游戏消息类型
export enum MessageType {
  // 认证相关
  LOGIN = 'login',
  REGISTER = 'register',
  LOGOUT = 'logout',

  // 房间相关
  CREATE_ROOM = 'create_room',
  JOIN_ROOM = 'join_room',
  LEAVE_ROOM = 'leave_room',
  ROOM_LIST = 'room_list',

  // 游戏状态
  PLAYER_READY = 'player_ready',
  START_GAME = 'start_game',
  GAME_UPDATE = 'game_update',
  TURN_START = 'turn_start',
  TURN_END = 'turn_end',

  // 战略层
  MOVE_ARMY = 'move_army',
  BUILD_STRUCTURE = 'build_structure',
  RECRUIT_UNITS = 'recruit_units',
  USE_IDENTITY_SKILL = 'use_identity_skill',

  // 战斗层
  COMBAT_START = 'combat_start',
  DEPLOY_CARDS = 'deploy_cards',
  COMBAT_AUTO = 'combat_auto',
  COMBAT_END = 'combat_end',

  // 卡牌相关
  BUY_CARD = 'buy_card',
  SELL_CARD = 'sell_card',
  UPGRADE_CARD = 'upgrade_card',

  // 匹配相关
  FIND_MATCH = 'find_match',
  CANCEL_MATCH = 'cancel_match',
  MATCH_FOUND = 'match_found'
}

// ==================== 游戏配置 ====================

export const GAME_CONFIG = {
  // 房间设置
  MAX_PLAYERS_PER_ROOM: 8,
  MIN_PLAYERS_TO_START: 8,

  // 游戏时长
  MAX_TURNS: 120,
  TURN_TIME_LIMIT: 60000, // 60秒每回合

  // 地图设置
  MAP_WIDTH: 16,
  MAP_HEIGHT: 8,

  // 战斗设置
  COMBAT_BOARD_WIDTH: 16,
  COMBAT_BOARD_HEIGHT: 8,
  DEPLOY_TIME_LIMIT: 60000, // 60秒部署时间

  // 资源设置
  INITIAL_GOLD: 50,
  INITIAL_POPULATION: 100,
  INITIAL_BACKPACK_CAPACITY: 30,

  // 卡牌设置
  CARD_COST_MULTIPLIER: {
    [CardRarity.COMMON]: 1,
    [CardRarity.RARE]: 1.5,
    [CardRarity.EPIC]: 2,
    [CardRarity.LEGENDARY]: 3
  },

  // 匹配设置
  MATCH_TIMEOUT: 30000, // 30秒匹配超时
} as const;
