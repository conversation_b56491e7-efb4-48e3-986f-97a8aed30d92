// 用户相关类型
export interface User {
  id: string;
  username: string;
  isOnline: boolean;
  currentRoomId?: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  password: string;
}

export interface AuthResponse {
  success: boolean;
  user?: User;
  token?: string;
  message?: string;
}

// 房间相关类型
export interface Room {
  id: string;
  name: string;
  hostId: string;
  players: Player[];
  maxPlayers: number;
  isPrivate: boolean;
  gameState: GameState;
  createdAt: Date;
}

export interface Player {
  id: string;
  username: string;
  isReady: boolean;
  position?: Position;
  score?: number;
}

export interface Position {
  x: number;
  y: number;
}

// 游戏状态
export enum GameState {
  WAITING = 'waiting',
  STARTING = 'starting',
  PLAYING = 'playing',
  FINISHED = 'finished'
}

// 房间操作
export interface CreateRoomRequest {
  name: string;
  maxPlayers?: number;
  isPrivate?: boolean;
}

export interface JoinRoomRequest {
  roomId: string;
  password?: string;
}

// 游戏消息类型
export enum MessageType {
  // 认证相关
  LOGIN = 'login',
  REGISTER = 'register',
  LOGOUT = 'logout',
  
  // 房间相关
  CREATE_ROOM = 'create_room',
  JOIN_ROOM = 'join_room',
  LEAVE_ROOM = 'leave_room',
  ROOM_LIST = 'room_list',
  
  // 游戏相关
  PLAYER_READY = 'player_ready',
  START_GAME = 'start_game',
  GAME_UPDATE = 'game_update',
  PLAYER_MOVE = 'player_move',
  
  // 匹配相关
  FIND_MATCH = 'find_match',
  CANCEL_MATCH = 'cancel_match',
  MATCH_FOUND = 'match_found'
}

// 游戏配置
export const GAME_CONFIG = {
  MAX_PLAYERS_PER_ROOM: 8,
  MIN_PLAYERS_TO_START: 8,
  GAME_WIDTH: 1024,
  GAME_HEIGHT: 768,
  MATCH_TIMEOUT: 30000, // 30秒匹配超时
} as const;
