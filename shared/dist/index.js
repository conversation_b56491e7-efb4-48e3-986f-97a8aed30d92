export * from './types';
// 导出常用的工具函数
export const generateId = () => {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
};
export const validateUsername = (username) => {
    return username.length >= 3 && username.length <= 20 && /^[a-zA-Z0-9_]+$/.test(username);
};
export const validatePassword = (password) => {
    return password.length >= 6;
};
//# sourceMappingURL=index.js.map