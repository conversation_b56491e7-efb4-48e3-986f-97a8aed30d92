"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validatePassword = exports.validateUsername = exports.generateId = void 0;
// 导出所有类型定义
__exportStar(require("./types"), exports);
// 导出游戏系统
__exportStar(require("./game/GameController"), exports);
__exportStar(require("./game/StrategicGameManager"), exports);
// 导出地图系统
__exportStar(require("./map/HexMap"), exports);
// 导出战斗系统
__exportStar(require("./combat/AutoChessEngine"), exports);
// 导出身份系统
__exportStar(require("./identity/IdentitySkills"), exports);
// 导出阵营系统
__exportStar(require("./faction/FactionSystem"), exports);
// 导出卡牌系统
__exportStar(require("./cards/CardDatabase"), exports);
// 导出剧本系统
__exportStar(require("./scenario/ScenarioManager"), exports);
// 导出环境系统
__exportStar(require("./environment/EnvironmentSystem"), exports);
// 导出经济系统
__exportStar(require("./economy/EconomySystem"), exports);
// 导出常用的工具函数
const generateId = () => {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
};
exports.generateId = generateId;
const validateUsername = (username) => {
    return username.length >= 3 && username.length <= 20 && /^[a-zA-Z0-9_]+$/.test(username);
};
exports.validateUsername = validateUsername;
const validatePassword = (password) => {
    return password.length >= 6;
};
exports.validatePassword = validatePassword;
//# sourceMappingURL=index.js.map