import { HeroCard, SpellCard, StrategicCard, CardType, CardRarity, Faction } from '../types';
/**
 * 卡牌数据库
 * 包含所有游戏中的卡牌定义
 */
export declare const HERO_CARDS: HeroCard[];
export declare const SPELL_CARDS: SpellCard[];
export declare const STRATEGIC_CARDS: StrategicCard[];
/**
 * 卡牌管理器
 */
export declare class CardManager {
    private static instance;
    private heroCards;
    private spellCards;
    private strategicCards;
    private constructor();
    static getInstance(): CardManager;
    /**
     * 初始化卡牌数据
     */
    private initializeCards;
    /**
     * 获取卡牌
     */
    getCard(cardId: string): HeroCard | SpellCard | StrategicCard | null;
    /**
     * 获取所有英雄卡
     */
    getAllHeroCards(): HeroCard[];
    /**
     * 根据阵营获取英雄卡
     */
    getHeroCardsByFaction(faction: Faction): HeroCard[];
    /**
     * 根据稀有度获取卡牌
     */
    getCardsByRarity(rarity: CardRarity): (HeroCard | SpellCard | StrategicCard)[];
    /**
     * 计算卡牌购买价格
     */
    getCardPrice(card: HeroCard | SpellCard | StrategicCard): number;
    /**
     * 生成随机卡牌池
     */
    generateCardPool(count?: number): (HeroCard | SpellCard | StrategicCard)[];
    /**
     * 根据权重获取随机稀有度
     */
    private getRandomRarity;
    /**
     * 验证卡牌组合法性
     */
    validateDeck(cards: (HeroCard | SpellCard | StrategicCard)[]): {
        valid: boolean;
        errors: string[];
    };
}
/**
 * 背包管理系统
 */
export declare class BackpackManager {
    private playerId;
    private cards;
    private capacity;
    constructor(playerId: string, capacity?: number);
    /**
     * 添加卡牌到背包
     */
    addCard(card: HeroCard | SpellCard | StrategicCard): boolean;
    /**
     * 从背包移除卡牌
     */
    removeCard(cardId: string): boolean;
    /**
     * 获取当前背包占用
     */
    getCurrentCost(): number;
    /**
     * 获取剩余容量
     */
    getRemainingCapacity(): number;
    /**
     * 获取所有卡牌
     */
    getAllCards(): (HeroCard | SpellCard | StrategicCard)[];
    /**
     * 按类型获取卡牌
     */
    getCardsByType(type: CardType): (HeroCard | SpellCard | StrategicCard)[];
    /**
     * 升级背包容量
     */
    upgradeCapacity(additionalCapacity: number): void;
    /**
     * 检查是否可以添加卡牌
     */
    canAddCard(card: HeroCard | SpellCard | StrategicCard): boolean;
    /**
     * 获取背包状态
     */
    getStatus(): {
        playerId: string;
        cards: (HeroCard | SpellCard | StrategicCard)[];
        currentCost: number;
        capacity: number;
        remainingCapacity: number;
    };
}
//# sourceMappingURL=CardDatabase.d.ts.map