"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HexMap = void 0;
const types_1 = require("../types");
/**
 * 六边形地图系统
 * 实现六边形网格的坐标转换、邻居查找、路径计算等功能
 */
class HexMap {
    constructor(width = types_1.GAME_CONFIG.MAP_WIDTH, height = types_1.GAME_CONFIG.MAP_HEIGHT) {
        this.tiles = new Map();
        this.width = width;
        this.height = height;
        this.generateMap();
    }
    /**
     * 生成六边形地图
     */
    generateMap() {
        for (let q = 0; q < this.width; q++) {
            for (let r = 0; r < this.height; r++) {
                const position = { x: q, y: r };
                const tile = {
                    id: this.getHexId(position),
                    position,
                    terrainType: this.generateTerrain(position),
                    resources: this.generateResources(position),
                    buildings: [],
                    units: []
                };
                this.tiles.set(tile.id, tile);
            }
        }
    }
    /**
     * 生成地形类型（基于位置的程序化生成）
     */
    generateTerrain(position) {
        const { x, y } = position;
        const centerX = this.width / 2;
        const centerY = this.height / 2;
        const distanceFromCenter = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
        // 使用简单的噪声函数生成地形
        const noise = this.simpleNoise(x * 0.1, y * 0.1);
        // 边缘更可能是山脉
        if (distanceFromCenter > Math.min(this.width, this.height) * 0.4) {
            if (noise > 0.3)
                return types_1.TerrainType.MOUNTAIN;
            if (noise > 0.1)
                return types_1.TerrainType.FOREST;
        }
        // 中心区域更多平原和资源
        if (distanceFromCenter < Math.min(this.width, this.height) * 0.2) {
            if (noise > 0.4)
                return types_1.TerrainType.CRYSTAL;
            return types_1.TerrainType.PLAIN;
        }
        // 根据噪声值分配地形
        if (noise > 0.6)
            return types_1.TerrainType.MOUNTAIN;
        if (noise > 0.3)
            return types_1.TerrainType.FOREST;
        if (noise > 0.0)
            return types_1.TerrainType.PLAIN;
        if (noise > -0.3)
            return types_1.TerrainType.RIVER;
        return types_1.TerrainType.DESERT;
    }
    /**
     * 简单噪声函数
     */
    simpleNoise(x, y) {
        return Math.sin(x * 12.9898 + y * 78.233) * 43758.5453 % 1;
    }
    /**
     * 生成资源产出
     */
    generateResources(position) {
        const terrain = this.generateTerrain(position);
        switch (terrain) {
            case types_1.TerrainType.PLAIN:
                return { gold: 2, population: 3 };
            case types_1.TerrainType.FOREST:
                return { gold: 1, population: 2 };
            case types_1.TerrainType.MOUNTAIN:
                return { gold: 3, population: 1 };
            case types_1.TerrainType.RIVER:
                return { gold: 2, population: 2 };
            case types_1.TerrainType.CRYSTAL:
                return { gold: 5, population: 1 };
            case types_1.TerrainType.DESERT:
                return { gold: 1, population: 1 };
            default:
                return { gold: 1, population: 1 };
        }
    }
    /**
     * 获取六边形ID
     */
    getHexId(position) {
        return `hex_${position.x}_${position.y}`;
    }
    /**
     * 获取地块
     */
    getTile(position) {
        return this.tiles.get(this.getHexId(position));
    }
    /**
     * 获取所有地块
     */
    getAllTiles() {
        return Array.from(this.tiles.values());
    }
    /**
     * 获取六边形的邻居坐标
     */
    getNeighbors(position) {
        const { x, y } = position;
        const neighbors = [];
        // 六边形的6个邻居方向
        const directions = [
            { x: 1, y: 0 }, // 右
            { x: 1, y: -1 }, // 右上
            { x: 0, y: -1 }, // 左上
            { x: -1, y: 0 }, // 左
            { x: -1, y: 1 }, // 左下
            { x: 0, y: 1 } // 右下
        ];
        for (const dir of directions) {
            const newX = x + dir.x;
            const newY = y + dir.y;
            // 检查边界
            if (newX >= 0 && newX < this.width && newY >= 0 && newY < this.height) {
                neighbors.push({ x: newX, y: newY });
            }
        }
        return neighbors;
    }
    /**
     * 计算两个六边形之间的距离
     */
    getDistance(pos1, pos2) {
        const dx = pos1.x - pos2.x;
        const dy = pos1.y - pos2.y;
        return Math.max(Math.abs(dx), Math.abs(dy), Math.abs(dx + dy));
    }
    /**
     * 检查位置是否相邻
     */
    areAdjacent(pos1, pos2) {
        return this.getDistance(pos1, pos2) === 1;
    }
    /**
     * 获取从起点到终点的路径
     */
    getPath(start, end) {
        // 简单的A*路径查找实现
        const openSet = [start];
        const cameFrom = new Map();
        const gScore = new Map();
        const fScore = new Map();
        gScore.set(this.getHexId(start), 0);
        fScore.set(this.getHexId(start), this.getDistance(start, end));
        while (openSet.length > 0) {
            // 找到fScore最小的节点
            let current = openSet[0];
            let currentIndex = 0;
            for (let i = 1; i < openSet.length; i++) {
                const currentF = fScore.get(this.getHexId(openSet[i])) || Infinity;
                const bestF = fScore.get(this.getHexId(current)) || Infinity;
                if (currentF < bestF) {
                    current = openSet[i];
                    currentIndex = i;
                }
            }
            // 到达目标
            if (current.x === end.x && current.y === end.y) {
                const path = [];
                let temp = current;
                while (temp) {
                    path.unshift(temp);
                    temp = cameFrom.get(this.getHexId(temp));
                }
                return path;
            }
            // 从开放集中移除当前节点
            openSet.splice(currentIndex, 1);
            // 检查邻居
            const neighbors = this.getNeighbors(current);
            for (const neighbor of neighbors) {
                const tile = this.getTile(neighbor);
                if (!tile || tile.terrainType === types_1.TerrainType.MOUNTAIN) {
                    continue; // 跳过不可通行的地形
                }
                const tentativeGScore = (gScore.get(this.getHexId(current)) || 0) + 1;
                const neighborId = this.getHexId(neighbor);
                if (tentativeGScore < (gScore.get(neighborId) || Infinity)) {
                    cameFrom.set(neighborId, current);
                    gScore.set(neighborId, tentativeGScore);
                    fScore.set(neighborId, tentativeGScore + this.getDistance(neighbor, end));
                    if (!openSet.some(pos => pos.x === neighbor.x && pos.y === neighbor.y)) {
                        openSet.push(neighbor);
                    }
                }
            }
        }
        return []; // 没有找到路径
    }
    /**
     * 更新地块
     */
    updateTile(position, updates) {
        const tile = this.getTile(position);
        if (tile) {
            Object.assign(tile, updates);
            this.tiles.set(tile.id, tile);
        }
    }
    /**
     * 获取地图尺寸
     */
    getSize() {
        return { width: this.width, height: this.height };
    }
}
exports.HexMap = HexMap;
//# sourceMappingURL=HexMap.js.map