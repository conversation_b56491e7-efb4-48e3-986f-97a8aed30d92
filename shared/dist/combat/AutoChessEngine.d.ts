import { HeroCard, Position } from '../types';
/**
 * 战斗单位状态
 */
export interface CombatUnit {
    id: string;
    heroCard: HeroCard;
    position: Position;
    currentHealth: number;
    currentEnergy: number;
    buffs: CombatBuff[];
    debuffs: CombatBuff[];
    isAlive: boolean;
    ownerId: string;
    target?: CombatUnit;
    lastActionTime: number;
}
/**
 * 战斗增益/减益效果
 */
export interface CombatBuff {
    id: string;
    type: 'attack' | 'defense' | 'speed' | 'heal' | 'energy';
    value: number;
    duration: number;
    source: string;
}
/**
 * 战斗事件
 */
export interface CombatEvent {
    type: 'damage' | 'heal' | 'skill' | 'death' | 'move';
    timestamp: number;
    source?: CombatUnit;
    target?: CombatUnit;
    value?: number;
    description: string;
}
/**
 * 战斗结果
 */
export interface CombatResult {
    winner: string | null;
    duration: number;
    events: CombatEvent[];
    finalState: CombatUnit[];
    experience: Record<string, number>;
}
/**
 * 自走棋战斗引擎
 */
export declare class AutoChessEngine {
    private units;
    private events;
    private startTime;
    private isRunning;
    private boardWidth;
    private boardHeight;
    constructor();
    /**
     * 初始化战斗
     */
    initializeCombat(playerUnits: Map<string, HeroCard[]>): void;
    /**
     * 获取部署位置
     */
    private getDeploymentPosition;
    /**
     * 应用羁绊效果
     */
    private applySynergies;
    /**
     * 应用羁绊效果到单位
     */
    private applySynergiesToUnit;
    /**
     * 获取增益类型
     */
    private getBuffType;
    /**
     * 开始自动战斗
     */
    startAutoCombat(): Promise<CombatResult>;
    /**
     * 更新所有单位
     */
    private updateUnits;
    /**
     * 更新增益/减益效果
     */
    private updateBuffs;
    /**
     * 更新单位AI
     */
    private updateUnitAI;
    /**
     * 寻找攻击目标
     */
    private findTarget;
    /**
     * 执行攻击
     */
    private performAttack;
    /**
     * 尝试释放技能
     */
    private tryUseSkill;
    /**
     * 应用技能效果
     */
    private applySkillEffect;
    /**
     * 获取技能目标
     */
    private getSkillTargets;
    /**
     * 移动向目标
     */
    private moveTowardsTarget;
    /**
     * 获取兵种相克倍数
     */
    private getCounterMultiplier;
    /**
     * 获取攻击范围
     */
    private getAttackRange;
    /**
     * 计算距离
     */
    private getDistance;
    /**
     * 检查战斗结束
     */
    private checkCombatEnd;
    /**
     * 添加战斗事件
     */
    private addEvent;
    /**
     * 获取战斗结果
     */
    private getCombatResult;
    /**
     * 计算经验值奖励
     */
    private calculateExperience;
    /**
     * 停止战斗
     */
    stopCombat(): void;
    /**
     * 获取当前战斗状态
     */
    getCurrentState(): CombatUnit[];
}
//# sourceMappingURL=AutoChessEngine.d.ts.map