{"version": 3, "file": "IdentitySkills.js", "sourceRoot": "", "sources": ["../../src/identity/IdentitySkills.ts"], "names": [], "mappings": ";;;AAqFA,8CAaC;AAlGD,oCAAsE;AAEtE;;;GAGG;AAEH,OAAO;AACM,QAAA,cAAc,GAAoB;IAC7C;QACE,EAAE,EAAE,kBAAkB;QACtB,IAAI,EAAE,IAAI;QACV,IAAI,EAAE,QAAQ;QACd,WAAW,EAAE,oFAAoF;QACjG,QAAQ,EAAE,CAAC;KACZ;IACD;QACE,EAAE,EAAE,gBAAgB;QACpB,IAAI,EAAE,IAAI;QACV,IAAI,EAAE,QAAQ;QACd,WAAW,EAAE,yGAAyG;QACtH,QAAQ,EAAE,CAAC;KACZ;CACF,CAAC;AAEF,OAAO;AACM,QAAA,eAAe,GAAoB;IAC9C;QACE,EAAE,EAAE,kBAAkB;QACtB,IAAI,EAAE,IAAI;QACV,IAAI,EAAE,QAAQ;QACd,WAAW,EAAE,mDAAmD;QAChE,QAAQ,EAAE,CAAC;KACZ;IACD;QACE,EAAE,EAAE,oBAAoB;QACxB,IAAI,EAAE,IAAI;QACV,IAAI,EAAE,QAAQ;QACd,WAAW,EAAE,6EAA6E;QAC1F,QAAQ,EAAE,CAAC;QACX,IAAI,EAAE,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC,OAAO;KAClC;CACF,CAAC;AAEF,OAAO;AACM,QAAA,YAAY,GAAoB;IAC3C;QACE,EAAE,EAAE,cAAc;QAClB,IAAI,EAAE,IAAI;QACV,IAAI,EAAE,QAAQ;QACd,WAAW,EAAE,wBAAwB;QACrC,QAAQ,EAAE,CAAC;KACZ;IACD;QACE,EAAE,EAAE,gBAAgB;QACpB,IAAI,EAAE,IAAI;QACV,IAAI,EAAE,QAAQ;QACd,WAAW,EAAE,6CAA6C;QAC1D,QAAQ,EAAE,EAAE;QACZ,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE;KAClB;CACF,CAAC;AAEF,OAAO;AACM,QAAA,cAAc,GAAoB;IAC7C;QACE,EAAE,EAAE,oBAAoB;QACxB,IAAI,EAAE,IAAI;QACV,IAAI,EAAE,QAAQ;QACd,WAAW,EAAE,mDAAmD;QAChE,QAAQ,EAAE,CAAC;KACZ;IACD;QACE,EAAE,EAAE,eAAe;QACnB,IAAI,EAAE,IAAI;QACV,IAAI,EAAE,QAAQ;QACd,WAAW,EAAE,sEAAsE;QACnF,QAAQ,EAAE,EAAE;QACZ,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;KACnB;CACF,CAAC;AAEF;;GAEG;AACH,SAAgB,iBAAiB,CAAC,QAAwB;IACxD,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,sBAAc,CAAC,OAAO;YACzB,OAAO,CAAC,GAAG,sBAAc,CAAC,CAAC;QAC7B,KAAK,sBAAc,CAAC,QAAQ;YAC1B,OAAO,CAAC,GAAG,uBAAe,CAAC,CAAC;QAC9B,KAAK,sBAAc,CAAC,KAAK;YACvB,OAAO,CAAC,GAAG,oBAAY,CAAC,CAAC;QAC3B,KAAK,sBAAc,CAAC,OAAO;YACzB,OAAO,CAAC,GAAG,sBAAc,CAAC,CAAC;QAC7B;YACE,OAAO,EAAE,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAa,oBAAoB;IAAjC;QACU,iBAAY,GAAiC,IAAI,GAAG,EAAE,CAAC;QACvD,mBAAc,GAAwC,IAAI,GAAG,EAAE,CAAC;IA2K1E,CAAC;IAzKC;;OAEG;IACI,sBAAsB,CAAC,QAAgB,EAAE,QAAwB;QACtE,MAAM,MAAM,GAAG,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QAC3C,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACxC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,QAAgB;QACrC,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;IAC/C,CAAC;IAED;;OAEG;IACI,WAAW,CAAC,QAAgB,EAAE,OAAe;QAClD,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,SAAS;YAAE,OAAO,KAAK,CAAC;QAE7B,MAAM,iBAAiB,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAClD,OAAO,iBAAiB,IAAI,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACI,QAAQ,CAAC,QAAgB,EAAE,OAAe;QAC/C,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE,CAAC;YACzC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAC9C,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,CAAC;QAEjD,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YACtC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS;QACT,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QAC1D,SAAS,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC,QAAQ,IAAI,CAAC,CAAC;QACzC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;QAE7C,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,QAAgB;QACrC,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,SAAS;YAAE,OAAO;QAEvB,KAAK,MAAM,OAAO,IAAI,SAAS,EAAE,CAAC;YAChC,IAAI,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC3B,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC;YACvB,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,QAAgB,EAAE,OAAyD;QACpG,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAC9C,MAAM,OAAO,GAAkB,EAAE,CAAC;QAElC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;gBACxD,oBAAoB;gBACpB,IAAI,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,CAAC;oBAC/C,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;gBAClD,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,OAAe,EAAE,OAAe;QACzD,MAAM,aAAa,GAAG,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,cAAc,EAAE,oBAAoB,CAAC,CAAC;QAErG,QAAQ,OAAO,EAAE,CAAC;YAChB,KAAK,OAAO;gBACV,OAAO,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACzC;gBACE,OAAO,KAAK,CAAC;QACjB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,OAAe;QACrC,QAAQ,OAAO,EAAE,CAAC;YAChB,KAAK,kBAAkB;gBACrB,OAAO;oBACL;wBACE,IAAI,EAAE,MAAM;wBACZ,KAAK,EAAE,EAAE;wBACT,QAAQ,EAAE,CAAC;wBACX,MAAM,EAAE,MAAM;qBACf;iBACF,CAAC;YACJ,KAAK,kBAAkB;gBACrB,OAAO;oBACL;wBACE,IAAI,EAAE,QAAQ;wBACd,KAAK,EAAE,CAAC,EAAE;wBACV,QAAQ,EAAE,CAAC;wBACX,MAAM,EAAE,OAAO;qBAChB;iBACF,CAAC;YACJ,KAAK,cAAc;gBACjB,OAAO;oBACL;wBACE,IAAI,EAAE,MAAM;wBACZ,KAAK,EAAE,EAAE;wBACT,MAAM,EAAE,OAAO;qBAChB;iBACF,CAAC;YACJ;gBACE,OAAO,EAAE,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACI,sBAAsB,CAAC,OAAc;QAC1C,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACpD,MAAM,eAAe,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QAE1D,iBAAiB;QACjB,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,sBAAc,CAAC,OAAO,CAAC,CAAC;QACzE,IAAI,OAAO,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YAC/B,MAAM,cAAc,GAAG,eAAe,CAAC,QAAQ,CAAC,sBAAc,CAAC,KAAK,CAAC,CAAC;YACtE,MAAM,gBAAgB,GAAG,eAAe,CAAC,QAAQ,CAAC,sBAAc,CAAC,OAAO,CAAC,CAAC;YAE1E,IAAI,CAAC,cAAc,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACzC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;YAClD,CAAC;QACH,CAAC;QAED,YAAY;QACZ,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACjC,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,sBAAc,CAAC,KAAK,CAAC,CAAC;YAClF,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3B,OAAO,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;YACzD,CAAC;QACH,CAAC;QAED,qBAAqB;QACrB,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,MAAM,UAAU,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;YACnC,IAAI,UAAU,CAAC,QAAQ,KAAK,sBAAc,CAAC,OAAO,EAAE,CAAC;gBACnD,OAAO,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;YACrD,CAAC;QACH,CAAC;QAED,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;IACtC,CAAC;CACF;AA7KD,oDA6KC;AAED;;GAEG;AACH,MAAa,gBAAgB;IAC3B;;OAEG;IACI,MAAM,CAAC,gBAAgB,CAAC,SAAmB;QAChD,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;QAChC,CAAC;QAED,MAAM,UAAU,GAAqB;YACnC,sBAAc,CAAC,OAAO,EAAK,OAAO;YAClC,sBAAc,CAAC,QAAQ,EAAI,OAAO;YAClC,sBAAc,CAAC,QAAQ;YACvB,sBAAc,CAAC,KAAK,EAAO,OAAO;YAClC,sBAAc,CAAC,KAAK;YACpB,sBAAc,CAAC,KAAK;YACpB,sBAAc,CAAC,KAAK;YACpB,sBAAc,CAAC,OAAO,CAAK,OAAO;SACnC,CAAC;QAEF,SAAS;QACT,KAAK,IAAI,CAAC,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/C,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC9C,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE,CAAC;QAED,QAAQ;QACR,MAAM,WAAW,GAAG,IAAI,GAAG,EAA0B,CAAC;QACtD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,kBAAkB,CAAC,WAAwC;QACvE,MAAM,MAAM,GAAG,IAAI,GAAG,EAA0B,CAAC;QAEjD,KAAK,MAAM,QAAQ,IAAI,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC;YAC5C,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACxD,CAAC;QAED,OAAO,CACL,MAAM,CAAC,GAAG,CAAC,sBAAc,CAAC,OAAO,CAAC,KAAK,CAAC;YACxC,MAAM,CAAC,GAAG,CAAC,sBAAc,CAAC,QAAQ,CAAC,KAAK,CAAC;YACzC,MAAM,CAAC,GAAG,CAAC,sBAAc,CAAC,KAAK,CAAC,KAAK,CAAC;YACtC,MAAM,CAAC,GAAG,CAAC,sBAAc,CAAC,OAAO,CAAC,KAAK,CAAC,CACzC,CAAC;IACJ,CAAC;CACF;AApDD,4CAoDC"}