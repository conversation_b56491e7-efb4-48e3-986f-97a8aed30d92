"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SynergyManager = exports.ELEMENT_COUNTER_MAP = exports.QUN_TRAITS = exports.WU_TRAITS = exports.WEI_TRAITS = exports.SHU_TRAITS = void 0;
const types_1 = require("../types");
// 蜀国羁绊
exports.SHU_TRAITS = [
    {
        id: 'shu_benevolence',
        name: '仁德',
        description: '蜀国武将获得额外治疗效果',
        requiredCount: 2,
        effects: [
            {
                type: 'heal',
                value: 15,
                target: 'ally',
                duration: -1 // 永久效果
            }
        ]
    },
    {
        id: 'shu_unity',
        name: '团结',
        description: '蜀国武将互相提供护甲加成',
        requiredCount: 4,
        effects: [
            {
                type: 'buff',
                value: 20,
                target: 'ally',
                duration: -1
            }
        ]
    },
    {
        id: 'shu_legacy',
        name: '传承',
        description: '蜀国武将死亡时为友军提供攻击力加成',
        requiredCount: 6,
        effects: [
            {
                type: 'buff',
                value: 25,
                target: 'ally',
                duration: 3
            }
        ]
    }
];
// 魏国羁绊
exports.WEI_TRAITS = [
    {
        id: 'wei_strategy',
        name: '权谋',
        description: '魏国武将获得额外能量恢复',
        requiredCount: 2,
        effects: [
            {
                type: 'buff',
                value: 10,
                target: 'self',
                duration: -1
            }
        ]
    },
    {
        id: 'wei_dominance',
        name: '霸业',
        description: '魏国武将对敌方造成额外伤害',
        requiredCount: 4,
        effects: [
            {
                type: 'damage',
                value: 20,
                target: 'enemy',
                duration: -1
            }
        ]
    },
    {
        id: 'wei_empire',
        name: '帝业',
        description: '魏国武将获得控制免疫',
        requiredCount: 6,
        effects: [
            {
                type: 'control',
                value: 100,
                target: 'self',
                duration: -1
            }
        ]
    }
];
// 吴国羁绊
exports.WU_TRAITS = [
    {
        id: 'wu_naval',
        name: '水战',
        description: '吴国武将在河流地形获得加成',
        requiredCount: 2,
        effects: [
            {
                type: 'terrain',
                value: 25,
                target: 'self',
                duration: -1
            }
        ]
    },
    {
        id: 'wu_flexibility',
        name: '机动',
        description: '吴国武将获得额外移动速度',
        requiredCount: 4,
        effects: [
            {
                type: 'buff',
                value: 30,
                target: 'self',
                duration: -1
            }
        ]
    },
    {
        id: 'wu_persistence',
        name: '坚韧',
        description: '吴国武将获得复活机会',
        requiredCount: 6,
        effects: [
            {
                type: 'heal',
                value: 50,
                target: 'self',
                duration: 1
            }
        ]
    }
];
// 群雄羁绊
exports.QUN_TRAITS = [
    {
        id: 'qun_chaos',
        name: '乱世',
        description: '群雄武将获得随机增益效果',
        requiredCount: 2,
        effects: [
            {
                type: 'buff',
                value: 15,
                target: 'self',
                duration: -1
            }
        ]
    },
    {
        id: 'qun_independence',
        name: '独立',
        description: '群雄武将不受负面羁绊影响',
        requiredCount: 4,
        effects: [
            {
                type: 'control',
                value: 50,
                target: 'self',
                duration: -1
            }
        ]
    }
];
// 五行相克系统
exports.ELEMENT_COUNTER_MAP = {
    [types_1.Element.METAL]: [types_1.Element.WOOD, types_1.Element.THUNDER],
    [types_1.Element.WOOD]: [types_1.Element.EARTH, types_1.Element.WATER],
    [types_1.Element.WATER]: [types_1.Element.FIRE, types_1.Element.METAL],
    [types_1.Element.FIRE]: [types_1.Element.METAL, types_1.Element.WOOD],
    [types_1.Element.EARTH]: [types_1.Element.WATER, types_1.Element.THUNDER],
    [types_1.Element.THUNDER]: [types_1.Element.EARTH, types_1.Element.FIRE]
};
/**
 * 羁绊系统管理器
 */
class SynergyManager {
    /**
     * 计算阵营羁绊效果
     */
    static calculateFactionSynergies(heroes) {
        const factionCounts = new Map();
        const synergyEffects = new Map();
        // 统计各阵营数量
        for (const hero of heroes) {
            const count = factionCounts.get(hero.faction) || 0;
            factionCounts.set(hero.faction, count + 1);
        }
        // 计算羁绊效果
        for (const [faction, count] of factionCounts) {
            const traits = this.getFactionTraits(faction);
            for (const trait of traits) {
                if (count >= trait.requiredCount) {
                    synergyEffects.set(trait.id, trait.effects);
                }
            }
        }
        return synergyEffects;
    }
    /**
     * 计算五行相克效果
     */
    static calculateElementCounters(attacker, defender) {
        const attackerElement = attacker.element;
        const defenderElement = defender.element;
        // 检查是否相克
        const counters = exports.ELEMENT_COUNTER_MAP[attackerElement];
        if (counters.includes(defenderElement)) {
            return 1.5; // 相克时伤害增加50%
        }
        // 检查是否被克
        const defenderCounters = exports.ELEMENT_COUNTER_MAP[defenderElement];
        if (defenderCounters.includes(attackerElement)) {
            return 0.7; // 被克时伤害减少30%
        }
        return 1.0; // 无相克关系
    }
    /**
     * 获取阵营特色
     */
    static getFactionTraits(faction) {
        switch (faction) {
            case types_1.Faction.SHU:
                return exports.SHU_TRAITS;
            case types_1.Faction.WEI:
                return exports.WEI_TRAITS;
            case types_1.Faction.WU:
                return exports.WU_TRAITS;
            case types_1.Faction.QUN:
                return exports.QUN_TRAITS;
            default:
                return [];
        }
    }
    /**
     * 计算羁绊标签效果（如"猛将"、"谋士"等）
     */
    static calculateTagSynergies(heroes) {
        const tagCounts = new Map();
        const synergyEffects = new Map();
        // 统计标签数量
        for (const hero of heroes) {
            for (const tag of hero.synergies) {
                const count = tagCounts.get(tag) || 0;
                tagCounts.set(tag, count + 1);
            }
        }
        // 计算标签羁绊效果
        for (const [tag, count] of tagCounts) {
            const effects = this.getTagEffects(tag, count);
            if (effects.length > 0) {
                synergyEffects.set(tag, effects);
            }
        }
        return synergyEffects;
    }
    /**
     * 获取标签羁绊效果
     */
    static getTagEffects(tag, count) {
        switch (tag) {
            case '猛将':
                if (count >= 2) {
                    return [{
                            type: 'damage',
                            value: 20,
                            target: 'self',
                            duration: -1
                        }];
                }
                break;
            case '谋士':
                if (count >= 2) {
                    return [{
                            type: 'buff',
                            value: 15,
                            target: 'ally',
                            duration: -1
                        }];
                }
                break;
            case '神将':
                if (count >= 1) {
                    return [{
                            type: 'buff',
                            value: 30,
                            target: 'self',
                            duration: -1
                        }];
                }
                break;
            case '女将':
                if (count >= 2) {
                    return [{
                            type: 'heal',
                            value: 25,
                            target: 'ally',
                            duration: -1
                        }];
                }
                break;
        }
        return [];
    }
    /**
     * 应用羁绊效果到英雄
     */
    static applySynergiesToHero(hero, synergies) {
        const modifiedHero = { ...hero };
        for (const [synergyId, effects] of synergies) {
            for (const effect of effects) {
                switch (effect.type) {
                    case 'damage':
                        modifiedHero.stats.attack += effect.value || 0;
                        break;
                    case 'heal':
                        modifiedHero.stats.health += effect.value || 0;
                        break;
                    case 'buff':
                        // 根据具体buff类型应用
                        modifiedHero.stats.attack += (effect.value || 0) * 0.5;
                        modifiedHero.stats.armor += (effect.value || 0) * 0.3;
                        break;
                }
            }
        }
        return modifiedHero;
    }
    /**
     * 获取羁绊描述文本
     */
    static getSynergyDescription(heroes) {
        const descriptions = [];
        // 阵营羁绊
        const factionSynergies = this.calculateFactionSynergies(heroes);
        for (const [synergyId] of factionSynergies) {
            const trait = this.findTraitById(synergyId);
            if (trait) {
                descriptions.push(`${trait.name}: ${trait.description}`);
            }
        }
        // 标签羁绊
        const tagSynergies = this.calculateTagSynergies(heroes);
        for (const [tag] of tagSynergies) {
            descriptions.push(`${tag}羁绊已激活`);
        }
        return descriptions;
    }
    /**
     * 根据ID查找特色
     */
    static findTraitById(id) {
        const allTraits = [
            ...exports.SHU_TRAITS,
            ...exports.WEI_TRAITS,
            ...exports.WU_TRAITS,
            ...exports.QUN_TRAITS
        ];
        return allTraits.find(trait => trait.id === id) || null;
    }
}
exports.SynergyManager = SynergyManager;
//# sourceMappingURL=FactionSystem.js.map