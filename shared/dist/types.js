"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GAME_CONFIG = exports.MessageType = exports.BattleStance = exports.WeatherType = exports.TerrainType = exports.SpellSchool = exports.SkillType = exports.CardRarity = exports.CardType = exports.UnitRole = exports.UNIT_COUNTER_MAP = exports.UnitType = exports.Element = exports.Faction = exports.PlayerIdentity = exports.StrategicPhase = exports.GameState = void 0;
// ==================== 游戏核心状态 ====================
// 游戏主状态（嵌套状态机）
var GameState;
(function (GameState) {
    GameState["LOBBY"] = "lobby";
    GameState["STRATEGIC"] = "strategic";
    GameState["COMBAT_INIT"] = "combat_init";
    GameState["COMBAT_DEPLOY"] = "combat_deploy";
    GameState["COMBAT_AUTO"] = "combat_auto";
    GameState["COMBAT_RESULT"] = "combat_result";
    GameState["GAME_END"] = "game_end"; // 游戏结束
})(GameState || (exports.GameState = GameState = {}));
// 战略回合状态
var StrategicPhase;
(function (StrategicPhase) {
    StrategicPhase["RESOURCE_COLLECTION"] = "resource_collection";
    StrategicPhase["PLAYER_ACTION"] = "player_action";
    StrategicPhase["EVENT_PROCESSING"] = "event_processing";
    StrategicPhase["TURN_END"] = "turn_end"; // 回合结束
})(StrategicPhase || (exports.StrategicPhase = StrategicPhase = {}));
// ==================== 身份系统 ====================
// 玩家身份
var PlayerIdentity;
(function (PlayerIdentity) {
    PlayerIdentity["EMPEROR"] = "emperor";
    PlayerIdentity["LOYALIST"] = "loyalist";
    PlayerIdentity["REBEL"] = "rebel";
    PlayerIdentity["TRAITOR"] = "traitor"; // 内奸
})(PlayerIdentity || (exports.PlayerIdentity = PlayerIdentity = {}));
// ==================== 阵营系统 ====================
// 阵营（剧本相关）
var Faction;
(function (Faction) {
    Faction["SHU"] = "shu";
    Faction["WEI"] = "wei";
    Faction["WU"] = "wu";
    Faction["QUN"] = "qun";
    Faction["JIN"] = "jin";
    Faction["HAN"] = "han"; // 汉
})(Faction || (exports.Faction = Faction = {}));
// 五行属性
var Element;
(function (Element) {
    Element["METAL"] = "metal";
    Element["WOOD"] = "wood";
    Element["WATER"] = "water";
    Element["FIRE"] = "fire";
    Element["EARTH"] = "earth";
    Element["THUNDER"] = "thunder"; // 雷
})(Element || (exports.Element = Element = {}));
// ==================== 兵种系统 ====================
// 兵种类型
var UnitType;
(function (UnitType) {
    UnitType["INFANTRY"] = "infantry";
    UnitType["CAVALRY"] = "cavalry";
    UnitType["ARCHER"] = "archer";
    UnitType["CHARIOT"] = "chariot";
    UnitType["MAGE"] = "mage"; // 法师 (Staff 杖)
})(UnitType || (exports.UnitType = UnitType = {}));
// 兵种相克关系
exports.UNIT_COUNTER_MAP = {
    [UnitType.INFANTRY]: [UnitType.CAVALRY, UnitType.CHARIOT, UnitType.MAGE],
    [UnitType.CAVALRY]: [UnitType.ARCHER, UnitType.MAGE],
    [UnitType.ARCHER]: [UnitType.INFANTRY, UnitType.CHARIOT, UnitType.MAGE],
    [UnitType.CHARIOT]: [UnitType.CAVALRY, UnitType.MAGE],
    [UnitType.MAGE]: [] // 法师被所有兵种克制，但也克制所有兵种
};
// 单位定位
var UnitRole;
(function (UnitRole) {
    UnitRole["TANK"] = "tank";
    UnitRole["WARRIOR"] = "warrior";
    UnitRole["MAGE"] = "mage";
    UnitRole["CONTROL"] = "control";
    UnitRole["ARCHER"] = "archer";
    UnitRole["SUPPORT"] = "support"; // 辅助
})(UnitRole || (exports.UnitRole = UnitRole = {}));
// ==================== 卡牌系统 ====================
// 卡牌类型
var CardType;
(function (CardType) {
    CardType["HERO"] = "hero";
    CardType["SPELL"] = "spell";
    CardType["STRATEGIC"] = "strategic"; // 战略指令卡
})(CardType || (exports.CardType = CardType = {}));
// 卡牌稀有度
var CardRarity;
(function (CardRarity) {
    CardRarity["COMMON"] = "common";
    CardRarity["RARE"] = "rare";
    CardRarity["EPIC"] = "epic";
    CardRarity["LEGENDARY"] = "legendary"; // 史诗 (天)
})(CardRarity || (exports.CardRarity = CardRarity = {}));
// 技能类型
var SkillType;
(function (SkillType) {
    SkillType["ACTIVE"] = "active";
    SkillType["PASSIVE"] = "passive"; // 被动技
})(SkillType || (exports.SkillType = SkillType = {}));
// 法术系统
var SpellSchool;
(function (SpellSchool) {
    SpellSchool["MILITARY"] = "military";
    SpellSchool["MYSTIC"] = "mystic";
    SpellSchool["MECHANISM"] = "mechanism"; // 墨家机关
})(SpellSchool || (exports.SpellSchool = SpellSchool = {}));
// ==================== 地形与天气系统 ====================
// 地形类型
var TerrainType;
(function (TerrainType) {
    TerrainType["PLAIN"] = "plain";
    TerrainType["MOUNTAIN"] = "mountain";
    TerrainType["FOREST"] = "forest";
    TerrainType["RIVER"] = "river";
    TerrainType["DESERT"] = "desert";
    TerrainType["SWAMP"] = "swamp";
    TerrainType["CRYSTAL"] = "crystal";
    TerrainType["SCORCHED"] = "scorched"; // 焦土
})(TerrainType || (exports.TerrainType = TerrainType = {}));
// 天气类型
var WeatherType;
(function (WeatherType) {
    WeatherType["CLEAR"] = "clear";
    WeatherType["RAIN"] = "rain";
    WeatherType["SANDSTORM"] = "sandstorm";
    WeatherType["AURORA"] = "aurora";
    WeatherType["WINTER"] = "winter";
    WeatherType["THUNDER"] = "thunder"; // 雷暴
})(WeatherType || (exports.WeatherType = WeatherType = {}));
// ==================== 玩家系统 ====================
// 战斗状态
var BattleStance;
(function (BattleStance) {
    BattleStance["ATTACK"] = "attack";
    BattleStance["DEFEND"] = "defend";
    BattleStance["SUPPORT"] = "support";
    BattleStance["IGNORE"] = "ignore"; // 无视
})(BattleStance || (exports.BattleStance = BattleStance = {}));
// ==================== 消息系统 ====================
// 游戏消息类型
var MessageType;
(function (MessageType) {
    // 认证相关
    MessageType["LOGIN"] = "login";
    MessageType["REGISTER"] = "register";
    MessageType["LOGOUT"] = "logout";
    // 房间相关
    MessageType["CREATE_ROOM"] = "create_room";
    MessageType["JOIN_ROOM"] = "join_room";
    MessageType["LEAVE_ROOM"] = "leave_room";
    MessageType["ROOM_LIST"] = "room_list";
    // 游戏状态
    MessageType["PLAYER_READY"] = "player_ready";
    MessageType["START_GAME"] = "start_game";
    MessageType["GAME_UPDATE"] = "game_update";
    MessageType["TURN_START"] = "turn_start";
    MessageType["TURN_END"] = "turn_end";
    // 战略层
    MessageType["MOVE_ARMY"] = "move_army";
    MessageType["BUILD_STRUCTURE"] = "build_structure";
    MessageType["RECRUIT_UNITS"] = "recruit_units";
    MessageType["USE_IDENTITY_SKILL"] = "use_identity_skill";
    // 战斗层
    MessageType["COMBAT_START"] = "combat_start";
    MessageType["DEPLOY_CARDS"] = "deploy_cards";
    MessageType["COMBAT_AUTO"] = "combat_auto";
    MessageType["COMBAT_END"] = "combat_end";
    // 卡牌相关
    MessageType["BUY_CARD"] = "buy_card";
    MessageType["SELL_CARD"] = "sell_card";
    MessageType["UPGRADE_CARD"] = "upgrade_card";
    // 匹配相关
    MessageType["FIND_MATCH"] = "find_match";
    MessageType["CANCEL_MATCH"] = "cancel_match";
    MessageType["MATCH_FOUND"] = "match_found";
})(MessageType || (exports.MessageType = MessageType = {}));
// ==================== 游戏配置 ====================
exports.GAME_CONFIG = {
    // 房间设置
    MAX_PLAYERS_PER_ROOM: 8,
    MIN_PLAYERS_TO_START: 8,
    // 游戏时长
    MAX_TURNS: 120,
    TURN_TIME_LIMIT: 60000, // 60秒每回合
    // 地图设置
    MAP_WIDTH: 16,
    MAP_HEIGHT: 8,
    // 战斗设置
    COMBAT_BOARD_WIDTH: 16,
    COMBAT_BOARD_HEIGHT: 8,
    DEPLOY_TIME_LIMIT: 60000, // 60秒部署时间
    // 资源设置
    INITIAL_GOLD: 50,
    INITIAL_POPULATION: 100,
    INITIAL_BACKPACK_CAPACITY: 30,
    // 卡牌设置
    CARD_COST_MULTIPLIER: {
        [CardRarity.COMMON]: 1,
        [CardRarity.RARE]: 1.5,
        [CardRarity.EPIC]: 2,
        [CardRarity.LEGENDARY]: 3
    },
    // 匹配设置
    MATCH_TIMEOUT: 30000, // 30秒匹配超时
};
//# sourceMappingURL=types.js.map