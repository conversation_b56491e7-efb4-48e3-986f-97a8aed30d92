// 游戏状态
export var GameState;
(function (GameState) {
    GameState["WAITING"] = "waiting";
    GameState["STARTING"] = "starting";
    GameState["PLAYING"] = "playing";
    GameState["FINISHED"] = "finished";
})(GameState || (GameState = {}));
// 游戏消息类型
export var MessageType;
(function (MessageType) {
    // 认证相关
    MessageType["LOGIN"] = "login";
    MessageType["REGISTER"] = "register";
    MessageType["LOGOUT"] = "logout";
    // 房间相关
    MessageType["CREATE_ROOM"] = "create_room";
    MessageType["JOIN_ROOM"] = "join_room";
    MessageType["LEAVE_ROOM"] = "leave_room";
    MessageType["ROOM_LIST"] = "room_list";
    // 游戏相关
    MessageType["PLAYER_READY"] = "player_ready";
    MessageType["START_GAME"] = "start_game";
    MessageType["GAME_UPDATE"] = "game_update";
    MessageType["PLAYER_MOVE"] = "player_move";
    // 匹配相关
    MessageType["FIND_MATCH"] = "find_match";
    MessageType["CANCEL_MATCH"] = "cancel_match";
    MessageType["MATCH_FOUND"] = "match_found";
})(MessageType || (MessageType = {}));
// 游戏配置
export const GAME_CONFIG = {
    MAX_PLAYERS_PER_ROOM: 8,
    MIN_PLAYERS_TO_START: 2,
    GAME_WIDTH: 1024,
    GAME_HEIGHT: 768,
    MATCH_TIMEOUT: 30000, // 30秒匹配超时
};
//# sourceMappingURL=types.js.map