export * from './types';
export * from './game/GameController';
export * from './game/StrategicGameManager';
export * from './map/HexMap';
export * from './combat/AutoChessEngine';
export * from './identity/IdentitySkills';
export * from './faction/FactionSystem';
export * from './cards/CardDatabase';
export declare const generateId: () => string;
export declare const validateUsername: (username: string) => boolean;
export declare const validatePassword: (password: string) => boolean;
//# sourceMappingURL=index.d.ts.map