{"version": 3, "file": "EconomySystem.js", "sourceRoot": "", "sources": ["../../src/economy/EconomySystem.ts"], "names": [], "mappings": ";;;AAAA,oCAQkB;AAwBlB,QAAQ;AACK,QAAA,cAAc,GAAiC;IAC1D,QAAQ,EAAE;QACR,EAAE,EAAE,UAAU;QACd,IAAI,EAAE,IAAI;QACV,WAAW,EAAE,kBAAkB;QAC/B,IAAI,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,EAAE,EAAE;QACnC,SAAS,EAAE,CAAC;QACZ,QAAQ,EAAE,CAAC;QACX,OAAO,EAAE;YACP,EAAE,IAAI,EAAE,eAAe,EAAE,KAAK,EAAE,EAAE,EAAE;YACpC,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,EAAE;SACjC;QACD,YAAY,EAAE;YACZ,OAAO,EAAE,CAAC,mBAAW,CAAC,KAAK,EAAE,mBAAW,CAAC,QAAQ,CAAC;SACnD;KACF;IAED,KAAK,EAAE;QACL,EAAE,EAAE,OAAO;QACX,IAAI,EAAE,IAAI;QACV,WAAW,EAAE,eAAe;QAC5B,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE;QAClC,SAAS,EAAE,CAAC;QACZ,QAAQ,EAAE,CAAC;QACX,OAAO,EAAE;YACP,EAAE,IAAI,EAAE,eAAe,EAAE,KAAK,EAAE,EAAE,EAAE;YACpC,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,EAAE;SACjC;QACD,YAAY,EAAE;YACZ,OAAO,EAAE,CAAC,mBAAW,CAAC,KAAK,EAAE,mBAAW,CAAC,MAAM,EAAE,mBAAW,CAAC,QAAQ,CAAC;SACvE;KACF;IAED,IAAI,EAAE;QACJ,EAAE,EAAE,MAAM;QACV,IAAI,EAAE,IAAI;QACV,WAAW,EAAE,aAAa;QAC1B,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE;QAClC,SAAS,EAAE,CAAC;QACZ,QAAQ,EAAE,CAAC;QACX,OAAO,EAAE;YACP,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,CAAC,EAAE;SACrC;QACD,YAAY,EAAE;YACZ,OAAO,EAAE,CAAC,mBAAW,CAAC,QAAQ,EAAE,mBAAW,CAAC,OAAO,CAAC;SACrD;KACF;IAED,IAAI,EAAE;QACJ,EAAE,EAAE,MAAM;QACV,IAAI,EAAE,IAAI;QACV,WAAW,EAAE,aAAa;QAC1B,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE;QAClC,SAAS,EAAE,CAAC;QACZ,QAAQ,EAAE,CAAC;QACX,OAAO,EAAE;YACP,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,CAAC,EAAE;SACrC;QACD,YAAY,EAAE;YACZ,OAAO,EAAE,CAAC,mBAAW,CAAC,KAAK,EAAE,mBAAW,CAAC,KAAK,CAAC;SAChD;KACF;IAED,MAAM,EAAE;QACN,EAAE,EAAE,QAAQ;QACZ,IAAI,EAAE,IAAI;QACV,WAAW,EAAE,aAAa;QAC1B,IAAI,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,EAAE,EAAE;QACnC,SAAS,EAAE,CAAC;QACZ,QAAQ,EAAE,CAAC;QACX,OAAO,EAAE;YACP,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,CAAC,EAAE;YACpC,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,EAAE;SACjC;QACD,YAAY,EAAE;YACZ,OAAO,EAAE,CAAC,mBAAW,CAAC,KAAK,EAAE,mBAAW,CAAC,QAAQ,CAAC;YAClD,WAAW,EAAE,CAAC;SACf;KACF;IAED,MAAM,EAAE;QACN,EAAE,EAAE,QAAQ;QACZ,IAAI,EAAE,IAAI;QACV,WAAW,EAAE,kBAAkB;QAC/B,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE;QAClC,SAAS,EAAE,CAAC;QACZ,QAAQ,EAAE,CAAC;QACX,OAAO,EAAE;YACP,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,CAAC,EAAE;SACrC;QACD,YAAY,EAAE;YACZ,OAAO,EAAE,CAAC,mBAAW,CAAC,KAAK,EAAE,mBAAW,CAAC,KAAK,CAAC;SAChD;KACF;IAED,OAAO,EAAE;QACP,EAAE,EAAE,SAAS;QACb,IAAI,EAAE,IAAI;QACV,WAAW,EAAE,cAAc;QAC3B,IAAI,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,EAAE,EAAE;QACnC,SAAS,EAAE,CAAC;QACZ,QAAQ,EAAE,CAAC;QACX,OAAO,EAAE;YACP,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,CAAC,EAAE;SACrC;QACD,YAAY,EAAE;YACZ,OAAO,EAAE,CAAC,mBAAW,CAAC,KAAK,CAAC;YAC5B,iBAAiB,EAAE,CAAC,QAAQ,CAAC;YAC7B,WAAW,EAAE,CAAC;SACf;KACF;CACF,CAAC;AAaF;;GAEG;AACH,MAAa,aAAa;IAIxB,YAAY,iBAAoC;QAFxC,gBAAW,GAA4B,IAAI,GAAG,EAAE,CAAC;QAGvD,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;IAC7C,CAAC;IAED;;OAEG;IACI,uBAAuB,CAAC,IAAa;QAC1C,OAAO;QACP,IAAI,cAAc,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAE3C,SAAS;QACT,MAAM,qBAAqB,GAAG,IAAI,CAAC,iBAAiB,CAAC,2BAA2B,CAC9E,IAAI,CAAC,WAAW,EAChB,cAAc,CACf,CAAC;QAEF,SAAS;QACT,IAAI,aAAa,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;QAC9D,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACtC,MAAM,KAAK,GAAG,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;gBACpD,aAAa,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC;gBACjC,aAAa,CAAC,UAAU,IAAI,KAAK,CAAC,UAAU,CAAC;gBAC7C,aAAa,CAAC,UAAU,IAAI,KAAK,CAAC,UAAU,CAAC;YAC/C,CAAC;QACH,CAAC;QAED,OAAO;YACL,IAAI,EAAE,qBAAqB,CAAC,IAAI,GAAG,aAAa,CAAC,IAAI;YACrD,UAAU,EAAE,qBAAqB,CAAC,UAAU,GAAG,aAAa,CAAC,UAAU;YACvE,UAAU,EAAE,aAAa,CAAC,UAAU;SACrC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,QAAkB;QAC/C,MAAM,YAAY,GAAG,sBAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACnD,IAAI,KAAK,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;QAEtD,KAAK,MAAM,MAAM,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;YACtC,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;YAE5C,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;gBACpB,KAAK,gBAAgB;oBACnB,IAAI,QAAQ,CAAC,IAAI,KAAK,MAAM,IAAI,QAAQ,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;wBAC3D,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC;oBACtB,CAAC;yBAAM,IAAI,QAAQ,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;wBACpC,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC;oBAC5B,CAAC;yBAAM,IAAI,QAAQ,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;wBACvC,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC;oBAC5B,CAAC;oBACD,MAAM;YACV,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACI,gBAAgB,CACrB,MAAc,EACd,IAAa,EACb,cAAsB;QAEtB,MAAM,YAAY,GAAG,sBAAc,CAAC,cAAc,CAAC,CAAC;QACpD,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;QAC/C,CAAC;QAED,WAAW;QACX,IAAI,MAAM,CAAC,SAAS,CAAC,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACnD,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;QAC7C,CAAC;QACD,IAAI,MAAM,CAAC,SAAS,CAAC,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YAC/D,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;QAC7C,CAAC;QAED,SAAS;QACT,IAAI,YAAY,CAAC,YAAY,CAAC,OAAO;YACjC,CAAC,YAAY,CAAC,YAAY,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;YAClE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC;QAC9C,CAAC;QAED,WAAW;QACX,IAAI,YAAY,CAAC,YAAY,CAAC,WAAW;YACrC,MAAM,CAAC,KAAK,GAAG,YAAY,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;YACzD,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;QAC/C,CAAC;QAED,aAAa;QACb,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,cAAc,CAAC,EAAE,CAAC;YAC1E,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;QAC/C,CAAC;QAED,WAAW;QACX,IAAI,YAAY,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC;YAChD,gBAAgB;YAChB,cAAc;QAChB,CAAC;QAED,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACI,aAAa,CAClB,MAAc,EACd,IAAa,EACb,cAAsB;QAEtB,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC;QACrE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACvB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,YAAY,GAAG,sBAAc,CAAC,cAAc,CAAC,CAAC;QAEpD,OAAO;QACP,MAAM,CAAC,SAAS,CAAC,IAAI,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;QAChD,MAAM,CAAC,SAAS,CAAC,UAAU,IAAI,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC;QAE5D,OAAO;QACP,MAAM,WAAW,GAAa;YAC5B,EAAE,EAAE,YAAY,IAAI,CAAC,GAAG,EAAE,EAAE;YAC5B,IAAI,EAAE,cAAkC;YACxC,KAAK,EAAE,CAAC;YACR,OAAO,EAAE,CAAC,GAAG,YAAY,CAAC,OAAO,CAAC;SACnC,CAAC;QAEF,QAAQ;QACR,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACtB,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAEjC,OAAO,CAAC,GAAG,CAAC,OAAO,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,CAAC,SAAS,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC;QAC1G,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACI,eAAe,CACpB,MAAc,EACd,QAAkB;QAElB,MAAM,YAAY,GAAG,sBAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEnD,IAAI,QAAQ,CAAC,KAAK,IAAI,YAAY,CAAC,QAAQ,EAAE,CAAC;YAC5C,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,WAAW,GAAG;YAClB,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK;YAC7C,UAAU,EAAE,YAAY,CAAC,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,KAAK;SAC1D,CAAC;QAEF,IAAI,MAAM,CAAC,SAAS,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI;YACxC,MAAM,CAAC,SAAS,CAAC,UAAU,GAAG,WAAW,CAAC,UAAU,EAAE,CAAC;YACzD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO;QACP,MAAM,CAAC,SAAS,CAAC,IAAI,IAAI,WAAW,CAAC,IAAI,CAAC;QAC1C,MAAM,CAAC,SAAS,CAAC,UAAU,IAAI,WAAW,CAAC,UAAU,CAAC;QAEtD,OAAO;QACP,QAAQ,CAAC,KAAK,EAAE,CAAC;QAEjB,OAAO,CAAC,GAAG,CAAC,MAAM,MAAM,CAAC,QAAQ,QAAQ,YAAY,CAAC,IAAI,MAAM,QAAQ,CAAC,KAAK,IAAI,CAAC,CAAC;QACpF,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,IAAU,EAAE,MAAc;QAClD,IAAI,SAAS,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,OAAO;QAEvC,QAAQ;QACR,MAAM,gBAAgB,GAAG;YACvB,QAAQ,EAAE,GAAG;YACb,MAAM,EAAE,GAAG;YACX,MAAM,EAAE,GAAG;YACX,WAAW,EAAE,GAAG;SACjB,CAAC;QACF,SAAS,IAAI,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC;QAElD,SAAS;QACT,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;QACzD,SAAS,IAAI,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC;QAEjC,SAAS;QACT,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QACtD,SAAS,IAAI,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC;QAElC,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,MAAc;QACtC,IAAI,QAAQ,GAAG,CAAC,CAAC;QAEjB,kBAAkB;QAClB,KAAK,MAAM,WAAW,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;YAC7C,mBAAmB;YACnB,gBAAgB;YAChB,QAAQ,GAAG,GAAG,CAAC;YACf,MAAM;QACR,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACI,gBAAgB,CACrB,YAAoB,EACpB,UAAkB,EAClB,QAAyC,EACzC,MAAc;QAEd,MAAM,OAAO,GAAG,SAAS,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QAEtC,MAAM,UAAU,GAAe;YAC7B,EAAE,EAAE,OAAO;YACX,IAAI,EAAE,YAAY;YAClB,EAAE,EAAE,UAAU;YACd,QAAQ;YACR,MAAM;YACN,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC,EAAE,SAAS;YACzC,QAAQ,EAAE,CAAC,CAAC,QAAQ;SACrB,CAAC;QAEF,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QAC1C,OAAO,CAAC,GAAG,CAAC,cAAc,YAAY,MAAM,UAAU,KAAK,QAAQ,KAAK,MAAM,GAAG,CAAC,CAAC;QAEnF,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,OAA4B;QACpD,KAAK,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YAChD,KAAK,CAAC,QAAQ,EAAE,CAAC;YAEjB,IAAI,KAAK,CAAC,QAAQ,IAAI,CAAC,EAAE,CAAC;gBACxB,OAAO;gBACP,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAC3C,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBAEvC,IAAI,UAAU,IAAI,QAAQ,EAAE,CAAC;oBAC3B,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;gBACtD,CAAC;gBAED,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACnC,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,UAAkB,EAAE,QAAgB,EAAE,KAAiB;QAC/E,QAAQ,KAAK,CAAC,QAAQ,EAAE,CAAC;YACvB,KAAK,MAAM;gBACT,IAAI,UAAU,CAAC,SAAS,CAAC,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;oBAC3D,UAAU,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;oBACzD,QAAQ,CAAC,SAAS,CAAC,IAAI,IAAI,KAAK,CAAC,MAAM,CAAC;gBAC1C,CAAC;gBACD,MAAM;YACR,KAAK,YAAY;gBACf,IAAI,UAAU,CAAC,SAAS,CAAC,UAAU,IAAI,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;oBACjE,UAAU,CAAC,SAAS,CAAC,UAAU,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;oBAC/D,QAAQ,CAAC,SAAS,CAAC,UAAU,IAAI,KAAK,CAAC,MAAM,CAAC;gBAChD,CAAC;gBACD,MAAM;YACR,KAAK,OAAO;gBACV,SAAS;gBACT,IAAI,UAAU,CAAC,cAAc,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;oBACrD,MAAM,WAAW,GAAG,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;oBACtE,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC;oBAC7C,UAAU,CAAC,SAAS,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC;gBAC1C,CAAC;gBACD,MAAM;QACV,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,WAAW,UAAU,CAAC,QAAQ,MAAM,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACI,qBAAqB,CAAC,MAAc;QACzC,IAAI,MAAM,GAAG,CAAC,CAAC;QAEf,OAAO;QACP,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC;QAChC,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,UAAU,GAAG,GAAG,CAAC;QAC5C,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC;QAE1C,OAAO;QACP,MAAM,IAAI,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,EAAE,CAAC;QAEzC,OAAO;QACP,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;YACzC,MAAM,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO;QAChE,CAAC;QAED,OAAO;QACP,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YACjC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC;QACnC,CAAC;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,MAAc;QAOpC,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAEvD,cAAc;QACd,MAAM,aAAa,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;QAEpD,eAAe;QACf,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;QAE1C,MAAM,SAAS,GAAG,aAAa,GAAG,QAAQ,CAAC;QAE3C,OAAO;QACP,MAAM,eAAe,GAAa,EAAE,CAAC;QACrC,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;YAClB,eAAe,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC3C,CAAC;QACD,IAAI,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACtC,CAAC;QACD,IAAI,MAAM,CAAC,SAAS,CAAC,IAAI,GAAG,GAAG,EAAE,CAAC;YAChC,eAAe,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC7C,CAAC;QAED,OAAO;YACL,WAAW;YACX,aAAa;YACb,QAAQ;YACR,SAAS;YACT,eAAe;SAChB,CAAC;IACJ,CAAC;CACF;AAnXD,sCAmXC"}