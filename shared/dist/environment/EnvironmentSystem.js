"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnvironmentSystem = exports.TERRAIN_EFFECTS = exports.WEATHER_EFFECTS = void 0;
const types_1 = require("../types");
// 天气效果数据
exports.WEATHER_EFFECTS = {
    [types_1.WeatherType.CLEAR]: {
        type: types_1.WeatherType.CLEAR,
        name: '晴天',
        description: '万里无云，适合行军作战',
        resourceMultiplier: { gold: 1.0, population: 1.0 },
        combatEffects: [],
        movementEffects: { costMultiplier: 1.0, rangeModifier: 0 },
        specialEffects: []
    },
    [types_1.WeatherType.RAIN]: {
        type: types_1.WeatherType.RAIN,
        name: '暴雨',
        description: '大雨滂沱，农业丰收但行军困难',
        resourceMultiplier: { gold: 1.2, population: 1.3 },
        combatEffects: [
            {
                type: 'debuff',
                value: -10,
                target: 'all',
                duration: -1
            }
        ],
        movementEffects: { costMultiplier: 1.5, rangeModifier: -1 },
        specialEffects: ['火系法术威力减半', '雷系法术威力翻倍']
    },
    [types_1.WeatherType.SANDSTORM]: {
        type: types_1.WeatherType.SANDSTORM,
        name: '沙暴',
        description: '黄沙漫天，视野受限',
        resourceMultiplier: { gold: 0.7, population: 0.8 },
        combatEffects: [
            {
                type: 'debuff',
                value: -20,
                target: 'archer',
                duration: -1
            }
        ],
        movementEffects: { costMultiplier: 1.3, rangeModifier: -1 },
        specialEffects: ['弓兵射程减半', '所有单位命中率下降']
    },
    [types_1.WeatherType.AURORA]: {
        type: types_1.WeatherType.AURORA,
        name: '极光',
        description: '神秘极光，魔法力量增强',
        resourceMultiplier: { gold: 1.1, population: 0.9 },
        combatEffects: [
            {
                type: 'buff',
                value: 30,
                target: 'mage',
                duration: -1
            }
        ],
        movementEffects: { costMultiplier: 1.0, rangeModifier: 0 },
        specialEffects: ['法师能量恢复速度翻倍', '所有法术威力+50%']
    },
    [types_1.WeatherType.WINTER]: {
        type: types_1.WeatherType.WINTER,
        name: '严冬',
        description: '天寒地冻，补给困难',
        resourceMultiplier: { gold: 0.6, population: 0.7 },
        combatEffects: [
            {
                type: 'debuff',
                value: -15,
                target: 'all',
                duration: -1
            }
        ],
        movementEffects: { costMultiplier: 2.0, rangeModifier: -1 },
        specialEffects: ['所有单位每回合损失5%生命值', '冰系法术威力翻倍']
    },
    [types_1.WeatherType.THUNDER]: {
        type: types_1.WeatherType.THUNDER,
        name: '雷暴',
        description: '雷电交加，战场混乱',
        resourceMultiplier: { gold: 0.9, population: 0.8 },
        combatEffects: [
            {
                type: 'damage',
                value: 20,
                target: 'all',
                duration: 1
            }
        ],
        movementEffects: { costMultiplier: 1.2, rangeModifier: 0 },
        specialEffects: ['随机单位受到雷击伤害', '雷系法术威力翻倍']
    }
};
// 地形效果数据
exports.TERRAIN_EFFECTS = {
    [types_1.TerrainType.PLAIN]: {
        type: types_1.TerrainType.PLAIN,
        name: '平原',
        description: '开阔平地，适合大军团作战',
        movementCost: 1,
        defensiveBonus: 0,
        resourceProduction: { gold: 2, population: 3 },
        combatModifiers: { attackBonus: 0, defenseBonus: 0, speedModifier: 1.1 },
        specialProperties: ['骑兵攻击力+20%', '大型军团无移动惩罚']
    },
    [types_1.TerrainType.MOUNTAIN]: {
        type: types_1.TerrainType.MOUNTAIN,
        name: '山脉',
        description: '险峻山峰，易守难攻',
        movementCost: 3,
        defensiveBonus: 30,
        resourceProduction: { gold: 3, population: 1 },
        combatModifiers: { attackBonus: -10, defenseBonus: 40, speedModifier: 0.7 },
        specialProperties: ['不可通行（除非有特殊技能）', '弓兵射程+1', '法师法术威力+20%']
    },
    [types_1.TerrainType.FOREST]: {
        type: types_1.TerrainType.FOREST,
        name: '森林',
        description: '茂密丛林，适合伏击',
        movementCost: 2,
        defensiveBonus: 15,
        resourceProduction: { gold: 1, population: 2 },
        combatModifiers: { attackBonus: 10, defenseBonus: 20, speedModifier: 0.9 },
        specialProperties: ['步兵隐蔽+30%', '火系法术易引发森林火灾', '弓兵攻击力+15%']
    },
    [types_1.TerrainType.RIVER]: {
        type: types_1.TerrainType.RIVER,
        name: '河流',
        description: '奔腾河水，水战要地',
        movementCost: 2,
        defensiveBonus: 10,
        resourceProduction: { gold: 2, population: 2 },
        combatModifiers: { attackBonus: 0, defenseBonus: 15, speedModifier: 0.8 },
        specialProperties: ['吴国武将攻击力+25%', '火系法术威力减半', '需要渡河技能']
    },
    [types_1.TerrainType.DESERT]: {
        type: types_1.TerrainType.DESERT,
        name: '沙漠',
        description: '荒芜沙地，补给困难',
        movementCost: 2,
        defensiveBonus: 5,
        resourceProduction: { gold: 1, population: 1 },
        combatModifiers: { attackBonus: -5, defenseBonus: 5, speedModifier: 0.8 },
        specialProperties: ['所有单位每回合消耗额外补给', '沙暴天气效果翻倍']
    },
    [types_1.TerrainType.SWAMP]: {
        type: types_1.TerrainType.SWAMP,
        name: '沼泽',
        description: '泥泞湿地，行动缓慢',
        movementCost: 3,
        defensiveBonus: 20,
        resourceProduction: { gold: 1, population: 1 },
        combatModifiers: { attackBonus: -15, defenseBonus: 25, speedModifier: 0.6 },
        specialProperties: ['骑兵无法进入', '毒系法术威力翻倍', '移动速度大幅降低']
    },
    [types_1.TerrainType.CRYSTAL]: {
        type: types_1.TerrainType.CRYSTAL,
        name: '水晶矿脉',
        description: '神秘水晶，蕴含魔法能量',
        movementCost: 1,
        defensiveBonus: 10,
        resourceProduction: { gold: 5, population: 1 },
        combatModifiers: { attackBonus: 0, defenseBonus: 10, speedModifier: 1.0 },
        specialProperties: ['法师能量恢复速度翻倍', '所有法术威力+30%', '高价值战略资源']
    },
    [types_1.TerrainType.SCORCHED]: {
        type: types_1.TerrainType.SCORCHED,
        name: '焦土',
        description: '战火焚烧的废土',
        movementCost: 1,
        defensiveBonus: -10,
        resourceProduction: { gold: 0, population: 0 },
        combatModifiers: { attackBonus: 0, defenseBonus: -10, speedModifier: 1.0 },
        specialProperties: ['无法产出资源', '火系法术威力+50%', '所有单位士气-10']
    }
};
/**
 * 环境系统管理器
 */
class EnvironmentSystem {
    constructor() {
        this.currentWeather = types_1.WeatherType.CLEAR;
        this.weatherDuration = 0;
    }
    /**
     * 设置当前天气
     */
    setWeather(weather, duration = 5) {
        this.currentWeather = weather;
        this.weatherDuration = duration;
        console.log(`🌦️ 天气变化: ${exports.WEATHER_EFFECTS[weather].name} (持续${duration}回合)`);
    }
    /**
     * 更新天气（每回合调用）
     */
    updateWeather() {
        if (this.weatherDuration > 0) {
            this.weatherDuration--;
            if (this.weatherDuration === 0) {
                this.currentWeather = types_1.WeatherType.CLEAR;
                console.log('🌤️ 天气转晴');
            }
        }
    }
    /**
     * 获取当前天气效果
     */
    getCurrentWeatherEffect() {
        return exports.WEATHER_EFFECTS[this.currentWeather];
    }
    /**
     * 获取地形效果
     */
    getTerrainEffect(terrain) {
        return exports.TERRAIN_EFFECTS[terrain];
    }
    /**
     * 计算移动成本
     */
    calculateMovementCost(terrain, weather) {
        const terrainEffect = this.getTerrainEffect(terrain);
        const weatherEffect = weather ? exports.WEATHER_EFFECTS[weather] : this.getCurrentWeatherEffect();
        return Math.ceil(terrainEffect.movementCost * weatherEffect.movementEffects.costMultiplier);
    }
    /**
     * 应用环境效果到英雄
     */
    applyEnvironmentEffects(hero, terrain) {
        const modifiedHero = { ...hero };
        const terrainEffect = this.getTerrainEffect(terrain);
        const weatherEffect = this.getCurrentWeatherEffect();
        // 应用地形战斗修正
        modifiedHero.stats.attack += terrainEffect.combatModifiers.attackBonus;
        modifiedHero.stats.armor += terrainEffect.combatModifiers.defenseBonus;
        modifiedHero.stats.speed *= terrainEffect.combatModifiers.speedModifier;
        // 应用天气效果
        for (const effect of weatherEffect.combatEffects) {
            if (this.shouldApplyWeatherEffect(effect, hero)) {
                switch (effect.type) {
                    case 'buff':
                        modifiedHero.stats.attack += effect.value || 0;
                        break;
                    case 'debuff':
                        modifiedHero.stats.attack -= Math.abs(effect.value || 0);
                        break;
                    case 'damage':
                        modifiedHero.stats.health -= effect.value || 0;
                        break;
                }
            }
        }
        return modifiedHero;
    }
    /**
     * 检查是否应该应用天气效果
     */
    shouldApplyWeatherEffect(effect, hero) {
        switch (effect.target) {
            case 'all':
                return true;
            case 'archer':
                return hero.unitType === 'archer';
            case 'mage':
                return hero.unitType === 'mage';
            default:
                return false;
        }
    }
    /**
     * 计算资源产出修正
     */
    calculateResourceProduction(terrain, baseProduction) {
        const terrainEffect = this.getTerrainEffect(terrain);
        const weatherEffect = this.getCurrentWeatherEffect();
        return {
            gold: Math.floor((baseProduction.gold + terrainEffect.resourceProduction.gold) *
                weatherEffect.resourceMultiplier.gold),
            population: Math.floor((baseProduction.population + terrainEffect.resourceProduction.population) *
                weatherEffect.resourceMultiplier.population)
        };
    }
    /**
     * 检查地形特殊效果
     */
    checkTerrainSpecialEffects(terrain, action) {
        const terrainEffect = this.getTerrainEffect(terrain);
        switch (action) {
            case 'cavalry_enter':
                return !terrainEffect.specialProperties.includes('骑兵无法进入');
            case 'fire_spell':
                return terrainEffect.specialProperties.includes('火系法术易引发森林火灾');
            case 'wu_bonus':
                return terrainEffect.specialProperties.includes('吴国武将攻击力+25%');
            default:
                return false;
        }
    }
    /**
     * 获取环境描述
     */
    getEnvironmentDescription() {
        const weatherEffect = this.getCurrentWeatherEffect();
        return `当前天气：${weatherEffect.name} - ${weatherEffect.description}`;
    }
    /**
     * 预测天气变化
     */
    generateWeatherForecast(turns = 5) {
        const forecast = [];
        const weatherTypes = Object.values(types_1.WeatherType);
        for (let i = 0; i < turns; i++) {
            // 简单的天气预测算法
            const randomIndex = Math.floor(Math.random() * weatherTypes.length);
            forecast.push(weatherTypes[randomIndex]);
        }
        return forecast;
    }
}
exports.EnvironmentSystem = EnvironmentSystem;
//# sourceMappingURL=EnvironmentSystem.js.map