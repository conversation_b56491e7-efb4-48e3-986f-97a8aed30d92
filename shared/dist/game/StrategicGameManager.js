"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StrategicGameManager = void 0;
const types_1 = require("../types");
const HexMap_1 = require("../map/HexMap");
/**
 * 战略游戏管理器
 * 负责管理SLG层面的游戏逻辑，包括回合制、资源管理、军队移动等
 */
class StrategicGameManager {
    constructor(room) {
        this.room = room;
        this.hexMap = new HexMap_1.HexMap();
        this.initializeGame();
    }
    /**
     * 初始化游戏
     */
    initializeGame() {
        // 初始化地图
        this.room.map = this.hexMap.getAllTiles();
        // 为每个玩家分配起始位置
        this.assignStartingPositions();
        // 设置初始天气
        this.room.weather = {
            current: types_1.WeatherType.CLEAR,
            forecast: this.generateWeatherForecast()
        };
        // 开始第一回合
        this.room.currentTurn = 1;
        this.room.strategicPhase = types_1.StrategicPhase.RESOURCE_COLLECTION;
        this.room.gameState = types_1.GameState.STRATEGIC;
    }
    /**
     * 分配起始位置
     */
    assignStartingPositions() {
        const mapSize = this.hexMap.getSize();
        const playerCount = this.room.players.length;
        // 在地图边缘均匀分布玩家起始位置
        for (let i = 0; i < playerCount; i++) {
            const player = this.room.players[i];
            const angle = (i / playerCount) * 2 * Math.PI;
            const radius = Math.min(mapSize.width, mapSize.height) * 0.4;
            const startX = Math.round(mapSize.width / 2 + Math.cos(angle) * radius);
            const startY = Math.round(mapSize.height / 2 + Math.sin(angle) * radius);
            const startPosition = { x: startX, y: startY };
            // 确保起始位置是可用的
            const tile = this.hexMap.getTile(startPosition);
            if (tile && tile.terrainType !== types_1.TerrainType.MOUNTAIN) {
                // 占领起始地块
                tile.ownerId = player.id;
                player.territories.push(tile.id);
                // 创建初始军队
                const initialArmy = {
                    id: `army_${player.id}_1`,
                    ownerId: player.id,
                    name: `${player.username}的主力军`,
                    position: startPosition,
                    cards: [], // 初始卡牌将在后续添加
                    fatigue: 0,
                    morale: 100
                };
                player.armies.push(initialArmy);
                tile.units = [initialArmy];
                // 设置初始资源
                player.resources = {
                    gold: types_1.GAME_CONFIG.INITIAL_GOLD,
                    population: types_1.GAME_CONFIG.INITIAL_POPULATION,
                    experience: 0
                };
                player.backpackCapacity = types_1.GAME_CONFIG.INITIAL_BACKPACK_CAPACITY;
            }
        }
    }
    /**
     * 生成天气预报
     */
    generateWeatherForecast() {
        const forecast = [];
        const weatherTypes = Object.values(types_1.WeatherType);
        for (let i = 0; i < 5; i++) {
            const randomWeather = weatherTypes[Math.floor(Math.random() * weatherTypes.length)];
            forecast.push(randomWeather);
        }
        return forecast;
    }
    /**
     * 开始新回合
     */
    startTurn() {
        console.log(`开始第 ${this.room.currentTurn} 回合`);
        // 资源收集阶段
        this.collectResources();
        // 更新天气
        this.updateWeather();
        // 设置回合阶段
        this.room.strategicPhase = types_1.StrategicPhase.PLAYER_ACTION;
        // 重置玩家行动状态
        this.resetPlayerActions();
        // 设置回合计时器
        this.startTurnTimer();
    }
    /**
     * 资源收集
     */
    collectResources() {
        for (const player of this.room.players) {
            if (!player.isAlive)
                continue;
            let goldIncome = 0;
            let populationIncome = 0;
            // 从控制的地块收集资源
            for (const territoryId of player.territories) {
                const tile = this.room.map.find(t => t.id === territoryId);
                if (tile && tile.ownerId === player.id) {
                    goldIncome += tile.resources.gold;
                    populationIncome += tile.resources.population;
                    // 建筑加成
                    if (tile.buildings) {
                        for (const building of tile.buildings) {
                            for (const effect of building.effects) {
                                if (effect.type === 'resource_bonus') {
                                    goldIncome += effect.value;
                                    populationIncome += effect.value;
                                }
                            }
                        }
                    }
                }
            }
            // 天气影响
            const weatherMultiplier = this.getWeatherResourceMultiplier();
            goldIncome = Math.floor(goldIncome * weatherMultiplier);
            populationIncome = Math.floor(populationIncome * weatherMultiplier);
            // 更新玩家资源
            player.resources.gold += goldIncome;
            player.resources.population += populationIncome;
            console.log(`${player.username} 收集资源: +${goldIncome} 金币, +${populationIncome} 人口`);
        }
    }
    /**
     * 获取天气对资源的影响倍数
     */
    getWeatherResourceMultiplier() {
        switch (this.room.weather.current) {
            case types_1.WeatherType.CLEAR:
                return 1.0;
            case types_1.WeatherType.RAIN:
                return 1.2; // 雨天农业产出增加
            case types_1.WeatherType.SANDSTORM:
                return 0.8; // 沙暴减少产出
            case types_1.WeatherType.WINTER:
                return 0.7; // 严冬大幅减少产出
            case types_1.WeatherType.AURORA:
                return 1.5; // 极光增加魔法资源
            default:
                return 1.0;
        }
    }
    /**
     * 更新天气
     */
    updateWeather() {
        // 每5回合更新一次天气
        if (this.room.currentTurn % 5 === 0) {
            const forecast = this.room.weather.forecast;
            if (forecast.length > 0) {
                this.room.weather.current = forecast.shift();
                forecast.push(this.generateRandomWeather());
            }
            console.log(`天气变化: ${this.room.weather.current}`);
        }
    }
    /**
     * 生成随机天气
     */
    generateRandomWeather() {
        const weatherTypes = Object.values(types_1.WeatherType);
        return weatherTypes[Math.floor(Math.random() * weatherTypes.length)];
    }
    /**
     * 重置玩家行动状态
     */
    resetPlayerActions() {
        for (const player of this.room.players) {
            // 重置军队疲劳度
            for (const army of player.armies) {
                army.fatigue = Math.max(0, army.fatigue - 10);
            }
            // 减少技能冷却
            for (const skillId in player.skillCooldowns) {
                player.skillCooldowns[skillId] = Math.max(0, player.skillCooldowns[skillId] - 1);
            }
        }
    }
    /**
     * 开始回合计时器
     */
    startTurnTimer() {
        if (this.turnTimer) {
            clearTimeout(this.turnTimer);
        }
        this.turnTimer = setTimeout(() => {
            this.endTurn();
        }, types_1.GAME_CONFIG.TURN_TIME_LIMIT);
    }
    /**
     * 结束回合
     */
    endTurn() {
        if (this.turnTimer) {
            clearTimeout(this.turnTimer);
        }
        console.log(`第 ${this.room.currentTurn} 回合结束`);
        // 处理回合结束事件
        this.room.strategicPhase = types_1.StrategicPhase.TURN_END;
        // 检查游戏结束条件
        if (this.checkGameEndConditions()) {
            this.endGame();
            return;
        }
        // 进入下一回合
        this.room.currentTurn++;
        // 检查最大回合数
        if (this.room.currentTurn > types_1.GAME_CONFIG.MAX_TURNS) {
            this.endGame();
            return;
        }
        // 开始新回合
        setTimeout(() => {
            this.startTurn();
        }, 1000);
    }
    /**
     * 检查游戏结束条件
     */
    checkGameEndConditions() {
        const alivePlayers = this.room.players.filter(p => p.isAlive);
        // 只剩一个玩家
        if (alivePlayers.length <= 1) {
            return true;
        }
        // 检查身份胜利条件
        // TODO: 实现具体的身份胜利逻辑
        return false;
    }
    /**
     * 结束游戏
     */
    endGame() {
        this.room.gameState = types_1.GameState.GAME_END;
        console.log('游戏结束');
        // TODO: 计算最终得分和排名
    }
    /**
     * 移动军队
     */
    moveArmy(playerId, armyId, targetPosition) {
        const player = this.room.players.find(p => p.id === playerId);
        if (!player || !player.isAlive)
            return false;
        const army = player.armies.find(a => a.id === armyId);
        if (!army)
            return false;
        // 检查是否相邻
        if (!this.hexMap.areAdjacent(army.position, targetPosition)) {
            return false;
        }
        // 检查疲劳度
        if (army.fatigue >= 100) {
            return false;
        }
        const targetTile = this.hexMap.getTile(targetPosition);
        if (!targetTile)
            return false;
        // 检查地形是否可通行
        if (targetTile.terrainType === types_1.TerrainType.MOUNTAIN) {
            return false;
        }
        // 移除原位置的军队
        const oldTile = this.hexMap.getTile(army.position);
        if (oldTile && oldTile.units) {
            oldTile.units = oldTile.units.filter(u => u.id !== armyId);
        }
        // 更新军队位置
        army.position = targetPosition;
        army.fatigue += 20; // 增加疲劳度
        // 添加到新位置
        if (!targetTile.units) {
            targetTile.units = [];
        }
        targetTile.units.push(army);
        // 检查是否触发战斗
        if (targetTile.units.length > 1) {
            this.triggerCombat(targetPosition);
        }
        return true;
    }
    /**
     * 触发战斗
     */
    triggerCombat(position) {
        console.log(`在位置 (${position.x}, ${position.y}) 触发战斗`);
        // 切换到战斗状态
        this.room.gameState = types_1.GameState.COMBAT_INIT;
        // TODO: 实现战斗逻辑
    }
    /**
     * 获取游戏状态
     */
    getGameState() {
        return this.room;
    }
    /**
     * 获取地图
     */
    getMap() {
        return this.hexMap;
    }
}
exports.StrategicGameManager = StrategicGameManager;
//# sourceMappingURL=StrategicGameManager.js.map