{"version": 3, "file": "StrategicGameManager.js", "sourceRoot": "", "sources": ["../../src/game/StrategicGameManager.ts"], "names": [], "mappings": ";;;AAAA,oCAWkB;AAClB,0CAAuC;AAEvC;;;GAGG;AACH,MAAa,oBAAoB;IAK/B,YAAY,IAAc;QACxB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG,IAAI,eAAM,EAAE,CAAC;QAC3B,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAED;;OAEG;IACK,cAAc;QACpB,QAAQ;QACR,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;QAE1C,cAAc;QACd,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAE/B,SAAS;QACT,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG;YAClB,OAAO,EAAE,mBAAW,CAAC,KAAK;YAC1B,QAAQ,EAAE,IAAI,CAAC,uBAAuB,EAAE;SACzC,CAAC;QAEF,SAAS;QACT,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;QAC1B,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,sBAAc,CAAC,mBAAmB,CAAC;QAC9D,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,iBAAS,CAAC,SAAS,CAAC;IAC5C,CAAC;IAED;;OAEG;IACK,uBAAuB;QAC7B,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QACtC,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QAE7C,kBAAkB;QAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC;YACrC,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,KAAK,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;YAC9C,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC;YAE7D,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC;YACxE,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC;YAEzE,MAAM,aAAa,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC;YAE/C,aAAa;YACb,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YAChD,IAAI,IAAI,IAAI,IAAI,CAAC,WAAW,KAAK,mBAAW,CAAC,QAAQ,EAAE,CAAC;gBACtD,SAAS;gBACT,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,EAAE,CAAC;gBACzB,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAEjC,SAAS;gBACT,MAAM,WAAW,GAAS;oBACxB,EAAE,EAAE,QAAQ,MAAM,CAAC,EAAE,IAAI;oBACzB,OAAO,EAAE,MAAM,CAAC,EAAE;oBAClB,IAAI,EAAE,GAAG,MAAM,CAAC,QAAQ,MAAM;oBAC9B,QAAQ,EAAE,aAAa;oBACvB,KAAK,EAAE,EAAE,EAAE,aAAa;oBACxB,OAAO,EAAE,CAAC;oBACV,MAAM,EAAE,GAAG;iBACZ,CAAC;gBAEF,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAChC,IAAI,CAAC,KAAK,GAAG,CAAC,WAAW,CAAC,CAAC;gBAE3B,SAAS;gBACT,MAAM,CAAC,SAAS,GAAG;oBACjB,IAAI,EAAE,mBAAW,CAAC,YAAY;oBAC9B,UAAU,EAAE,mBAAW,CAAC,kBAAkB;oBAC1C,UAAU,EAAE,CAAC;iBACd,CAAC;gBAEF,MAAM,CAAC,gBAAgB,GAAG,mBAAW,CAAC,yBAAyB,CAAC;YAClE,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,uBAAuB;QAC7B,MAAM,QAAQ,GAAkB,EAAE,CAAC;QACnC,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAW,CAAC,CAAC;QAEhD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC3B,MAAM,aAAa,GAAG,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;YACpF,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC/B,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACI,SAAS;QACd,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,KAAK,CAAC,CAAC;QAE/C,SAAS;QACT,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,OAAO;QACP,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,SAAS;QACT,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,sBAAc,CAAC,aAAa,CAAC;QAExD,WAAW;QACX,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,UAAU;QACV,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAED;;OAEG;IACK,gBAAgB;QACtB,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACvC,IAAI,CAAC,MAAM,CAAC,OAAO;gBAAE,SAAS;YAE9B,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,IAAI,gBAAgB,GAAG,CAAC,CAAC;YAEzB,aAAa;YACb,KAAK,MAAM,WAAW,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;gBAC7C,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,WAAW,CAAC,CAAC;gBAC3D,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,MAAM,CAAC,EAAE,EAAE,CAAC;oBACvC,UAAU,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;oBAClC,gBAAgB,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;oBAE9C,OAAO;oBACP,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;wBACnB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;4BACtC,KAAK,MAAM,MAAM,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gCACtC,IAAI,MAAM,CAAC,IAAI,KAAK,gBAAgB,EAAE,CAAC;oCACrC,UAAU,IAAI,MAAM,CAAC,KAAK,CAAC;oCAC3B,gBAAgB,IAAI,MAAM,CAAC,KAAK,CAAC;gCACnC,CAAC;4BACH,CAAC;wBACH,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO;YACP,MAAM,iBAAiB,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;YAC9D,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,iBAAiB,CAAC,CAAC;YACxD,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,iBAAiB,CAAC,CAAC;YAEpE,SAAS;YACT,MAAM,CAAC,SAAS,CAAC,IAAI,IAAI,UAAU,CAAC;YACpC,MAAM,CAAC,SAAS,CAAC,UAAU,IAAI,gBAAgB,CAAC;YAEhD,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,QAAQ,WAAW,UAAU,SAAS,gBAAgB,KAAK,CAAC,CAAC;QACrF,CAAC;IACH,CAAC;IAED;;OAEG;IACK,4BAA4B;QAClC,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YAClC,KAAK,mBAAW,CAAC,KAAK;gBACpB,OAAO,GAAG,CAAC;YACb,KAAK,mBAAW,CAAC,IAAI;gBACnB,OAAO,GAAG,CAAC,CAAC,WAAW;YACzB,KAAK,mBAAW,CAAC,SAAS;gBACxB,OAAO,GAAG,CAAC,CAAC,SAAS;YACvB,KAAK,mBAAW,CAAC,MAAM;gBACrB,OAAO,GAAG,CAAC,CAAC,WAAW;YACzB,KAAK,mBAAW,CAAC,MAAM;gBACrB,OAAO,GAAG,CAAC,CAAC,WAAW;YACzB;gBACE,OAAO,GAAG,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACK,aAAa;QACnB,aAAa;QACb,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YACpC,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC5C,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,QAAQ,CAAC,KAAK,EAAG,CAAC;gBAC9C,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC,CAAC;YAC9C,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,qBAAqB;QAC3B,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAW,CAAC,CAAC;QAChD,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACK,kBAAkB;QACxB,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACvC,UAAU;YACV,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBACjC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC;YAChD,CAAC;YAED,SAAS;YACT,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;gBAC5C,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;YACnF,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,cAAc;QACpB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC/B,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;YAC/B,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,CAAC,EAAE,mBAAW,CAAC,eAAe,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACI,OAAO;QACZ,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC/B,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,WAAW,OAAO,CAAC,CAAC;QAE/C,WAAW;QACX,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,sBAAc,CAAC,QAAQ,CAAC;QAEnD,WAAW;QACX,IAAI,IAAI,CAAC,sBAAsB,EAAE,EAAE,CAAC;YAClC,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,OAAO;QACT,CAAC;QAED,SAAS;QACT,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;QAExB,UAAU;QACV,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,mBAAW,CAAC,SAAS,EAAE,CAAC;YAClD,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,OAAO;QACT,CAAC;QAED,QAAQ;QACR,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,CAAC,SAAS,EAAE,CAAC;QACnB,CAAC,EAAE,IAAI,CAAC,CAAC;IACX,CAAC;IAED;;OAEG;IACK,sBAAsB;QAC5B,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QAE9D,SAAS;QACT,IAAI,YAAY,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,WAAW;QACX,oBAAoB;QAEpB,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,OAAO;QACb,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,iBAAS,CAAC,QAAQ,CAAC;QACzC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAEpB,kBAAkB;IACpB,CAAC;IAED;;OAEG;IACI,QAAQ,CAAC,QAAgB,EAAE,MAAc,EAAE,cAAwB;QACxE,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAC;QAC9D,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO;YAAE,OAAO,KAAK,CAAC;QAE7C,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,CAAC;QACtD,IAAI,CAAC,IAAI;YAAE,OAAO,KAAK,CAAC;QAExB,SAAS;QACT,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,cAAc,CAAC,EAAE,CAAC;YAC5D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,QAAQ;QACR,IAAI,IAAI,CAAC,OAAO,IAAI,GAAG,EAAE,CAAC;YACxB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QACvD,IAAI,CAAC,UAAU;YAAE,OAAO,KAAK,CAAC;QAE9B,YAAY;QACZ,IAAI,UAAU,CAAC,WAAW,KAAK,mBAAW,CAAC,QAAQ,EAAE,CAAC;YACpD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,WAAW;QACX,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACnD,IAAI,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;YAC7B,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,CAAC;QAC7D,CAAC;QAED,SAAS;QACT,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC;QAC/B,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC,QAAQ;QAE5B,SAAS;QACT,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YACtB,UAAU,CAAC,KAAK,GAAG,EAAE,CAAC;QACxB,CAAC;QACD,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE5B,WAAW;QACX,IAAI,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;QACrC,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,QAAkB;QACtC,OAAO,CAAC,GAAG,CAAC,QAAQ,QAAQ,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;QAEvD,UAAU;QACV,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,iBAAS,CAAC,WAAW,CAAC;QAE5C,eAAe;IACjB,CAAC;IAED;;OAEG;IACI,YAAY;QACjB,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;CACF;AAzXD,oDAyXC"}