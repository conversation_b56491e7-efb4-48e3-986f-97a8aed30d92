import { GameRoom, Position } from '../types';
import { HexMap } from '../map/HexMap';
/**
 * 战略游戏管理器
 * 负责管理SLG层面的游戏逻辑，包括回合制、资源管理、军队移动等
 */
export declare class StrategicGameManager {
    private room;
    private hexMap;
    private turnTimer?;
    constructor(room: GameRoom);
    /**
     * 初始化游戏
     */
    private initializeGame;
    /**
     * 分配起始位置
     */
    private assignStartingPositions;
    /**
     * 生成天气预报
     */
    private generateWeatherForecast;
    /**
     * 开始新回合
     */
    startTurn(): void;
    /**
     * 资源收集
     */
    private collectResources;
    /**
     * 获取天气对资源的影响倍数
     */
    private getWeatherResourceMultiplier;
    /**
     * 更新天气
     */
    private updateWeather;
    /**
     * 生成随机天气
     */
    private generateRandomWeather;
    /**
     * 重置玩家行动状态
     */
    private resetPlayerActions;
    /**
     * 开始回合计时器
     */
    private startTurnTimer;
    /**
     * 结束回合
     */
    endTurn(): void;
    /**
     * 检查游戏结束条件
     */
    private checkGameEndConditions;
    /**
     * 结束游戏
     */
    private endGame;
    /**
     * 移动军队
     */
    moveArmy(playerId: string, armyId: string, targetPosition: Position): boolean;
    /**
     * 触发战斗
     */
    private triggerCombat;
    /**
     * 获取游戏状态
     */
    getGameState(): GameRoom;
    /**
     * 获取地图
     */
    getMap(): HexMap;
}
//# sourceMappingURL=StrategicGameManager.d.ts.map