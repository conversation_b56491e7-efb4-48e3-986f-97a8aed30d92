# 游戏设计文档

## 1. 游戏概述与核心理念

### 1.1 游戏名称

待定（建议根据剧本系统和核心玩法进行命名，例如《三国：棋弈天下》、《元素战场：策略对决》等）

### 1.2 游戏类型

本游戏是一款融合了 **战略模拟（SLG）** 与 **自走棋（Auto Chess）** 玩法的创新型混合策略游戏。玩家将在宏大的战略地图上进行资源争夺、领土扩张，并在局部冲突中通过部署自走棋队伍进行实时策略战斗。

### 1.3 核心体验

游戏旨在提供**宏观战略布局**与**微观战术博弈**并重的深度体验。通过独特的“嵌套回合制”和“事件驱动”机制，确保玩家在享受大地图策略乐趣的同时，也能体验到自走棋战斗的紧张与刺激。游戏节奏快，每局对战控制在1-2小时内，适合8人同时竞技，并支持轻量化竞技场或赛季制玩法。

“同时行动回合制”或“实时回合制”（Simultaneous Turn Resolution），不过更常见的是将其称为“We-Go”系统。在这种系统中，所有玩家在同一回合内提交他们的行动指令，然后在回合结束时所有行动同时结算


### 1.4 独特卖点

- **SLG与自走棋的无缝融合**：创新性地将两种热门玩法结合，通过“战略指令卡”等机制实现宏观与微观层面的深度联动，每次局部战斗的胜利都可能引发全局战略的连锁反应。
- **剧本系统**：类似于别的游戏的地图，提供多样化的游戏背景、初始设定和特殊规则，极大地增加了游戏的可玩性和重玩价值，例如三国、中世纪、魔幻等不同主题的剧本。
- **快节奏8人对战**：优化游戏流程，确保每局游戏在有限时间内完成，提供紧张刺激的多人竞技体验。
- **卡牌驱动的核心玩法**：英雄卡、法术卡和战略指令卡贯穿游戏始终，是玩家策略选择和成长的核心。
- **动态环境与身份博弈**：天气系统和身份系统为游戏增加了更多不确定性和策略深度，玩家需要根据环境变化和身份目标灵活调整战术。

### 1.5 目标用户

- 喜爱策略游戏，尤其是SLG和自走棋玩法的玩家。
- 寻求创新游戏体验，不满足于单一玩法的玩家。
- 偏好快节奏、高对抗性多人竞技的玩家。
- 乐于钻研战术、享受深度策略博弈的玩家。

### 1.6 游戏愿景

打造一款兼具策略深度、战术广度与高竞技性的混合型策略游戏，让玩家在每一次决策中都能感受到宏观与微观策略的完美结合，并通过持续更新的剧本和卡牌内容，保持游戏的长期活力和吸引力。

---




## 2. SLG与自走棋混合机制

本游戏的核心创新在于将宏观战略模拟（SLG）与微观自走棋战斗机制进行了深度融合。这种融合并非简单的叠加，而是通过一套精妙的“嵌套回合制”和“事件驱动”系统，以及贯穿始终的“战略指令卡”，实现了两者之间的有机联动和相互影响。

### 2.1 嵌套回合制与状态机

游戏运行在一个**嵌套回合制（Nested Turn-Based System）**的框架下。整个游戏流程由一个宏大的**主游戏循环（Strategic World Map）**驱动，玩家在世界地图上进行战略操作。

我们可以将游戏的运行流程理解为一个**状态机（State Machine）**。核心状态包括：

- **战略模式（Strategic Mode）**：默认状态，玩家在世界地图上进行部队移动、地块占领、资源管理等宏观操作。每个主回合玩家可执行一个主要战略行动。
- **战斗模式（Combat Mode）**：当冲突事件（如两支部队在同一地块相遇）发生时激活。游戏状态从“战略模式”切换到“战斗模式”，玩家进入自走棋战斗的步数界面，进行单位部署。
- **结算模式（Resolution Mode）**：自走棋战斗结束后激活。系统自动处理战斗胜负、地块归属、单位损耗、奖励分配等，并将结果反馈到战略地图。之后，游戏状态切换回“战略模式”，继续主游戏流程。

### 2.2 事件驱动的游戏循环

主游戏循环持续监听特定的**事件（Event-driven Game Loop）**。一旦这些事件被触发，系统将调用相应的子系统来处理。例如：

- **冲突触发（Conflict Trigger）**：当两支（或更多）玩家军队在同一战略回合内，移动到同一个地块，或尝试占领同一地块时，系统将判定为冲突发生，并立即进入“战斗模式”。
- **PVE触发**：玩家部队移动到包含NPC（如黄巾军）的地块时，触发PVE自走棋战斗。
- **空投/副本事件**：地图上随机出现高价值卡牌的空投点，或特殊副本入口，玩家派遣部队前往则触发相应的自走棋战斗。

### 2.3 宏观战略层：世界地图与资源管理

#### 2.3.1 地图构成

世界地图由多个地图块组成。每个地块拥有独特的属性，这些属性直接影响玩家的战略决策和战斗表现：

- **资源产出**：地块可以提供金币、法术卡片、英雄棋子等资源，是玩家经济和发展的基石。不同类型的地块产出不同，例如，矿场产出金币。
- **地形效果**：地块的地形类型（如山脉、森林、河流、平原、焦土、水晶矿脉等）会为驻扎或经过的部队提供增益或减益效果，并动态影响自走棋战斗棋盘的地形元素。例如，山脉提供防御加成，河流减缓移动速度。


#### 2.3.2 玩家行动（每战略回合）

每回合只能指定部队移动到相邻的地块，但是到自己已经占领的地块，则可以直接分配战斗的部队：

- **移动与占领**：玩家派遣军队移动到相邻地块。如果目标地块为空或中立，则部队自动占领并获得该地块的控制权；如果目标地块为敌方所有，则触发自走棋战斗。

购买物品则是无限制的刷新，每一次刷新消耗一定的金币

- **召唤/招募（商店）**：玩家可以在特定地块或固定商店使用金币资源来购买新的英雄棋子、普通单位或法术卡。这相当于自走棋的“商店”和“备战区”，玩家可以刷新商店以获取不同的卡牌选择。

#### 2.3.3 地块归属与战斗触发

- **战斗触发机制**：当两个（或更多）玩家的军队在同一战略回合内，移动到同一个地块时，系统将判定为冲突发生，并立即进入“战斗阶段”。PVE战斗则在玩家部队进入NPC占领地块时触发。
- **地块归属判定**：自走棋战斗结束后，胜利方将获得该地块的控制权。

### 2.4 微观战术层：自走棋战斗机制

自走棋战斗是游戏的核心微观玩法，它是一个独立的、基于自走棋逻辑的回合制流程，拥有自己独特的规则、单位部署和胜负判定机制。战斗结果将直接影响战略地图上的地块归属和玩家的资源状况。

#### 2.4.1 战斗棋盘

- **布局**：战斗发生在一个独立的六边形网格状棋盘上（例如，32x16格）。棋盘的生成会根据SLG大地图的地块类型动态调整，确保每次战斗都有独特的地形环境。
- **地形元素**：战斗棋盘上会包含地形元素（如山脉、森林、河流、水晶矿脉、焦土、平原等），这些元素会影响单位的移动、攻击范围、视野，或提供增益/减益效果。例如，山脉阻挡远程攻击，森林提供生命恢复。
- **部署区**：每个玩家在各自半场拥有特定的部署区域，用于放置出战的棋子。玩家只能在部署区内放置单位，但单位在自动战斗开始后可以自由移动。

#### 2.4.2 回合制战斗流程

自走棋战斗分为三个核心阶段：战斗初始化、部署阶段（玩家操作）和自动战斗与结算。

1. **战斗初始化（系统自动）**：
   - **触发条件**：当玩家在大地图移动部队到有敌人（玩家部队或NPC部队）的地块时，系统在当前SLG大回合进程，进入战斗模式。
   - **棋盘生成**：基于SLG大地图的地块类型，动态生成一个六边形网格棋盘。地形效果由地块属性决定，并在棋盘上可视化随机分布。例如，山地地块会生成更多山脉格，河流地块会生成河流带。
   - **双方准备**：系统显示双方玩家的部队包（即已选择带入本次战斗的卡牌列表），但不可见对方具体的卡牌选择和部署策略。玩家有15秒时间预览棋盘地形，预判部署策略。

2. **部署阶段（玩家操作）**：
   这是战斗的核心策略环节，玩家需在有限时间内完成卡牌选择与战场布置。时间限制增加了游戏的紧张感和操作性。
   - **步骤a：卡牌选择与部队筛选（前15-20秒）**：
     - 玩家从 当前出征的部队包中选择本次战斗要实际部署的卡牌（英雄卡、法术卡、战略指令卡）。UI会清晰显示可用背包容量和每张卡牌的占用值。
     - **策略要点**：玩家需根据敌方预判、当前地形和自身卡牌组合，决定哪些卡牌最适合本次战斗。高占用史诗卡虽强但数量受限；低占用普通卡可带更多。例如，在PVE中，玩家可能放弃战略指令卡，携带更多英雄卡和法术卡以获得数量优势。
     - 如果背包容量不足以携带所有心仪的卡牌，玩家必须做出取舍。
   - **步骤b：单位部署与技能激活**：
     - **英雄卡放置**：玩家将已选择的英雄卡拖拽到棋盘上。棋盘分为玩家半场，初始位置在己方底线区域。地形会影响部署，例如山脉格不可放置单位，河流格可放置但单位初始速度相关可能减慢。玩家需要考虑单位站位策略，如近战英雄放置前排作为肉盾，远程英雄放置后排进行输出；利用地形，例如将高生命值的单位放置在森林格持续恢复生命。
     - **法术卡激活**：玩家可以在部署阶段选择激活法术卡（如“召唤陨石”改变地形，“提升骑兵攻击力”给单位上Buff），其效果在自动战斗开始时立即生效。法术卡通常为一次性消耗品。
     - **战略指令卡激活**：玩家在此阶段激活战略指令卡。确认使用后，卡牌进入冷却，并在自动战斗结束后，其SLG全局效果立即在大地图上生效。UI会提示玩家该指令卡将产生的宏观影响。战略指令卡具有稀缺性和高价值，是连接微观战斗与宏观战略的关键。

3. **自动战斗阶段（系统执行）**：
   部署完成后，战斗自动进行，类似自走棋的规则，但增加了地形和类型克制的影响。玩家在此阶段无法直接操作单位，只能观察战斗进程。
   - **战斗机制**：战斗过程完全由系统自动执行，玩家不能干预。棋子会根据设定的AI逻辑自动攻击、释放技能、移动等。单位按照其速度属性行动（移动、攻击、施放技能）。地形效果每秒应用，例如，站在森林格的单位持续回血，站在焦土格的单位持续掉血。
   - **克制系统**：单位类型（步兵/骑兵/弓箭手/战车/法师）之间存在克制关系，提供伤害加成/减免。例如，骑兵克弓箭手，弓箭手克步兵，步兵克骑兵，提供15%伤害加成/减免。法师则可能克制所有单位，但自身防御较弱，形成“玻璃大炮”机制。
   - **技能释放**：英雄单位根据其能量积累（通过攻击或受到伤害）或内置冷却时间自动释放技能。法术卡效果根据其描述生效。
   - **胜利条件**：一方所有单位死亡。如果规定时间内无一方全灭，则按剩余单位总战力判定胜负。


4. **战斗结算（系统自动）**：
   战斗结束后，系统立即进行结果判定并给予奖励，同时将结果反馈到SLG大地图。
   - **结果判定**：PVP战斗中，胜利方占领地块，失败方失去地块控制权；PVE战斗中，胜利方清除NPC。
   - **奖励机制**：
     - **基础奖励**：金币、普通卡牌掉落。
     - **特殊奖励**：空投或boss战斗可能掉落稀有/史诗级卡牌。战略指令卡通常在高难度PVE/Boss战、占领州府等特定事件中获得。
     - **经验值**：提升玩家SLG等级，进而增加背包容量和解锁新的SLG功能。
   - **战略指令卡持续效果**：如果在战斗中使用了战略指令卡，其SLG全局效果立即生效。例如，“火烧连营”使相邻地块变为焦土并持续伤害；“筑城令”在战斗胜利地块立即建造要塞。这些效果通常持续多回合（3-5回合），并在大地图上进行可视化显示，所有玩家可见，从而迫使对手调整战略，形成情报博弈。
   - **与SLG衔接**：胜利方获得地块控制权。

### 2.5 大地图SLG与战斗的深度集成

战斗机制并非孤立存在，它服务于SLG的全局战略，并与大地图节奏紧密相连，形成一个有机的整体。

#### 2.5.1 SLG回合流程（每个大回合 60秒）

1. **自动资源收集**：玩家从控制的地块（城市、矿场、农田等）自动获得金币资源。资源产出量受地块类型和天气等因素影响。
2. **部队行动**：玩家可以移动部队到相邻地块。如果目标是无主地块，部队自动占领；如果目标地块有敌人，则触发自走棋部署，然后自动结算。  点击地块 相当于消耗行动力，如果会发生战斗 会切换到部署模式，然后  该回合结束  返回战斗结果
3. **事件处理**：
   - **随机空投事件**：地图上随机出现高价值卡牌（如史诗法术卡、稀有英雄卡）的空投点，多个玩家可派遣部队争夺，触发多方自走棋战斗。这增加了游戏的随机性和竞争性。
   - **副本事件**：特殊地块（如“虎牢关”）激活，玩家可选择派遣部队挑战，副本是获取稀有资源和卡牌的重要途径。
4. **升级与制造**：玩家消耗金币，升级玩家等级（增加行动力部队数量和部队背包容量）。

#### 2.5.2 关键元素与游戏节奏

- **PVE内容**：
  - **早期回合（回合1-4）**：以PVE为主。地图上分布大量“小怪地块”，玩家派遣部队进行“训练战”，主要目的是熟悉战斗机制，积累基础卡牌（普通英雄卡、法术卡），并获得初期资源。这有助于新手玩家快速上手并积累实力。
  - **中期PVP增加（回合5-50）**：随着玩家卡牌积累和等级提升，玩家开始向有其他玩家占领的资源地块移动，PVP战斗频率增加，资源争夺变得激烈。玩家之间的对抗成为游戏的主旋律。
- **副本与空投**：
  - **特殊地块（如“神秘空投点”、“远古遗迹”）**：在特定回合刷新，触发多人参与的自走棋战斗。胜者获得稀有/史诗卡牌，败者虽有损失但并非出局。这些事件为玩家提供了额外的策略选择和高风险高回报的机会。
  - **Boss战（如“虎牢关Boss战”、“黄巾军首领”）**：高难度PVE挑战，玩家可组队（或单人）挑战，胜利后获得大量经验值和极其稀有的卡牌（包括战略指令卡），甚至可能解锁新的科技树。Boss战是游戏中的重要里程碑，也是玩家展示实力的机会。
- **最终目标与剧本决战**：
  - **后期聚焦关键目标（回合51-90+）**：游戏进入白热化阶段。每个剧本会设定终极目标，例如：
    - **三国剧本**：占领“洛阳”或“长安”等核心都城，并击败守城Boss，触发最终的8人混战自走棋。首先达到条件并守住一定回合的玩家获得胜利。
    - **中世纪剧本**：统一所有州府，或攻占“国王都城”，并抵抗其他玩家的反扑。



#### 2.5.3 战斗冲突

串行战斗，共享总部署时间（推荐）
这是最常见且相对容易实现的方式，通过在总回合时间内分配战斗的部署时间。

设计思路：

核心理念： 保持SLG大回合总时长不变，当触发多场战斗时，玩家进入战斗的顺序是串行的。每一场战斗都有一个独立的变长的部署时间，但所有战斗的部署时间都计入SLG大回合的总剩余时间。

优先级机制：

玩家操作优先级： 当同一回合内触发多场战斗时，系统会提示玩家，并按优先级（例如：玩家主动进攻的战斗 > 被攻击的防守战 > PvE战斗）或时间先后顺序，逐一进入战斗的部署阶段。


界面提示：

在SLG大地图上，清晰地标记所有即将发生的战斗（例如，用图标或闪烁效果）。


---




## 3. 身份与战斗状态系统

### 3.1 身份系统

身份系统是游戏的核心社交和策略机制，它在剧本开局时为玩家分配阵营归属和角色定位，直接决定了玩家的初始目标、特殊技能、胜利条件以及与其他玩家的关系。身份的明置或隐藏将成为重要的心理战和战略转折点。 只有 8 人剧本才有 身份系统。

#### 3.1.1 身份类型（8 人场）

- **主公**：
  - **目标**：生存并统一天下（或剧本设定的最终目标）。
  - **技能**：
    - **崩殂（锁定技）**：当你死亡时，杀死你的角色明置身份牌。
    若其为反贼，立即获得10金币，并使该玩家的所有棋子在本场战斗中攻击速度提升20%。
    若其为忠臣，该玩家的所有棋子立即随机获得一个负面状态（如：攻击力-10%持续本场战斗，或移动速度-20%持续本场战斗）。
    - **托孤（主动技）**：
    当你死亡时，可指定一名身份未知的角色明置身份牌。
    若为忠臣，提前获得卡包里面的卡，并立即获得一次免费的商店刷新机会，身份替换为“丞相”。
    若为反贼，其获胜。
    若为内奸，该人获得你所有法术卡片，并立即进行一次商店刷新，身份替换为主公，其他忠臣变为内奸（非同一阵营）。
  - **战略意义**：作为游戏的核心目标，主公的存亡直接影响游戏进程和所有玩家的胜负。

- **忠臣**：
  - **目标**：保护主公，协助主公达成胜利目标。
  - **技能**：
    - **赤心（锁定技）**：死亡时，若杀死你的是主公或丞相，其所有棋子本场战斗的基础攻击力降低15%。
    若是内奸，其获得15金币。
    - **舍身（主动技）**：主公/丞相濒死（大地图上无地块可守）时，你可以选择发动。发动时你让主公重新选择地块，主公/丞相所有棋子立即恢复20%最大兵力，并使所有棋子在下一场战斗中获得一个额外羁绊效果（随机）。其他忠臣失去“舍身”技能。
  - **战略意义**：关键时刻牺牲自我领地，极大强化主公在自走棋战斗中的续航和爆发力；需谨慎使用。

- **反贼**：
  - **目标**：击败主公，或达成剧本设定的反叛胜利条件。
  - **技能**：
    - **败溃（锁定技）**：死亡时，杀死你的角色立即获得30金币。
    - **起义（主动技）**：回合可明置身份牌并立即刷新一次商店，且本回合内所有棋子购买费用降低1金币（最低1金）。
  - **战略意义**：明跳后加速经济和阵容成型，适合中期集中火力推主。

- **内奸**：
  - **目标**：在主公和反贼之间周旋，最终达成渔翁得利的目标（例如，在主公和反贼两败俱伤后，击败剩余的玩家）。
  - **技能**：
    - **潜谋（锁定技）**：死亡时，若“夺权”未发动，杀死你的角色随机损失20 金币。若已发动“夺权”，杀死你的角色获得2金币。
    - **夺权（主动技）**：出牌阶段可明置身份牌：立即获得10点slg 经验，并使你所有棋子立即获得攻速增幅。降低所有谋略卡牌 cost。
  - **战略意义**：后期爆发型选手，通过能量优势快速部署高战力棋子，并具备暂时封锁敌方关键技能的能力；但跳内后容易成为集火目标。

#### 3.1.2 身份影响范围

- **战略目标**：不同身份拥有截然不同的胜利目标，这决定了玩家在游戏中的基本行为模式。
- **身份技能**：提供独特的主动或被动能力，直接作用于战斗表现、经济、甚至改变游戏规则。
- **同盟/敌对关系**：身份决定了玩家之间的天然同盟或敌对关系，影响外交、支援、掠夺等行为。
- **终局博弈**：尤其在游戏后期，身份的明置或隐藏成为重要的心理战和战略转折点，玩家需要根据局势判断何时亮明身份以获取最大收益。

---




## 4. 战斗系统

战斗系统是游戏的核心，它将宏观的SLG战略与微观的自走棋战术紧密结合，为玩家提供快节奏、高策略性的对战体验。战斗在玩家或玩家与NPC在地块相遇时触发，每个战斗独立且快节奏，总时长平均2-5分钟。整个战斗过程分为三个核心阶段：战斗初始化、部署阶段（玩家操作）、自动战斗与结算。

### 4.1 战斗初始化（系统自动）

1.  **触发条件**：当玩家在大地图移动部队到有敌人（玩家部队或NPC部队）的地块时，系统自动切换进入战斗部署模式。
2.  **棋盘生成**：
    *   基于SLG大地图的地块类型（例如：森林、山地、河流、平原、焦土、资源点），动态生成一个32x16格的六边形网格棋盘（60°转角）。
    *   地形效果由地块属性决定，并在棋盘上可视化随机分布。
    *   **地形规则细化**：

| 地形类型 | 效果 | 视觉特征 | 生成规则（示例） |
| :--- | :--- | :--- | :--- |
| 山脉 | +30%防御（对站立单位），阻断远程攻击路径，不可放置单位。 | 棕褐色凸起岩石 | 山地地块：固定5-7个山脉格随机分布。 |
| 森林 | 战斗中恢复5%生命值（仅对站立单位），阻挡部分视野。 | 深绿色树丛 | 森林地块：4-6个随机森林格；其他地块：可能随机1-2个。 |
| 河流 | 移动消耗双倍回合，单位进入需额外行动点，远程单位可隔河攻击。 | 蓝色流动水体 | 河流地块：棋盘中央或侧边生成2-3条河流带；连接河流的平原：可能随机1条。 |
| 水晶矿脉 | 能量恢复+50%（影响法术相关单位，使其更快释放技能）。 | 紫色发光水晶 | 资源地块（矿产）：随机3-4个水晶格。 |
| 焦土 | 每秒/回合持续伤害3%（对所有单位），无法通过移动恢复生命。 | 黑色裂纹地面，冒烟 | 灾害事件或战略指令卡（如“火烧连营”）生成。 |
| 平原 | 无特殊效果。 | 绿色草地 | 棋盘默认背景，用于填充其余空间。 |

3.  **双方准备**：系统显示双方玩家的部队包（即已选择带入本次战斗的卡牌列表），但不可见对方具体的卡牌选择和部署策略。玩家有10秒时间预览棋盘地形，预判部署策略。

### 4.2 部署阶段（玩家操作）

这是战斗的核心策略环节，玩家需在有限时间内完成卡牌选择与战场布置。

1.  **步骤a：卡牌选择与部队筛选**：
    *   玩家从部队包中选择本次战斗要实际部署的卡牌（英雄卡、法术卡、战略指令卡）。UI会清晰显示可用背包容量和每张卡牌的占用值。
    *   **策略要点**：
        *   玩家需根据敌方预判、当前地形和自身卡牌组合，决定哪些卡牌最适合本次战斗。
        *   高占用史诗卡虽强但数量受限；低占用普通卡可带更多。例如，在PVE中，玩家可能放弃战略指令卡，携带更多英雄卡和法术卡以获得数量优势。
        *   如果背包容量不足以携带所有心仪的卡牌，玩家必须做出取舍。
2.  **步骤b：单位部署与技能激活**：
    *   **英雄卡放置**：玩家将已选择的英雄卡拖拽到棋盘上。棋盘分为玩家半场（各8行），初始位置在己方底线区域。
        *   地形影响部署：山脉格不可放置单位；河流格可放置但单位初始速度相关可能减慢。
        *   单位站位策略：近战英雄放置前排作为肉盾，远程英雄放置后排进行输出；利用地形，例如将高生命值的单位放置在森林格持续恢复生命。
    *   **法术卡激活**：玩家可以在部署阶段选择激活法术卡（如“召唤陨石”改变地形，“提升骑兵攻击力”给单位上Buff），其效果在自动战斗开始时立即生效。
    *   **战略指令卡激活**：玩家在此阶段激活战略指令卡。确认使用后，卡牌进入冷却，并在自动战斗结束后，其SLG全局效果立即在大地图上生效。UI会提示玩家该指令卡将产生的宏观影响。

### 4.3 自动战斗阶段（系统执行，0s 结束，回合结束返回战报）

部署完成后，战斗自动进行，类似自走棋的规则，但增加了地形和类型克制的影响。

1.  **回合机制**：单位按照其速度属性行动（移动、攻击、施放技能）。
    *   每个单位都有自己的行动条，行动条满时执行一次行动。
    -   地形效果每秒/回合应用：例如，站在森林格的单位持续回血，站在焦土格的单位持续掉血。
2.  **克制系统**：
    *   单位类型（步兵/骑兵/弓箭手）之间存在15%的伤害加成/减免。例如，骑兵攻击弓箭手造成115%伤害，弓箭手攻击步兵造成115%伤害，步兵攻击骑兵造成115%伤害。反之亦然。
3.  **技能释放**：英雄单位根据其能量积累（通过攻击或受到伤害）或内置冷却时间自动释放技能。法术卡效果根据其描述生效。
4.  **胜利条件**：一方所有单位死亡。
5.  **持续时间**：战斗总时长受SLG回合时间限制，通常控制在平均1-3分钟。如果规定时间内无一方全灭，则按剩余单位总战力判定胜负。

### 4.4 战斗结算（系统自动）

战斗结束后，系统立即进行结果判定并给予奖励，同时将结果反馈到SLG大地图。

1.  **结果判定**：
    *   PVP战斗：胜利方占领地块，失败方失去地块控制权。
    *   PVE战斗：胜利方清除NPC，失败方英雄卡返回。
    *   卡片状态：双方所有卡片消失
2.  **奖励机制**：
    *   基础奖励：金币、普通卡牌掉落。
    *   特殊奖励：
        *   空投或副本战斗：掉落稀有/史诗级卡牌。
        *   战略指令卡：在高难度PVE/Boss战、占领州府等特定事件中获得。
        *   经验值：提升玩家SLG等级，进而增加背包容量和解锁新的SLG功能。
3.  **战略指令卡持续效果**：
    *   如果在战斗中使用了战略指令卡，其SLG全局效果立即生效。
    *   例如：“火烧连营”使相邻地块变为焦土并持续伤害；“筑城令”在战斗胜利地块之后不可通行。
    *   这些效果通常持续多回合（3-5回合），并在大地图上进行可视化显示（如焦土地块冒烟），所有玩家可见。这迫使对手调整战略，形成情报博弈。
4.  **与SLG衔接**：
    *   地块控制权：胜利方获得地块控制权
    
---




## 5. 卡牌系统

卡牌是游戏的核心驱动力，分为三种主要类型：英雄卡、法术卡和战略指令卡，它们共同构建了战斗的策略深度，并贯穿于SLG大地图和自走棋战斗的始终。

### 5.1 卡牌类型

| 卡牌类型 | 功能描述 | 背包占用值（示例） | 是否消耗（使用后） | 获取方式 |
| :--- | :--- | :--- | :--- | :--- |
| **英雄卡 (Hero Cards)** | 代表可部署的战斗单位（如骑兵、弓箭手、法师、步兵等）。每个单位有属性（生命值、攻击力、攻击类型、移动速度）和被动技能。引入类型克制系统：例如，骑兵克弓箭手，弓箭手克步兵，步兵克骑兵（提供15%伤害加成/减免）。英雄卡是战场上的主要战斗力。 | 高占用（8-15点），取决于单位稀有度/强度。例如：普通单位10点，史诗单位15点。 | 否（战斗后保留，但可能受伤需恢复） | PVE小怪掉落、副本奖励、商店购买（用SLG资源）。 |
| **法术卡 (Spell Cards)** | 一次性效果，在部署阶段或自动战斗过程中特定时机激活。效果多样，包括：地形改变（如召唤陨石制造焦土，或召唤陷阱区）、单位Buff/Debuff（如提升己方骑兵攻击力，或降低敌方单位防御）、直接伤害（如火球AOE，或单体高爆发伤害）。法术卡提供局部战术优势和瞬时爆发。 | 中占用（5-10点），取决于效果强度。例如：小法术5点，大招10点。 | 是（使用后消失） | PVE掉落、空投事件、SLG资源合成。 |
| **战略指令卡 (Strategic Command Cards)** | 核心设计：跨层级影响力。在自走棋战斗中使用后，不仅影响当前战斗（微观），更会在大地图SLG层面生成持续性效果或全局事件（宏观），形成“一卡双用”的战略纽带。具有稀缺性与高价值。部分指令卡需消耗SLG资源或牺牲局部利益换取全局优势。 | 高占用（15-25点），极为稀有。 | 是（使用后消失） | 稀有：高难度PVE/Boss战、空投事件或占领州府获得。可能通过特定3张法术卡+SLG资源合成1张低级指令卡（如“小火攻”）。 |



### 5.3 英雄棋卡牌机制

#### 5.3.1 卡牌基础结构

英雄卡牌的基础结构将根据剧本的不同而有所差异，但核心属性保持一致：

| 属性类别 | 描述 |
| :--- | :--- |
| 武将名称 | 历史人物，如“关羽”、“曹操”、“诸葛亮”等 |
| 阵营 | 蜀 / 魏 / 吴 / 群（可扩展：晋、汉、战国等） |
| 兵种类型 | 步兵（Shield 盾） / 骑兵（Sword 剑） / 弓兵（Bow 弓） / 战车（Spear 枪） / 法师（Staff 杖） |
| 性格五行 | 金、木、水、火、土、雷（影响羁绊、术法效果） |
| 武将定位 | 坦克、战士、法师、控制、射手、辅助等 |
| 技能类型 | 仅一个 主动技 / 被动技（必选一种），每位武将仅有一个特色技能（区别定位） |

#### 5.3.2 兵种相克关系

在战斗中，兵种相克关系将直接影响伤害和抗性判断：

| → 被克制 / 克制 → | 步兵<br>(Shield) | 骑兵<br>(Sword) | 弓兵<br>(Bow) | 战车<br>(Spear) | 法师<br>(Staff) |
| :--- | :--- | :--- | :--- | :--- |
| 步兵 | — | ✓（盾克剑） | ✗ | ✓（盾克车） | ✓（盾克法） |
| 骑兵 | ✗ | — | ✓（剑克弓） | ✗ | ✓（剑克法） |
| 弓兵 | ✓（弓克盾） | ✗ | — | ✓（弓克车） | ✓（弓克法） |
| 战车 | ✗ | ✓（车克盾） | ✗ | — | ✓（车克法） |
| 法师 | ✓（杖克一切） | ✓ | ✓ | ✓ | — |

- “✓”表示行侧可克制列侧；
- 法师（Staff）可克制所有，但同时也被所有克制——“玻璃大炮”机制：输出最强，全表行侧对其它4种都有✓；防御最弱，其所在列除了自己（—）外全被✓。

#### 5.3.3 属性面板

| 属性名称 | 说明 |
| :--- | :--- |
| 生命值（兵力） | 单位人数，等同于血量，战斗损耗即减员，0则战死 |
| 能量 | 类似于蓝条，满了可以释放主动技能 |
| 攻击力 | 影响普通攻击与部分技能伤害（物理） |
| 护甲 | 减免来自敌方物理攻击的伤害 |
| 谋略 | 影响谋略技能伤害与抗性（类似法术攻击） |
| 魔抗 | 减少受到谋略/术法攻击的伤害 |
| 速度 | 决定单位在战场中攻击频率 |
| cost | 占部队背包的cost 的大小（如 10） |
| 羁绊标签 | 阵营/兵种/关系/性格（用于组合加成） |

#### 5.3.4 技能系统

- **技能分类**：
  - **主动技**：能量条满了自动触发，具有指向或范围。
  - **被动技**：持续生效，如加攻/回血/复活等。
- **技能效果可涵盖**：
  - **数值增强类**：加攻、防、速度、暴击等。
  - **控制类**：眩晕、沉默、缴械、减速、击退。
  - **削弱类**：破甲、减疗、中毒、灼烧。
  - **特殊类**：吸血、召唤兵、地形变化、改变敌方行为。

#### 5.3.5 羁绊系统（Synergy）

羁绊系统是英雄卡牌组合的核心，通过特定组合激活强大的协同效应：

| 羁绊类型 | 示例 |
| :--- | :--- |
| 阵营羁绊 | 蜀国全员：攻击+10%；魏国：技能冷却-20% |
| 五行克制 | 火克金、金克木等，提供额外伤害或抗性 |
| 兵种联动 | 步+盾 → 护盾提升；弓+骑 → 侧翼暴击 |
| 历史缘分 | 关羽+张飞+刘备 → “桃园结义”：初始士气上升 |

#### 5.3.6 战斗表现

- **兵力体现**：血条 + 数字（例：1350/1500）。
- **战损动画**：小兵消散、颜色变灰、喷血气泡。
- **技能释放**：使用帧动画或粒子系统表达攻击范围/效果。

### 5.4 法术卡牌系统

法术卡牌提供一次性或短时效果，是自走棋战斗中的重要战术补充。

| 属性 | 说明 | 示例 |
| :--- | :--- | :--- |
| 术法类型 | 兵家权谋/玄门道法/墨家机关 | 十面埋伏（兵家） |
| 术法等级 | 天（史诗）/地（传奇）/玄（精良）/黄（普通） | 天级·呼风唤雨 |
| 生效维度 | 战场效果(自走棋) | 木牛流马（战场+经济） |
| 能量消耗 | 施法需消耗cost | 玄级术法消耗8点 |
| 势力专精 | 特定阵营强化对应术法（曹操军兵家+20%效果） | 蜀国玄门道法消耗-30% |

#### 5.4.1 三系术法机制详解

- **兵家权谋系（战术强化）**：
  - **设计特点**：战场即时生效，无持续效果；与部队构成强关联（如弓兵数量决定箭阵强度）；曹操势力专精：效果+20%持续时间。
  - **效果示例**：
    - **单位强化**：属性增益（如“背水一战”+50%攻速）、能力赋予（如骑兵获得“突袭”特性）。
    - **阵型特技**：站位特效（如“十面埋伏”的森林伏击）、协同加成（如方阵相邻单位+防御）。

- **玄门道法系（环境操控）**：
  - **设计特点**：通过改变地形、天象或运用阴阳秘术来影响战局。
  - **效果示例**：
    - **地形改造**：元素转化（如河流变焦土）、地形增益（如山脉生成灵泉）。
    - **天象操控**：气象战争（如“赤壁东风”）。
    - **阴阳秘术**：生死逆转（如“七星续命”复活）、因果律（如指定单位必被暴击）。

- **墨家机关系（战略装置）**：
  - **设计特点**：部署战争器械，提供持续性支援或改变后勤。
  - **效果示例**：
    - **战略建筑**：箭塔（地块自动防御）、烽火台（3格内预警）。
    - **战争器械**：投石车（远程攻城）、连弩车（范围压制）。
    - **后勤革新**：木牛流马（经济地块收益+20%）。

#### 5.4.2 动态触发系统

术法的获取和选择通过三重触发节点进行：

| 触发类型 | 触发条件 | 可选术法等级 | 特殊机制 |
| :--- | :--- | :--- | :--- |
| **固定回合** | 每3/7/11回合（短局）/ 每5/10/15回合（长局）+5 | 玄/黄（第3）/ 地/天（第15） | 概率出现地级（第7） |
| **战略节点** | 占领洛阳/虎牢关等名城 | 必出天级选择 | 附带守军强化效果 |
| **事件节点** | 击败董卓/参加七星坛仪式 | 事件专属术法 | 如“传国玉玺”全局加成 |
| **商店刷新** | 刷新相关的术法市集 | 天地玄黄随着你的等级变化而变化 | |

#### 5.4.3 经济系统深度联动与术法市集

- **刷新机制**：
  - 专门的术法商店，每回合免费刷新1次，3金/次追加刷新。
  - 刷新概率（随着等级增加而变化）：黄60%/玄30%/地8%/天2%。
- **特殊交易**：
  - 限定拍卖（拍卖行）：每20回合出现天级术法竞拍/及其他。
  - 随着回合或者剧情的推进：攻略城池或者特定州府任务。

### 5.5 战略指令卡效果设计

战略指令卡是连接微观战斗与宏观战略的关键，其效果具有跨层级影响力。

| 卡牌名称（三国剧本） | 自走棋战斗效果（微观） | SLG全局效果（宏观） | 战略意义 |
| :--- | :--- | :--- | :--- |
| **火烧连营** | 当前战斗：对3x3区域造成持续火焰伤害（类似法术）。 | 全局：目标地块相邻的2块森林/草原地块转化为“焦土”（持续3回合），敌方部队进入时每回合损失5%兵力。 | 牺牲局部地形换取区域控制权，阻断敌方行军路线，限制敌人移动。 |

---




## 6. 天气系统

天气系统为游戏引入了动态的环境变量，增加了战略和战术的不确定性与变数，并与地形、身份、术法等系统产生复杂的化学反应。

### 6.1 天气影响维度

天气系统对游戏的影响主要体现在战略层、战术层和身份特权三个维度：

- **战略层影响**：
  - **资源产出效率**：例如，暴雨可能导致农田产出下降，晴天则提升矿产效率。
  - **全局事件触发**：特定天气可能触发全局性事件，如“九星连珠”在连续极光天气后触发。
- **战术层影响**：
  - **自走棋单位属性**：特定天气下，单位的攻击力、防御、速度、命中率等属性可能发生变化。例如，沙暴降低远程攻击命中率。
  - **地形效果强化**：天气与地形结合产生新的效果。例如，暴雨中的山脉可能触发“落石事件”。
  - **术法连锁反应**：某些术法效果会因天气而增强或减弱，甚至产生连锁反应。例如，“火攻”在晴天效果更佳，在暴雨中则可能被削弱。
- **身份特权**：不同身份的玩家拥有操控或适应天气的特殊技能，增加了博弈深度。
  - **主公**：钦天监特权，可预知未来天气。
  - **反贼**：灾厄使者，可强化负面天气效果。
  - **忠臣**：风调雨顺，可维持宜人天气。
  - **内奸**：天象伪装，可伪造天气情报。

### 6.2 天气与地形的化学反应

天气与地形的结合将产生丰富的环境效果，影响战斗和战略：

| 地形 \ 天气 | 暴雨 | 沙暴 | 极光 | 严冬 |
| :--- | :--- | :--- | :--- | :--- |
| **山脉** | “落石事件”（伤害+30%） | “沙尘护盾”（防御+40%） | “星陨矿脉”（资源×2） | “雪崩预警”（移动封锁） |
| **森林** | “树木导电”（雷伤+50%） | “枯木逢春”（治疗×3） | “精灵祝福”（攻速+25%） | “伐木禁令”（产出-70%） |
| **河流** | “洪水泛滥”（地块分割） | “河床裸露”（新行军道） | “虹桥奇观”（瞬移通道） | “冰面陷阱”（随机摔伤） |

### 6.3 阵营专属天气操控

不同身份的玩家拥有独特的天气操控技能，这些技能具有战略价值：

| 身份 | 特权技能 | 冷却回合 | 战略价值 |
| :--- | :--- | :--- | :--- |
| **主公** | “钦天监：预知下3种天气，提前5回合部署” | 15 | 规避行军风险，抢占资源先机 |
| **反贼** | “灾厄使者：强化负面天气（效果×1.5，范围×2）” | 20 | 破坏敌方经济区，制造行军障碍 |
| **忠臣** | “风调雨顺：指定区域维持宜人天气5回合” | 25 | 保护核心产粮区，提升盟友战力 |
| **内奸** | “天象伪装：伪造天气情报，误导敌方决策” | 10 | 诱敌进入暴雨区，掩护真实意图 |

### 6.4 动态天气进度条

游戏界面将通过可视化的气候轮盘和倒计时，向玩家展示当前和未来的天气变化，增加气候博弈的透明度：

- **操作界面**：
  - 地图边缘显示气候轮盘（当前/下个天气图标）。
  - 鼠标悬停显示倒计时：“暴雨将在2回合后降临”。
- **天气周期预测（10回合）**：
  - 主公特权：可预知更长时间的天气变化。
  - 常规玩家：只能看到短期天气预测。

### 6.5 极端天气事件

极端天气事件是改变战局的超级灾害，它们通常由特定条件触发，并对SLG大地图和自走棋战场产生巨大影响：

| 事件名称 | 触发条件 | SLG影响 | 自走棋战场影响 |
| :--- | :--- | :--- | :--- |
| **九星连珠** | 连续3次极光 | 所有资源点产量×3，但每回合10%概率自毁 | 所有单位觉醒隐藏技能 |
| **十日凌空** | 晴天地块>70% | 河流干涸变行军道，森林地块自燃 | 每回合随机单位自焚 |
| **归墟潮汐** | 暴雨+满月 | 沿海地块沉没，生成新岛屿资源点 | 棋盘随机出现漩涡，吸入单位永久消失 |
| **地龙翻身** | 山地冲突超10次 | 山脉崩塌形成关隘，随机生成矿洞 | 每回合重组棋盘地形 |

---




## 7. 对局（Match）玩家核心属性系统

玩家核心属性是构成玩家在游戏中实力、状态和行为模式的基础。这些属性相互关联，共同决定了玩家在SLG大地图和自走棋战斗中的表现。

### 7.1 身份（Identity）

- **定义**：玩家在剧本开局时被分配的阵营归属和角色定位，如主公、忠臣、反贼、内奸。
- **重要性**：最高优先级的玩家属性，直接决定了玩家的初始目标、特殊技能、胜利条件、与其它玩家的关系，以及在特定事件中触发的专属效果。
- **影响范围**：
  - **战略目标**：主公的目标是生存并统一，反贼是击败主公，忠臣是保护主公，内奸则是在混乱中渔利。
  - **身份技能**：提供独特的主动或被动能力（如“托孤”、“舍身”、“夺权”、“起义”），直接作用于战斗表现、经济、甚至改变游戏规则。

  - **终局博弈**：尤其在游戏后期，身份的明置或隐藏成为重要的心理战和战略转折点。

### 7.2 领地（Territory / Domain）

- **定义**：玩家在战略大地图上实际控制的地块数量和质量。
- **重要性**：玩家生存和发展的基础，可以看作是玩家在战略层面的**“生命线”和“资源产出核心”**。
- **影响范围**：
  - **生存状态**：地块数量直接关联玩家的生存。失去所有地块通常意味着“流离”状态甚至被淘汰。
  - **资源产出**：地块是金钱的主要来源，地块数量直接影响玩家的经济和发展速度。
  - **战略纵深**：更多地块提供更大的战略缓冲空间。
  - **局部地形**：地块类型决定了触发战斗时自走棋棋盘的动态地形，从而影响战术选择。

### 7.3 经济资源（Economic Resources）

- **定义**：玩家可用的货币和用于部队管理的资源，主要为金钱（Gold）。
- **重要性**：驱动玩家发展和部队强度的核心引擎。没有足够的经济资源，玩家将无法招募、升级棋子，也无法解锁和强化术法。
- **影响范围**：
  - **金钱**：
    - **棋子招募与刷新**：购买新英雄卡片，法术卡片，战略卡片，刷新商店寻找所需单位。

    - **玩家等级提升**：升级玩家等级所需的消耗之一。


### 7.3 背包 (pakage)

- **定义**：一个玩家在对局（Match游戏里面所有的用于当前游戏的所有卡牌的管理，主要为 三种卡牌 英雄棋子，法术牌，战略牌 。
- **重要性**：每次获得的卡牌都存放到这个背包，在出征的时候选择卡牌放到部队背包里面。


### 7.4 玩家等级（Player Level）

- **定义**：代表玩家在对局（Match游戏中的整体成长进度和实力阶梯。
- **重要性**：解锁更高级内容和提升整体实力的关键。
- **影响范围**：
  - **行动力**: 1-3  在一个回合出击的部队数量
  - **部队背包的容量（cost Cap）**：每升一级通常会直接增加玩家的部队背包的容量，即在自走棋战场上可部署的棋子总数量（或英雄数量）。这是玩家能够组建更大、更强阵容的基础。 cost cap = 20 * level


  - **商店品级**：玩家等级越高，商店中刷出高星级、高品质棋子的概率越大。

### 7.5 部队（Armies / Units）

- **定义**：玩家在战略地图上实际操控的军队及这些军队内部由英雄棋子,法术卡，战略卡 构成的战斗力。
- **重要性**：**玩家进行战略行动和赢得战斗的执行者。**没有强大的部队，再多的地块和资源也无法守住或扩张。
- **属性**：
  - **军队数量**：玩家在战略地图上可以同时操控的独立军队数量，影响其多线作战和战略部署能力。
  - **棋子强度**：由每个英雄棋子的属性、技能和羁绊决定。
  - **cost upper limit**：不同等级的cost 的上限 不一样，根据 cost 带英雄棋子卡牌 法术卡 战略指令卡：战略军队的“空间格”可以理解为：每个战略军队本身就带有一个固定的“人口容量”，而玩家等级提升的是这个“人口容量”的总上限，从而可以为每支军队分配更多棋子，或组建更多拥有高cost消耗的法术卡牌。



### 7.7 天气适应性（Weather Adaptability）

- **定义**：玩家部队对全局天气和局部战场天气的适应能力。
- **重要性**：引入动态的环境变量，增加战略和战术的不确定性和变数。
- **影响范围**：
  - **单位属性变化**：特定天气下，骑兵可能移动更快、弓兵命中率降低等。
  - **特殊效果触发**：如雷暴天气随机闪电伤害，极光天气稀有技能触发率翻倍。
  - **术法联动**：某些术法（如祈雨术、烈日咒）能改变天气，而某些术法效果也会因天气而增强或减弱。
  - **身份加成**：不同身份对天气的抗性或利用能力不同。

---


# 游戏流程图



## 宏观游戏流程（从登录到结算）
```mermaid

flowchart TD
    A[玩家登录/注册] --> B[进入匹配房间]
    B --> C[选择偏好剧本、身份、卡组]
    C --> D[系统分配身份与初始地块]
    D --> E[进入SLG回合制游戏（8人）]
    E --> F[游戏进行中]
    F --> G[是否达成胜利条件]
    G -->|是| H[游戏结束，结算积分]
    G -->|否| F
    H --> I[更新游戏外等级与成就]
    I --> J[返回大厅或继续游戏]
```


## 局内单个回合流程（We-Go 回合制）

```mermaid
flowchart TD
    A[回合开始 (K 回合, 60秒)] --> B((系统更新UI / 可查看战报));

    subgraph Player_Action_Phase [玩家行动阶段 (60秒)]
        B --> C{提交行动指令}
        C -- 非部队移动 --> C1[购买卡牌 / 提升等级]
        C -- 部队移动 --> C2[选择目标地块]
        C2 -- 无主地块 --> C2a[自动占领地块]
        C2 -- 敌方/PVE地块 --> C2b[触发战斗部署]
        C2b --> D[进入战斗部署阶段]
        D --> E[部署阶段: 选择卡牌与布阵 (限时)]
    end

    C1 --> F[所有指令提交完成]
    C2a --> F
    E --> F

    F --> G[回合结束: 系统统一结算]
    G --> H{判定战斗结果 & 地块归属}
    H --> I[奖励分配 & 战略卡效果生效]
    I --> J{检查胜利条件 / 身份锁定}
    J -- 游戏未结束 --> A
    J -- 游戏结束 --> K[进入游戏结算]

    style A fill:#f96,stroke:#333,stroke-width:4px
    style B fill:#bbf,stroke:#333,stroke-width:2px
    style C1 fill:#cfc,stroke:#333
    style C2a fill:#cfc,stroke:#333
    style D fill:#fcc,stroke:#333
    style E fill:#fcc,stroke:#333
    style G fill:#9cf,stroke:#333,stroke-width:2px
    style H fill:#9cf,stroke:#333
    style I fill:#9cf,stroke:#333
    style J fill:#bbf,stroke:#333,stroke-width:2px
    style K fill:#ff9,stroke:#333

```



## 战斗流程（部署 + 自动战斗 + 结算）

```mermaid
flowchart TD
    A[战斗触发] --> B[生成战斗棋盘（基于地形）]
    B --> C[双方进入部署阶段]
    C --> D[选择出战卡牌（英雄/法术/战略）]
    D --> E[布阵：放置棋子+激活法术/战略卡]
    E --> F[部署结束，进入自动战斗]
    F --> G[系统模拟战斗（不可干预）]
    G --> H{胜负判定}
    H -->|胜利方| I[占领地块]
    H -->|失败方| J[失去地块]
    I --> K[战斗结算：奖励/经验/身份触发]
    J --> K
```

## 身份系统触发流程（以主公死亡为例）
```mermaid
flowchart TD
    A[主公地块被攻破] --> B[触发“崩殂”技能]
    B --> C{击杀者身份}
    C -->|反贼| D[反贼获得10金币+攻速+20%]
    C -->|忠臣| E[忠臣棋子获得负面状态]
    C -->|内奸| F[内奸获得所有法术卡+刷新商店]
    F --> G[内奸身份变主公]
```





# 卡牌实例


英雄卡牌   法术卡牌  战略卡牌

# 开发工具


TypeScript是一种编程语言，适用于任何规模的JavaScript应用开发；Phaser是一个主要用于构建2D游戏的框架；而Colyseus则是一个专注于实时多人游戏或应用的后端框架。 以及 vite 开发这个歌游戏 

实现一个多人在线游戏的框架  、Phaser3和Colyseus以及 vite搭建出一个最基本的在线多人可以开房间一起进行游戏的基础游戏框架 可以实现登录 注册 然后开房间或者匹配 8 人的方法 进行游戏的开始以及初始化  如果人不够的情况下，可以添加机器人的功能，使得游戏可以顺利开始，因为现在是 8 个人才能正式开始，但是后续的验证只有我一个，所以我希望引入一些机器人，然后进行游戏。









## 5.6 卡牌实例设计

为了更具体地阐述英雄卡、法术卡和战略指令卡的设计理念，以下将提供一些具体的卡牌实例，并详细说明其属性、技能和在游戏中的战略应用。

### 5.6.1 英雄卡实例

#### 英雄卡：关羽

| 属性类别 | 描述 |
| :--- | :--- |
| 武将名称 | 关羽 |
| 阵营 | 蜀 |
| 兵种类型 | 骑兵（Sword 剑） |
| 性格五行 | 金 |
| 武将定位 | 战士、输出 |
| 生命值（兵力） | 1500 |
| 能量 | 100（满能量释放技能） |
| 攻击力 | 120 |
| 护甲 | 50 |
| 谋略 | 30 |
| 魔抗 | 20 |
| 速度 | 1.2（攻击频率） |
| cost | 15 |
| 羁绊标签 | 蜀国、五虎上将、桃园结义 |
| 技能类型 | 主动技 |
| 技能名称 | 青龙偃月斩 |
| 技能描述 | 关羽挥舞青龙偃月刀，对前方扇形区域内的所有敌人造成200%攻击力的物理伤害，并使其在3秒内移动速度降低30%。能量消耗：100。 |

**战略应用**：关羽作为高输出骑兵，适合作为突进型战士，利用其高攻击力和范围伤害技能快速清理敌方后排或集结的单位。其减速效果也能有效限制敌方单位的走位，为己方远程单位创造输出环境。在SLG大地图上，关羽部队适合快速突袭敌方资源点或支援友军。

#### 英雄卡：诸葛亮

| 属性类别 | 描述 |
| :--- | :--- |
| 武将名称 | 诸葛亮 |
| 阵营 | 蜀 |
| 兵种类型 | 法师（Staff 杖） |
| 性格五行 | 水 |
| 武将定位 | 法师、控制 |
| 生命值（兵力） | 800 |
| 能量 | 100 |
| 攻击力 | 50 |
| 护甲 | 20 |
| 谋略 | 150 |
| 魔抗 | 80 |
| 速度 | 0.8 |
| cost | 12 |
| 羁绊标签 | 蜀国、卧龙、军师 |
| 技能类型 | 主动技 |
| 技能名称 | 呼风唤雨 |
| 技能描述 | 诸葛亮召唤一阵狂风暴雨，对指定区域内的所有敌人造成150%谋略值的魔法伤害，并有50%概率使其眩晕2秒。能量消耗：100。 |

**战略应用**：诸葛亮是强大的区域控制型法师，其技能能够对敌方造成范围伤害并附带控制效果，是团战中的核心输出和控场单位。由于其生命值较低，需要良好的站位保护。在SLG大地图上，诸葛亮部队适合作为防守力量，利用其谋略优势在特定地形（如河流、森林）进行伏击或防御。

### 5.6.2 法术卡实例

#### 法术卡：火计

| 属性 | 说明 | 示例 |
| :--- | :--- | :--- |
| 术法类型 | 兵家权谋 |
| 术法等级 | 玄（精良） |
| 生效维度 | 战场效果(自走棋) |
| cost | 8 |
| 效果描述 | 对指定3x3区域内的所有敌方单位造成每秒50点火焰伤害，持续5秒。 |

**战略应用**：火计是一种有效的区域伤害法术，适合在敌方单位密集时使用，造成持续性的范围伤害。在部署阶段，玩家可以预判敌方布阵，将火计施放在敌方核心单位的集结点，以削弱其血量或迫使其改变站位。

#### 法术卡：草船借箭

| 属性 | 说明 | 示例 |
| :--- | :--- | :--- |
| 术法类型 | 玄门道法 ||
| 术法等级 | 地（传奇） ||
| 生效维度 | 战场效果(自走棋) ||
| cost | 12 ||
| 效果描述 | 召唤一支箭雨，对敌方所有远程单位造成100点物理伤害，并使其在5秒内攻击力降低20%。 ||

**战略应用**：草船借箭是针对敌方远程单位的强力反制法术，能够有效削弱敌方后排输出。在面对以弓兵或法师为核心的敌方阵容时，此卡能起到关键作用，打乱敌方输出节奏。

### 5.6.3 战略指令卡实例

#### 战略指令卡：空城计

| 卡牌名称（三国剧本） | cost | 自走棋战斗效果（微观） | SLG全局效果（宏观） | 战略意义 |
| :--- | :---: | :--- | :--- | :--- |
| **空城计** | 20 | 当前战斗：己方所有单位在战斗开始时获得一个持续5秒的“无敌”状态，期间免疫所有伤害和控制效果。 | 全局：目标地块在接下来的3个SLG回合内，所有敌方部队无法进入，且该地块的资源产出提升50%。 | 战略意义：在微观战斗中提供强大的开局保护，确保己方单位能够顺利部署和释放技能。在宏观层面，则能有效阻止敌方部队的推进，保护关键资源点或为己方部队争取战略转移时间。此卡适合在劣势局面下争取喘息之机，或在关键防守战中确保胜利。 |

#### 战略指令卡：远交近攻

| 卡牌名称（三国剧本） | cost | 自走棋战斗效果（微观） | SLG全局效果（宏观） | 战略意义 |
| :--- | :---: | :--- | :--- | :--- |
| **远交近攻** | 25 | 当前战斗：己方所有单位对距离最远的敌方单位造成额外20%伤害。 | 全局：选择一个非相邻的玩家（“远交”对象），在接下来的5个SLG回合内，双方部队在SLG大地图上移动时不会触发战斗。同时，选择一个相邻的玩家（“近攻”对象），其所有地块的资源产出降低20%，持续5回合。 | 战略意义：在微观战斗中，此卡能有效针对敌方后排核心输出单位。在宏观层面，则能通过外交手段（远交）避免不必要的冲突，集中精力打击近距离的竞争对手（近攻），从而优化资源分配和战略布局。此卡适合在多方混战中，寻求局部优势或分化敌方联盟。 |





## 5.7 羁绊系统详细设计

羁绊系统是《三国：棋弈天下》中英雄卡牌组合的核心机制，它通过特定英雄、兵种、阵营或五行属性的组合，激活强大的协同效应，为玩家提供丰富的策略选择和阵容搭配深度。羁绊效果的合理运用，将是玩家在自走棋战斗中取得优势的关键。

### 5.7.1 羁绊激活机制

羁绊的激活通常基于以下几种条件：

1.  **数量激活**：当玩家在战场上部署了特定数量的、具有相同羁绊标签的英雄时，羁绊效果就会被激活。通常有多个激活阶段，部署的英雄数量越多，羁绊效果越强大。
2.  **组合激活**：某些羁绊需要特定的英雄组合才能激活，例如“桃园结义”需要刘备、关羽、张飞同时在场。


### 5.7.2 羁绊类型与效果示例

#### 5.7.2.1 阵营羁绊

阵营羁绊是游戏中最基础也是最重要的羁绊类型，它鼓励玩家围绕特定阵营的英雄进行阵容构建。

| 阵营 | 激活条件 | 羁绊效果（示例） |
| :--- | :--- | :--- |
| **蜀国** | (3) 蜀国英雄：所有蜀国英雄生命值上限+15%。<br>(6) 蜀国英雄：所有蜀国英雄生命值上限+30%，且每秒回复1%最大生命值。 |
| **魏国** | (3) 魏国英雄：所有魏国英雄攻击力+10%。<br>(6) 魏国英雄：所有魏国英雄攻击力+20%，且技能冷却时间缩短15%。 |
| **吴国** | (3) 吴国英雄：所有吴国英雄获得15点额外护甲和魔抗。<br>(6) 吴国英雄：所有吴国英雄获得30点额外护甲和魔抗，且普通攻击有20%几率眩晕敌人1秒。 |
| **群雄** | (2) 群雄英雄：所有群雄英雄攻击速度+10%。<br>(4) 群雄英雄：所有群雄英雄攻击速度+20%，且每次攻击有10%几率造成双倍伤害。 |

**战略意义**：阵营羁绊鼓励玩家形成纯粹的阵营队伍，通过数量优势获得稳定的属性加成。例如，蜀国羁绊适合构建肉盾和持续作战的阵容；魏国羁绊适合爆发输出和技能流阵容。

#### 5.7.2.2 兵种羁绊

兵种羁绊强化了特定兵种的作战能力，使玩家可以专注于某一兵种的优势。

| 兵种 | 激活条件 | 羁绊效果（示例） |
| :--- | :--- | :--- |
| **步兵** | (3) 步兵英雄：所有步兵英雄获得一个可吸收200点伤害的护盾。<br>(6) 步兵英雄：所有步兵英雄获得一个可吸收400点伤害的护盾，且护盾存在期间免疫控制效果。 |
| **骑兵** | (3) 骑兵英雄：所有骑兵英雄移动速度+20%。<br>(6) 骑兵英雄：所有骑兵英雄移动速度+40%，且首次攻击会冲锋至目标身边并造成额外伤害。 |
| **弓兵** | (3) 弓兵英雄：所有弓兵英雄攻击距离+1格。<br>(6) 弓兵英雄：所有弓兵英雄攻击距离+2格，且攻击有15%几率穿透目标，对后方单位造成50%伤害。 |
| **法师** | (3) 法师英雄：所有法师英雄技能伤害+15%。<br>(6) 法师英雄：所有法师英雄技能伤害+30%，且每次释放技能回复10点能量。 |

**战略意义**：兵种羁绊允许玩家根据战场地形和敌方阵容，选择性地强化特定兵种。例如，在开阔地形上，骑兵羁绊能快速突进；在狭窄地形或需要远程压制时，弓兵羁绊则更具优势。

#### 5.7.2.3 五行羁绊

五行羁绊引入了更深层次的策略维度，通过五行相生相克的关系，提供额外的增益或特殊效果。

| 五行 | 激活条件 | 羁绊效果（示例） |
| :--- | :--- | :--- |
| **金** | (3) 金属性英雄：所有金属性英雄护甲+20。<br>(5) 金属性英雄：所有金属性英雄护甲+40，且受到物理伤害时有20%几率反弹10%伤害。 |
| **木** | (3) 木属性英雄：所有木属性英雄每秒回复1%最大生命值。<br>(5) 木属性英雄：所有木属性英雄每秒回复2%最大生命值，且死亡时有50%几率复活并回复20%生命值。 |
| **水** | (3) 水属性英雄：所有水属性英雄技能冷却时间缩短10%。<br>(5) 水属性英雄：所有水属性英雄技能冷却时间缩短20%，且每次释放技能有15%几率使目标冰冻1秒。 |
| **火** | (3) 火属性英雄：所有火属性英雄攻击力+15%。<br>(5) 火属性英雄：所有火属性英雄攻击力+30%，且普通攻击有20%几率点燃目标，造成持续伤害。 |
| **土** | (3) 土属性英雄：所有土属性英雄最大生命值+15%。<br>(5) 土属性英雄：所有土属性英雄最大生命值+30%，且受到伤害时有10%几率生成一个护盾。 |
| **雷** | (2) 雷属性英雄：所有雷属性英雄攻击速度+15%。<br>(4) 雷属性英雄：所有雷属性英雄攻击速度+30%，且普通攻击有10%几率连锁闪电，对附近2个敌人造成伤害。 |

**战略意义**：五行羁绊为玩家提供了更精细的阵容调整空间。例如，通过组合金和土属性英雄，可以构建极度坚韧的防御阵容；组合火和雷属性英雄，则能形成高爆发的输出阵容。

#### 5.7.2.4 历史缘分羁绊

历史缘分羁绊是基于三国历史人物关系设计的特殊羁绊，通常需要特定的英雄组合才能激活，效果往往独特而强大。

| 羁绊名称 | 激活条件 | 羁绊效果（示例） |
| :--- | :--- | :--- |
| **桃园结义** | 刘备、关羽、张飞同时在场 | 所有蜀国英雄初始能量+30，且战斗开始时获得一个持续5秒的“士气高涨”效果，期间攻击力+20%。 |
| **五虎上将** | 关羽、张飞、赵云、马超、黄忠同时在场 | 所有五虎上将英雄攻击速度+25%，且每次攻击有20%几率造成目标最大生命值5%的真实伤害。 |
| **乱世枭雄** | 曹操、刘备、孙权同时在场 | 战斗开始时，随机使敌方3个单位的攻击力降低20%，持续10秒。 |
| **江东二乔** | 大乔、小乔同时在场 | 所有吴国英雄每秒回复1%最大生命值，且技能冷却时间缩短10%。 |

**战略意义**：历史缘分羁绊通常是阵容的核心，它们提供强大的全局增益或独特的战斗机制，能够显著提升整个队伍的战斗力。玩家需要通过收集和培养特定英雄来激活这些羁绊。

### 5.7.3 羁绊的UI表现

在游戏界面中，羁绊的激活状态和效果应清晰地展示给玩家：

-   **羁绊面板**：在战斗准备阶段和战斗过程中，应有一个独立的UI面板显示当前已激活的羁绊及其效果，以及距离下一阶段羁绊激活所需的英雄数量。
-   **英雄卡牌提示**：当玩家将英雄卡牌拖拽到部署区时，应有视觉提示显示该英雄能激活或强化哪些羁绊。
-   **特效表现**：羁绊激活时，应有独特的视觉和音效表现，例如“桃园结义”激活时，刘备、关羽、张飞身上出现特殊光效。

### 5.7.4 羁绊与平衡性

羁绊系统的设计需要高度关注平衡性，以确保没有某一种羁绊组合过于强大而导致游戏缺乏多样性：

-   **数值调整**：通过持续的测试和数据分析，调整不同羁绊的数值加成，使其在不同阶段和阵容中都能发挥作用。
-   **稀有度与获取难度**：强大的羁绊（如历史缘分羁绊）通常需要更稀有的英雄或更难的获取途径，以平衡其强度。
-   **反制机制**：设计一些能够反制特定羁绊的英雄技能或法术卡，增加游戏的策略深度和反制空间。

通过以上详细设计，羁绊系统将成为《三国：棋弈天下》中连接英雄、兵种、阵营和五行属性的纽带，为玩家提供无限的阵容搭配可能性和深度策略体验。





## 5.8 商店系统设计

商店系统是玩家获取英雄卡、法术卡和战略指令卡的核心途径，也是游戏经济循环的重要组成部分。一个设计良好的商店系统能够提供丰富的选择，同时通过刷新机制和概率控制，为玩家带来策略性和随机性的乐趣。

### 5.8.1 商店类型

游戏将包含两种主要商店类型：

1.  **主城商店（SLG大地图）**：
    *   **位置**：玩家在SLG大地图上控制的主城或特定资源点（如大型市集）会拥有一个常驻商店。
    *   **刷新机制**：每回合自动免费刷新一次，玩家也可以消耗金币进行额外刷新。刷新费用会随着刷新次数的增加而递增，每回合结束后重置。
    *   **商品**：主要出售英雄卡（普通、稀有）、法术卡（普通、精良），以及少量低级战略指令卡（如“小火攻”）。商品池会随着玩家等级的提升而解锁更高级的卡牌。
    *   **战略意义**：提供稳定的卡牌来源，是玩家构建和强化部队的基础。

2.  **特殊事件商店（SLG大地图/副本）**：
    *   **位置**：在特定SLG事件（如随机空投、神秘商人出现）或副本（如虎牢关副本）中限时出现。
    *   **刷新机制**：通常不可刷新，或刷新次数极为有限。
    *   **商品**：主要出售稀有、史诗级英雄卡、传奇法术卡，以及高级战略指令卡。这些卡牌往往具有改变战局的能力。
    *   **战略意义**：提供高价值卡牌的获取机会，但需要玩家投入更多资源或承担风险。

### 5.8.2 商店刷新机制与概率

商店刷新是玩家获取心仪卡牌的关键操作。刷新机制将结合玩家等级和卡牌稀有度，提供动态的商品池。

1.  **刷新费用**：
    *   首次免费刷新。
    *   后续刷新费用：1金币 -> 2金币 -> 4金币 -> 8金币...（费用呈指数级增长，每回合重置）。

2.  **卡牌稀有度与刷新概率**：
    *   卡牌分为：普通（Common）、精良（Uncommon）、稀有（Rare）、史诗（Epic）、传奇（Legendary）。
    *   玩家等级越高，商店中刷出高稀有度卡牌的概率越大。以下为示例概率表：

| 玩家等级 | 普通 | 精良 | 稀有 | 史诗 | 传奇 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **1-2** | 70% | 25% | 5% | 0% | 0% |
| **3-4** | 50% | 35% | 12% | 3% | 0% |
| **5-6** | 30% | 40% | 20% | 8% | 2% |
| **7-8** | 15% | 35% | 30% | 15% | 5% |
| **9-10** | 5% | 25% | 35% | 25% | 10% |

**设计理念**：通过等级与概率挂钩，确保玩家在游戏前期能够稳定获取基础卡牌，而在后期则有更高几率获得强力卡牌，从而平滑玩家的成长曲线。

### 5.8.3 商店商品池

商店中的商品池将根据卡牌类型和稀有度进行分类，并结合剧本设定进行动态调整。

1.  **英雄卡**：
    *   **普通英雄**：如普通步兵、弓兵、骑兵单位，用于填充阵容或作为羁绊激活的基石。
    *   **稀有英雄**：具有独特技能或更高属性的知名武将，如“周仓”、“廖化”等。
    *   **史诗/传奇英雄**：三国时期顶尖武将，如“吕布”、“赵云”，通常拥有强大的技能和羁绊，但刷新概率极低，且购买费用高昂。

2.  **法术卡**：
    *   **普通法术**：如“小火球”、“治疗术”，提供基础的战场支援。
    *   **精良法术**：如“落石术”、“鼓舞士气”，效果更强或范围更大。
    *   **稀有/传奇法术**：如“草船借箭”、“空城计”，具有改变战局的战略意义。

3.  **战略指令卡**：
    *   **低级战略指令卡**：如“小火攻”，效果范围较小或持续时间较短。
    *   **高级战略指令卡**：如“火烧连营”、“远交近攻”，具有强大的全局影响力，通常只能通过特殊事件商店或高难度PVE/Boss战获得。

### 5.8.4 商店UI/UX设计

商店界面应简洁直观，方便玩家快速浏览和购买卡牌。

-   **卡牌展示**：清晰展示卡牌的名称、类型、稀有度、属性、技能描述和购买价格。
-   **背包容量提示**：实时显示玩家当前的部队背包容量和已占用容量，方便玩家判断是否能购买更多卡牌。
-   **刷新按钮**：醒目的刷新按钮，并显示下次刷新所需的金币数量。
-   **购买确认**：购买高价值卡牌时，可弹出确认窗口，避免误操作。
-   **筛选/排序功能**：未来可考虑添加卡牌筛选（按类型、稀有度、阵营）和排序功能，方便玩家查找。

通过以上设计，商店系统将成为玩家在《三国：棋弈天下》中不断探索、成长和策略调整的重要环节，为游戏的长期可玩性提供支撑。





## 5.9 经济系统平衡性设计

经济系统是《三国：棋弈天下》的命脉，它贯穿于SLG大地图的资源管理和自走棋战斗的卡牌获取。一个健康的经济系统能够确保玩家的持续成长，维持游戏的节奏，并促进玩家间的策略博弈。本节将详细阐述经济系统的构成、资源产出与消耗机制，以及如何通过平衡性设计来优化游戏体验。

### 5.9.1 经济系统核心构成

游戏中的经济系统主要围绕以下几个核心要素展开：

1.  **金币（Gold）**：游戏中的主要货币，用于购买卡牌、刷新商店、升级玩家等级等。
2.  **资源地块（Resource Tiles）**：SLG大地图上的各种地块，如农田、矿场、市集等，是金币的主要产出源。
3.  **卡牌（Cards）**：英雄卡、法术卡、战略指令卡，它们是玩家投入金币获取的核心资产，也是战斗力的直接体现。
4.  **玩家等级（Player Level）**：影响金币产出效率、商店刷新概率和部队背包容量，是经济系统与玩家成长挂钩的关键。

### 5.9.2 资源产出机制

金币的产出主要通过以下方式实现：

1.  **地块自动产出**：
    *   **机制**：玩家每回合从其控制的地块中自动获得金币。不同类型的地块产出效率不同，例如：
        *   **农田**：提供稳定的基础金币产出。
        *   **矿场**：提供较高的金币产出，但可能受天气影响较大。
        *   **市集**：提供额外的金币加成，并可能解锁特殊商店。
    *   **影响因素**：
        *   **地块数量与类型**：控制更多高产地块的玩家将拥有更强的经济基础。
        *   **天气系统**：某些天气（如干旱）可能降低农田产出，而另一些天气（如晴朗）可能提升矿场产出。
        *   **战略指令卡**：部分战略指令卡（如“木牛流马”）可以提升特定地块的资源产出效率。

2.  **战斗奖励**：
    *   **机制**：PVE和PVP战斗胜利后，玩家会获得金币奖励。奖励金额与战斗难度、敌方强度和玩家表现挂钩。
    *   **PVE奖励**：击败NPC部队或完成副本挑战，获得基础金币和卡牌掉落。
    *   **PVP奖励**：击败其他玩家部队，除了金币外，还可能获得对方部队中的部分卡牌（作为战利品）。

3.  **事件奖励**：
    *   **机制**：完成特定任务、触发随机事件或达成成就时，获得一次性金币奖励。
    *   **示例**：首次占领某个州府、完成新手任务、击败特定Boss等。

### 5.9.3 资源消耗机制

金币的消耗主要用于以下方面：

1.  **卡牌购买**：
    *   **英雄卡**：根据稀有度和强度，价格从低到高不等。
    *   **法术卡**：根据效果强度和稀有度，价格从低到高不等。
    *   **战略指令卡**：价格昂贵，通常只能通过特殊事件或高难度挑战获得。

2.  **商店刷新**：
    *   **机制**：玩家可以通过消耗金币来刷新商店，以获取新的卡牌选择。刷新费用呈递增趋势，鼓励玩家在合理范围内刷新，避免过度依赖刷新。

3.  **玩家等级提升**：
    *   **机制**：玩家等级的提升需要消耗金币和经验值。等级提升后，玩家将获得部队背包容量增加、商店刷新概率提升等收益。

4.  **部队恢复/治疗**：
    *   **机制**：在某些剧本或模式下，战斗中受损的英雄卡可能需要消耗金币进行恢复或治疗，才能再次出战。

5.  **特殊功能/建筑**：
    *   **机制**：在SLG大地图上建造或升级某些特殊建筑（如要塞、兵营）可能需要消耗金币。

### 5.9.4 经济平衡性设计原则

为了确保游戏的公平性、策略性和可玩性，经济系统需要遵循以下平衡性设计原则：

1.  **产出与消耗的动态平衡**：
    *   **目标**：确保玩家在游戏的不同阶段都能获得足够的金币来支持其发展，同时避免金币泛滥导致游戏失去挑战性。
    *   **实现**：通过调整地块产出效率、战斗奖励、卡牌价格和刷新费用，使金币的流入和流出保持在一个健康的比例。例如，在游戏前期，PVE奖励和地块产出应能满足玩家基础卡牌的购买需求；在游戏后期，高价值卡牌的昂贵价格和递增的刷新费用则会限制玩家的无限扩张。

2.  **风险与回报的匹配**：
    *   **目标**：高风险的行动（如挑战高难度Boss、PVP冲突）应带来更高的金币和稀有卡牌奖励，以激励玩家进行更具挑战性的尝试。
    *   **实现**：设计不同难度的PVE副本和Boss战，其奖励与难度成正比。PVP战斗的胜利者应获得比PVE更丰厚的奖励，以鼓励玩家间的对抗。

3.  **新手友好与后期深度**：
    *   **目标**：确保新手玩家能够快速上手并积累初期资源，同时为资深玩家提供深度的经济策略空间。
    *   **实现**：游戏前期提供稳定的基础金币产出和易于获取的普通卡牌。随着玩家等级的提升，解锁更复杂的经济机制（如特殊商店、战略指令卡对经济的影响），并增加高风险高回报的经济决策点。

4.  **防止经济雪球效应**：
    *   **目标**：避免少数玩家在早期获得巨大经济优势后，迅速滚雪球般地碾压其他玩家。
    *   **实现**：
        *   **追赶机制**：为落后玩家提供一些追赶机制，例如，被击败的玩家可能获得一些基础资源或低保金币，使其不至于完全出局。
        *   **资源限制**：限制单个玩家可控制的地块数量上限，或引入维护费用，防止过度扩张。
        *   **PVP惩罚**：被击败的玩家会失去地块控制权，这本身就是一种经济惩罚，但应避免过于严厉导致玩家无法翻身。

5.  **与卡牌系统的联动**：
    *   **目标**：经济系统应与卡牌系统紧密结合，确保玩家有足够的经济能力来构建和调整其卡组。
    *   **实现**：卡牌的价格应与其实用性、稀有度和强度相匹配。商店刷新概率的设计应鼓励玩家在不同阶段尝试不同的卡牌组合，而不是只追求少数几张顶级卡牌。

通过对金币产出、消耗机制的精细设计，并遵循上述平衡性原则，《三国：棋弈天下》的经济系统将为玩家提供一个公平、富有挑战性和策略深度的游戏环境，确保每局游戏都能带来独特的经济博弈体验。





## 5.10 UI/UX设计指导

用户界面（UI）和用户体验（UX）是游戏成功的关键因素，尤其对于《三国：棋弈天下》这种融合了SLG宏观战略和自走棋微观战术的复杂游戏。良好的UI/UX设计能够降低玩家的学习成本，提升操作效率，并增强游戏的沉浸感和乐趣。本节将提供UI/UX设计的基本原则和关键模块的设计指导。

### 5.10.1 UI/UX设计基本原则

1.  **清晰直观**：信息呈现应简洁明了，避免视觉混乱。关键信息（如资源、回合、单位状态）应一目了然。
2.  **反馈及时**：玩家的每一次操作都应得到即时、明确的视觉或听觉反馈，例如点击按钮后的高亮、单位移动后的路径显示、技能释放后的特效。
3.  **一致性**：保持界面元素、图标、字体、颜色和交互模式的一致性，减少玩家的认知负担。
4.  **效率优先**：减少不必要的操作步骤，优化常用功能的访问路径，例如一键购买、快速部署。
5.  **沉浸感**：UI应尽可能融入游戏世界观，避免过于突兀。通过动画、音效和背景元素增强沉浸感。
6.  **可扩展性**：考虑到未来可能新增的卡牌、系统和剧本，UI设计应具备良好的可扩展性，方便后续内容的集成。

### 5.10.2 关键UI模块设计指导

#### 5.10.2.1 SLG大地图界面

SLG大地图是玩家进行宏观战略操作的主要界面，其UI设计应突出战略信息和操作便捷性。

-   **核心信息区（顶部/底部常驻）**：
    *   **玩家资源**：金币、当前回合数、总回合数、玩家等级、部队背包容量（Cost Cap）等核心资源和状态应常驻显示。
    *   **回合信息**：清晰显示当前回合阶段（如“行动阶段”、“结算阶段”）和剩余时间。
    *   **天气预报**：可视化显示当前天气和未来几回合的天气预报（如气候轮盘）。
-   **地图操作与信息展示**：
    *   **地块信息**：鼠标悬停或点击地块时，应弹出详细信息面板，显示地块类型、资源产出、当前驻扎部队、地形效果等。
    *   **部队信息**：地图上的部队图标应清晰显示其归属、数量和状态。点击部队可查看详细信息和可执行操作（如移动、驻扎、进入战斗）。
    *   **战略指令卡槽**：在地图界面上，应有专门的区域显示玩家当前持有的战略指令卡，并可直接点击使用。
-   **事件与提示**：
    *   **事件弹窗**：当触发随机事件、副本开启、空投出现等重要事件时，应有醒目的弹窗提示，并提供快速跳转或处理的选项。
    *   **战报提示**：每回合结算后，应有简要的战报提示，告知玩家战斗结果、地块归属变化、资源增减等。

#### 5.10.2.2 商店界面

商店界面应方便玩家浏览、筛选和购买卡牌。

-   **卡牌展示区**：
    *   **网格布局**：采用清晰的网格布局展示可购买的卡牌，每张卡牌应包含名称、稀有度、类型、价格、关键属性和技能图标。
    *   **详细信息**：鼠标悬停或点击卡牌时，弹出详细信息面板，展示完整的属性、技能描述和羁绊标签。
-   **刷新与购买**：
    *   **刷新按钮**：醒目的“刷新”按钮，显示下次刷新所需金币。刷新后应有视觉和听觉反馈。
    *   **购买按钮**：每张卡牌下方应有清晰的“购买”按钮，点击后即时扣除金币并添加到玩家背包。
-   **背包容量提示**：在商店界面常驻显示玩家当前的部队背包容量和已占用容量，方便玩家决策。

#### 5.10.2.3 自走棋战斗部署界面

部署界面是玩家进行微观战术布局的核心，应强调操作的精准性和效率。

-   **棋盘显示**：
    *   **清晰网格**：六边形网格棋盘应清晰可见，不同地形元素应有明显的视觉区分。
    *   **部署区标识**：玩家的部署区域应有明确的边界和颜色标识。
-   **卡牌选择区**：
    *   **部队包**：显示玩家当前部队包中所有可供选择的英雄卡、法术卡和战略指令卡。卡牌应有清晰的图标和占用值。
    *   **拖拽部署**：玩家应能通过拖拽方式将卡牌从选择区放置到棋盘上的部署区。拖拽过程中应有预览效果。
-   **时间限制与倒计时**：
    *   **醒目倒计时**：在界面顶部或显眼位置显示部署阶段的剩余时间，并有视觉和听觉上的倒计时警告。
-   **羁绊与属性预览**：
    *   **实时更新**：当玩家放置或移除单位时，羁绊面板应实时更新，显示当前激活的羁绊效果和距离下一阶段激活所需的单位数量。
    *   **单位属性**：点击棋盘上的单位，可查看其详细属性和技能。

#### 5.10.2.4 战斗结算与战报界面

战斗结算界面应清晰地展示战斗结果、奖励和对SLG大地图的影响。

-   **胜负结果**：醒目地显示“胜利”或“失败”字样，并配以相应的音效和动画。
-   **奖励展示**：详细列出获得的金币、经验值、卡牌掉落等奖励。
-   **战报详情**：提供战斗过程的简要回顾，包括双方参战单位、造成的伤害、治疗量、关键技能释放等数据。
-   **SLG影响**：明确提示地块归属变化、战略指令卡效果生效等对大地图的影响。

### 5.10.3 视觉风格与艺术指导

-   **整体风格**：建议采用写实与卡通结合的风格，既能体现三国历史的厚重感，又能保持游戏的轻松和趣味性。色彩运用应丰富但不失协调。
-   **图标设计**：图标应简洁明了，具有辨识度，能够直观地表达其功能或含义。
-   **动画与特效**：适当运用动画和特效来增强反馈和表现力，但避免过度使用导致视觉疲劳或性能问题。
-   **音效与背景音乐**：为不同操作、事件和战斗阶段设计合适的音效和背景音乐，提升游戏的沉浸感和氛围。

通过遵循以上UI/UX设计指导，我们将能够为玩家提供一个易于上手、操作流畅、信息清晰且富有沉浸感的游戏体验，从而更好地展现《三国：棋弈天下》的策略深度和游戏乐趣。





## 8. 游戏状态机与系统架构

为了确保《三国：棋弈天下》复杂的游戏逻辑能够清晰、高效地运行，我们需要设计一个健壮的游戏状态机和模块化的系统架构。这将有助于开发团队理解游戏流程，实现各系统间的顺畅交互，并为未来的功能扩展提供便利。

### 8.1 游戏状态机设计

游戏的核心运行逻辑可以通过一个多层嵌套的状态机来表示，它清晰地定义了游戏在不同模式下的行为和状态转换。主要状态包括：

1.  **全局游戏状态（Global Game State）**：
    *   **匹配阶段（Matching State）**：玩家登录、选择剧本、进入匹配队列、等待其他玩家加入。
    *   **游戏准备阶段（Game Setup State）**：所有玩家匹配成功，系统分配身份、初始地块、初始化卡组和资源。
    *   **游戏进行中（In-Game State）**：游戏的核心循环，包含SLG战略模式和自走棋战斗模式的切换。
    *   **游戏结算阶段（Game End State）**：达成胜利条件，进行积分结算、奖励发放、数据保存。

2.  **游戏进行中子状态（In-Game Sub-States）**：
    *   **SLG战略模式（Strategic Mode）**：
        *   **回合开始（Turn Start）**：自动资源收集、天气更新、事件检查。
        *   **玩家行动（Player Action）**：玩家提交移动、购买、使用战略指令卡等指令。
        *   **指令结算（Command Resolution）**：系统处理所有玩家提交的指令，判定是否触发战斗。
    *   **自走棋战斗模式（Combat Mode）**：
        *   **战斗初始化（Combat Init）**：生成棋盘、加载部队、显示预览。
        *   **部署阶段（Deployment Phase）**：玩家选择卡牌、布阵、激活法术/战略卡。
        *   **自动战斗（Auto Combat）**：系统根据AI逻辑自动执行战斗。
        *   **战斗结算（Combat Resolution）**：判定胜负、发放奖励、更新SLG大地图状态。

以下是游戏状态机的Mermaid流程图表示：

```mermaid
graph TD
    subgraph Global Game State
        A[玩家登录/注册] --> B{匹配成功?}
        B -- 是 --> C[游戏准备阶段]
        C --> D[游戏进行中]
        D --> E{达成胜利条件?}
        E -- 是 --> F[游戏结算阶段]
        F --> G[返回大厅]
        E -- 否 --> D
    end

    subgraph In-Game State
        D --> H[SLG战略模式]
        H --> I[回合开始]
        I --> J[玩家行动]
        J --> K{是否触发战斗?}
        K -- 否 --> L[指令结算]
        L --> I
        K -- 是 --> M[自走棋战斗模式]
        M --> N[战斗初始化]
        N --> O[部署阶段]
        O --> P[自动战斗]
        P --> Q[战斗结算]
        Q --> L
    end
```

**状态机设计原则**：

-   **清晰的入口和出口**：每个状态都有明确的进入和退出条件。
-   **事件驱动**：状态转换由特定的游戏事件触发（如玩家操作、时间流逝、战斗结果）。
-   **层次化**：复杂的状态可以分解为子状态，使得逻辑更易于管理和理解。
-   **可恢复性**：在网络中断或客户端崩溃时，游戏状态应能从服务器端恢复，确保玩家体验的连续性。

### 8.2 数据库架构设计

游戏数据将分为两大部分：**持久化数据（Persistent Data）**和**会话数据（Session Data）**。持久化数据存储玩家账号、历史战绩、解锁内容等，而会话数据则存储当前进行中的游戏局（Match）的所有实时状态。考虑到游戏类型，建议采用关系型数据库（如PostgreSQL）结合NoSQL数据库（如Redis）的混合方案。

#### 8.2.1 持久化数据（PostgreSQL）

用于存储不随单局游戏结束而消失的数据，如玩家档案、卡牌库、历史战绩等。PostgreSQL的事务支持和数据完整性保证非常适合此类数据。

-   **`Users` 表**：
    *   `user_id` (PK, UUID)
    *   `username` (VARCHAR, UNIQUE)
    *   `password_hash` (VARCHAR)
    *   `email` (VARCHAR, UNIQUE)
    *   `created_at` (TIMESTAMP)
    *   `last_login` (TIMESTAMP)
    *   `level` (INT) - 玩家总等级
    *   `experience` (INT) - 玩家总经验
    *   `total_gold` (INT) - 玩家总金币（游戏外）
    *   `unlocked_scripts` (JSONB) - 已解锁的剧本列表
    *   `achievements` (JSONB) - 成就列表

-   **`Cards` 表**：
    *   `card_id` (PK, UUID)
    *   `user_id` (FK, UUID) - 拥有者
    *   `card_template_id` (FK, UUID) - 关联卡牌模板
    *   `rarity` (VARCHAR) - 稀有度
    *   `current_health` (INT) - 英雄卡当前兵力（如果需要持久化兵力状态）
    *   `is_locked` (BOOLEAN) - 是否锁定（例如，在部队背包中）

-   **`CardTemplates` 表**：
    *   `template_id` (PK, UUID)
    *   `name` (VARCHAR)
    *   `type` (VARCHAR) - 英雄/法术/战略指令
    *   `rarity` (VARCHAR)
    *   `cost` (INT)
    *   `attributes` (JSONB) - 存储英雄属性、法术效果、战略指令效果等详细数据
    *   `skills` (JSONB) - 技能描述
    *   `bonds` (JSONB) - 羁绊标签

-   **`MatchHistory` 表**：
    *   `match_id` (PK, UUID)
    *   `script_id` (FK, UUID) - 剧本ID
    *   `start_time` (TIMESTAMP)
    *   `end_time` (TIMESTAMP)
    *   `winner_user_id` (FK, UUID)
    *   `player_results` (JSONB) - 存储每位玩家在对局中的表现（如最终金币、等级、击杀数等）
    *   `match_log` (JSONB) - 关键事件日志（可选，用于回放或分析）

#### 8.2.2 会话数据（Redis）

用于存储单局游戏（Match）的实时、高频变动数据。Redis的内存存储和快速读写能力非常适合游戏中的实时状态同步。

-   **`Match:{match_id}` (Hash)**：存储当前对局的基本信息
    *   `script_id`
    *   `current_turn`
    *   `current_phase` (SLG/Combat)
    *   `turn_timer_end`
    *   `players` (JSON) - 参与玩家列表及身份
    *   `map_state` (JSON) - 大地图地块所有权、资源状态、天气状态等
    *   `pending_actions` (JSON) - 玩家提交但尚未结算的行动指令

-   **`Player:{user_id}:{match_id}` (Hash)**：存储玩家在当前对局中的实时状态
    *   `identity`
    *   `gold`
    *   `level`
    *   `current_cost_cap`
    *   `owned_territories` (Set/List) - 玩家控制的地块ID列表
    *   `army_units` (JSON) - 玩家部队中的卡牌实例（英雄、法术、战略指令）及其当前状态（如英雄兵力、能量）
    *   `inventory_cards` (JSON) - 玩家背包中的卡牌实例
    *   `shop_state` (JSON) - 玩家商店的当前商品和刷新状态

-   **`Combat:{combat_id}` (Hash)**：存储当前进行中的自走棋战斗的实时状态
    *   `map_layout` (JSON) - 战斗棋盘地形
    *   `units_on_board` (JSON) - 棋盘上所有单位的实时位置、兵力、能量、Buff/Debuff
    *   `combat_log` (List) - 战斗过程中的事件日志（用于回放或结算）

**数据库设计原则**：

-   **数据分离**：将持久化和会话数据分离，优化读写性能和数据一致性。
-   **范式化与非范式化结合**：持久化数据适当范式化以保证数据完整性，会话数据可适当非范式化以提高查询效率。
-   **索引优化**：为常用查询字段建立索引，提高查询速度。
-   **备份与恢复**：建立完善的数据库备份和恢复机制，确保数据安全。

### 8.3 网络通信协议设计

游戏将采用WebSocket协议进行实时通信，以支持多人在线同步操作和实时战斗。基于Colyseus框架，通信将围绕房间（Room）和状态同步（State Synchronization）模型展开。

-   **连接与认证**：
    *   客户端通过HTTP请求登录/注册，获取认证Token。
    *   客户端使用Token通过WebSocket连接到Colyseus服务器。
    *   服务器验证Token，将玩家加入到相应的游戏房间。

-   **房间管理**：
    *   **创建/加入房间**：玩家可以创建私人房间、加入公开房间或通过匹配系统自动加入房间。
    *   **房间状态**：房间会广播其状态变化（如玩家加入/离开、游戏开始/结束）。

-   **状态同步**：
    *   **Schema定义**：使用Colyseus的Schema定义游戏状态结构，确保客户端和服务器端的数据结构一致性。
    *   **增量更新**：服务器只发送状态的增量更新，减少网络带宽消耗。
    *   **客户端预测**：客户端可以进行本地预测，减少延迟感，服务器端进行权威验证。

-   **消息类型**：
    *   **客户端到服务器（Client-to-Server）**：
        *   `player_action`：玩家提交的SLG行动指令（移动、购买、使用卡牌）。
        *   `deploy_units`：玩家在部署阶段提交的布阵信息。
        *   `refresh_shop`：刷新商店请求。
        *   `chat_message`：聊天消息。
    *   **服务器到客户端（Server-to-Client）**：
        *   `game_state_update`：游戏全局状态更新（回合切换、天气变化、事件触发）。
        *   `map_update`：SLG大地图状态更新（地块归属、资源变化）。
        *   `combat_start`：战斗开始通知，附带棋盘信息。
        *   `combat_state_update`：自走棋战斗实时状态更新（单位位置、兵力、技能释放）。
        *   `combat_end`：战斗结束通知，附带结算结果。
        *   `player_data_update`：玩家自身数据更新（金币、等级、背包）。
        *   `chat_message`：其他玩家的聊天消息。

**网络通信设计原则**：

-   **实时性**：确保关键游戏状态的实时同步，减少玩家操作延迟。
-   **安全性**：所有客户端请求都应在服务器端进行严格验证，防止作弊。
-   **可伸缩性**：设计能够支持大量并发玩家和游戏房间的架构。
-   **容错性**：处理网络波动、断线重连等异常情况，保证游戏体验的流畅性。

### 8.4 系统架构图

以下是《三国：棋弈天下》的整体系统架构概览图，展示了主要组件及其相互关系：

```mermaid
graph LR
    subgraph Client
        A[Web Browser / Desktop App] --> B[Phaser3 游戏引擎]
        B --> C[UI/UX 模块]
        C --> D[游戏逻辑 (客户端预测)]
    end

    subgraph Backend
        E[Colyseus 游戏服务器] --> F[游戏房间管理]
        F --> G[游戏状态管理]
        G --> H[游戏逻辑 (权威验证)]
        H --> I[匹配服务]
        H --> J[卡牌/商店服务]
        H --> K[战斗模拟服务]
        I --> E
        J --> E
        K --> E
    end

    subgraph Database (数据层)
        L[PostgreSQL (持久化数据)]
        M[Redis (会话数据)]
    end

    A -- WebSocket --> E
    E -- REST/RPC --> L
    E -- Redis Pub/Sub --> M
    L -- 数据存储 --> M
```

**系统架构说明**：

-   **客户端（Client）**：基于Web浏览器或桌面应用，使用Phaser3作为游戏引擎，负责渲染游戏画面、处理用户输入、进行客户端预测和展示UI/UX。
-   **后端（Backend）**：核心是Colyseus游戏服务器，负责管理游戏房间、同步游戏状态、执行权威游戏逻辑（如战斗模拟、卡牌/商店逻辑、匹配）。各个服务模块化，便于扩展和维护。
-   **数据层（Database）**：PostgreSQL用于存储玩家账号、卡牌模板、历史战绩等持久化数据；Redis用于存储当前进行中的游戏局的实时状态，确保高性能的读写和状态同步。

这种架构设计旨在提供一个高性能、可伸缩、易于维护的解决方案，以支持《三国：棋弈天下》复杂的游戏机制和多人在线体验。

