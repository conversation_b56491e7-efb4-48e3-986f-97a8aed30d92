{"name": "@multiplayer-game/server", "version": "1.0.0", "description": "多人游戏服务器 - 基于Colyseus", "main": "dist/index.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"colyseus": "^0.15.0", "@colyseus/schema": "^2.0.0", "express": "^4.18.2", "cors": "^2.8.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.0", "@multiplayer-game/shared": "workspace:*"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/bcryptjs": "^2.4.2", "@types/jsonwebtoken": "^9.0.2", "@types/node": "^20.0.0", "typescript": "^5.0.0", "ts-node-dev": "^2.0.0", "rimraf": "^5.0.0"}}