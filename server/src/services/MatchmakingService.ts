import { GAME_CONFIG, GameState } from '@multiplayer-game/shared';
import { RoomService } from './RoomService';

export interface MatchRequest {
  userId: string;
  username: string;
  timestamp: number;
  preferences?: {
    maxPlayers?: number;
    gameMode?: string;
  };
}

export class MatchmakingService {
  private static instance: MatchmakingService;
  private matchQueue: MatchRequest[] = [];
  private roomService = RoomService.getInstance();
  private matchmakingInterval: NodeJS.Timeout | null = null;

  private constructor() {
    // 启动匹配处理循环
    this.startMatchmaking();
  }

  public static getInstance(): MatchmakingService {
    if (!MatchmakingService.instance) {
      MatchmakingService.instance = new MatchmakingService();
    }
    return MatchmakingService.instance;
  }

  // 添加玩家到匹配队列
  public addToQueue(request: MatchRequest): void {
    // 检查玩家是否已在队列中
    const existingIndex = this.matchQueue.findIndex(req => req.userId === request.userId);
    if (existingIndex !== -1) {
      // 更新现有请求
      this.matchQueue[existingIndex] = request;
    } else {
      // 添加新请求
      this.matchQueue.push(request);
    }

    console.log(`🔍 玩家 ${request.username} 加入匹配队列，当前队列长度: ${this.matchQueue.length}`);
  }

  // 从匹配队列中移除玩家
  public removeFromQueue(userId: string): boolean {
    const index = this.matchQueue.findIndex(req => req.userId === userId);
    if (index !== -1) {
      const removed = this.matchQueue.splice(index, 1)[0];
      console.log(`❌ 玩家 ${removed.username} 离开匹配队列`);
      return true;
    }
    return false;
  }

  // 检查玩家是否在队列中
  public isInQueue(userId: string): boolean {
    return this.matchQueue.some(req => req.userId === userId);
  }

  // 获取队列状态
  public getQueueStatus() {
    return {
      queueLength: this.matchQueue.length,
      estimatedWaitTime: this.calculateEstimatedWaitTime(),
      playersInQueue: this.matchQueue.map(req => ({
        username: req.username,
        waitTime: Date.now() - req.timestamp
      }))
    };
  }

  // 启动匹配处理
  private startMatchmaking(): void {
    this.matchmakingInterval = setInterval(() => {
      this.processMatches();
      this.cleanupExpiredRequests();
    }, 2000); // 每2秒处理一次匹配

    console.log('🎯 匹配系统启动');
  }

  // 停止匹配处理
  public stopMatchmaking(): void {
    if (this.matchmakingInterval) {
      clearInterval(this.matchmakingInterval);
      this.matchmakingInterval = null;
    }
    console.log('⏹️ 匹配系统停止');
  }

  // 处理匹配
  private processMatches(): void {
    if (this.matchQueue.length === 0) return;

    // 首先尝试将玩家匹配到现有房间
    this.matchToExistingRooms();

    // 如果队列中有足够的玩家，创建新房间
    if (this.matchQueue.length >= GAME_CONFIG.MIN_PLAYERS_TO_START) {
      this.createMatchRoom();
    }
  }

  // 匹配到现有房间
  private matchToExistingRooms(): void {
    const availableRooms = this.roomService.getAvailableRooms();
    
    for (const room of availableRooms) {
      const availableSlots = room.maxPlayers - room.playerCount;
      if (availableSlots > 0 && this.matchQueue.length > 0) {
        const playersToMatch = this.matchQueue.splice(0, Math.min(availableSlots, this.matchQueue.length));
        
        // 这里应该通知客户端加入房间
        // 实际实现中需要通过WebSocket或其他方式通知客户端
        console.log(`🎮 匹配 ${playersToMatch.length} 名玩家到房间 ${room.id}`);
        
        playersToMatch.forEach(player => {
          console.log(`  - ${player.username} 匹配到房间 ${room.id}`);
        });
      }
    }
  }

  // 创建匹配房间
  private createMatchRoom(): void {
    const playersForNewRoom = this.matchQueue.splice(0, GAME_CONFIG.MAX_PLAYERS_PER_ROOM);
    
    if (playersForNewRoom.length >= GAME_CONFIG.MIN_PLAYERS_TO_START) {
      console.log(`🏠 为 ${playersForNewRoom.length} 名玩家创建新匹配房间`);
      
      // 这里应该创建新房间并通知客户端
      // 实际实现中需要通过Colyseus创建房间
      playersForNewRoom.forEach(player => {
        console.log(`  - ${player.username} 将加入新房间`);
      });
    } else {
      // 如果人数不够，将玩家放回队列
      this.matchQueue.unshift(...playersForNewRoom);
    }
  }

  // 清理过期的匹配请求
  private cleanupExpiredRequests(): void {
    const now = Date.now();
    const timeout = GAME_CONFIG.MATCH_TIMEOUT;
    
    const expiredRequests = this.matchQueue.filter(req => now - req.timestamp > timeout);
    
    if (expiredRequests.length > 0) {
      this.matchQueue = this.matchQueue.filter(req => now - req.timestamp <= timeout);
      
      expiredRequests.forEach(req => {
        console.log(`⏰ 匹配请求超时: ${req.username}`);
      });
    }
  }

  // 计算预估等待时间
  private calculateEstimatedWaitTime(): number {
    if (this.matchQueue.length === 0) return 0;
    
    // 简单的估算：基于当前队列长度和历史数据
    const baseWaitTime = 10000; // 10秒基础等待时间
    const queueFactor = Math.max(0, this.matchQueue.length - GAME_CONFIG.MIN_PLAYERS_TO_START);
    
    return baseWaitTime + (queueFactor * 5000); // 每多一个玩家增加5秒
  }

  // 获取匹配统计
  public getStats() {
    return {
      queueLength: this.matchQueue.length,
      averageWaitTime: this.calculateEstimatedWaitTime(),
      totalMatches: 0, // 这里可以添加统计逻辑
      successRate: 0.95 // 这里可以添加成功率统计
    };
  }
}
