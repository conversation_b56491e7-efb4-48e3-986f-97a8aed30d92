import { Room as ColyseusRoom } from 'colyseus';
import { Room, GameState, GAME_CONFIG } from '@multiplayer-game/shared';

export interface RoomInfo {
  id: string;
  name: string;
  hostId: string;
  hostUsername: string;
  playerCount: number;
  maxPlayers: number;
  gameState: GameState;
  isPrivate: boolean;
  createdAt: Date;
}

export class RoomService {
  private static instance: RoomService;
  private rooms = new Map<string, RoomInfo>();

  private constructor() {}

  public static getInstance(): RoomService {
    if (!RoomService.instance) {
      RoomService.instance = new RoomService();
    }
    return RoomService.instance;
  }

  // 注册房间
  public registerRoom(roomId: string, roomInfo: Omit<RoomInfo, 'id'>): void {
    this.rooms.set(roomId, {
      id: roomId,
      ...roomInfo
    });
  }

  // 更新房间信息
  public updateRoom(roomId: string, updates: Partial<RoomInfo>): void {
    const room = this.rooms.get(roomId);
    if (room) {
      this.rooms.set(roomId, { ...room, ...updates });
    }
  }

  // 移除房间
  public removeRoom(roomId: string): void {
    this.rooms.delete(roomId);
  }

  // 获取房间信息
  public getRoom(roomId: string): RoomInfo | undefined {
    return this.rooms.get(roomId);
  }

  // 获取所有公开房间
  public getPublicRooms(): RoomInfo[] {
    return Array.from(this.rooms.values())
      .filter(room => !room.isPrivate)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  // 获取可加入的房间（未满且在等待状态）
  public getAvailableRooms(): RoomInfo[] {
    return this.getPublicRooms().filter(room => 
      room.playerCount < room.maxPlayers && 
      room.gameState === GameState.WAITING
    );
  }

  // 查找合适的匹配房间
  public findMatchRoom(): RoomInfo | null {
    const availableRooms = this.getAvailableRooms();
    
    // 优先选择人数较多的房间
    availableRooms.sort((a, b) => b.playerCount - a.playerCount);
    
    return availableRooms.length > 0 ? availableRooms[0] : null;
  }

  // 获取房间统计
  public getStats() {
    const rooms = Array.from(this.rooms.values());
    return {
      totalRooms: rooms.length,
      activeRooms: rooms.filter(r => r.gameState === GameState.PLAYING).length,
      waitingRooms: rooms.filter(r => r.gameState === GameState.WAITING).length,
      totalPlayers: rooms.reduce((sum, room) => sum + room.playerCount, 0)
    };
  }

  // 清理空房间
  public cleanupEmptyRooms(): void {
    const emptyRooms = Array.from(this.rooms.entries())
      .filter(([_, room]) => room.playerCount === 0)
      .map(([roomId, _]) => roomId);
    
    emptyRooms.forEach(roomId => {
      this.removeRoom(roomId);
      console.log(`🧹 清理空房间: ${roomId}`);
    });
  }
}
