import { GameState, Player, GAME_CONFIG } from '@multiplayer-game/shared';

export interface Bot {
  id: string;
  username: string;
  isReady: boolean;
  position: { x: number; y: number };
  score: number;
  behavior: BotBehavior;
  lastAction: number;
}

export enum BotBehavior {
  PASSIVE = 'passive',     // 被动，只是站着
  ACTIVE = 'active',       // 主动，会移动和互动
  AGGRESSIVE = 'aggressive' // 激进，会快速行动
}

export class BotService {
  private static instance: BotService;
  private bots: Map<string, Bot> = new Map();
  private botNames = [
    '智能助手Alpha', '机器人Beta', '自动玩家Gamma', '虚拟战士Delta',
    '数字精灵Epsilon', '代码骑士Zeta', '算法大师Eta', '程序守护Theta'
  ];

  private constructor() {}

  public static getInstance(): BotService {
    if (!BotService.instance) {
      BotService.instance = new BotService();
    }
    return BotService.instance;
  }

  // 创建机器人
  public createBot(behavior: BotBehavior = BotBehavior.ACTIVE): Bot {
    const botId = `bot_${Date.now()}_${Math.random().toString(36).substring(2)}`;
    const availableNames = this.botNames.filter(name => 
      !Array.from(this.bots.values()).some(bot => bot.username === name)
    );
    
    const botName = availableNames.length > 0 
      ? availableNames[Math.floor(Math.random() * availableNames.length)]
      : `机器人${Math.floor(Math.random() * 1000)}`;

    const bot: Bot = {
      id: botId,
      username: botName,
      isReady: true,
      position: {
        x: Math.random() * (GAME_CONFIG.GAME_WIDTH - 100) + 50,
        y: Math.random() * (GAME_CONFIG.GAME_HEIGHT - 100) + 50
      },
      score: 0,
      behavior,
      lastAction: Date.now()
    };

    this.bots.set(botId, bot);
    console.log(`🤖 创建机器人: ${botName} (${behavior})`);
    return bot;
  }

  // 移除机器人
  public removeBot(botId: string): boolean {
    const bot = this.bots.get(botId);
    if (bot) {
      this.bots.delete(botId);
      console.log(`🗑️ 移除机器人: ${bot.username}`);
      return true;
    }
    return false;
  }

  // 获取机器人
  public getBot(botId: string): Bot | undefined {
    return this.bots.get(botId);
  }

  // 获取所有机器人
  public getAllBots(): Bot[] {
    return Array.from(this.bots.values());
  }

  // 机器人自动准备
  public autoPrepareBot(botId: string, delay: number = 1000): void {
    setTimeout(() => {
      const bot = this.bots.get(botId);
      if (bot && !bot.isReady) {
        bot.isReady = true;
        bot.lastAction = Date.now();
        console.log(`🤖 ${bot.username} 自动准备完成`);
      }
    }, delay);
  }

  // 机器人自动移动
  public auteMoveBot(botId: string): { x: number; y: number } | null {
    const bot = this.bots.get(botId);
    if (!bot) return null;

    const now = Date.now();
    const timeSinceLastAction = now - bot.lastAction;

    // 根据行为类型决定移动频率
    let moveInterval = 2000; // 默认2秒移动一次
    switch (bot.behavior) {
      case BotBehavior.PASSIVE:
        moveInterval = 5000; // 5秒移动一次
        break;
      case BotBehavior.ACTIVE:
        moveInterval = 2000; // 2秒移动一次
        break;
      case BotBehavior.AGGRESSIVE:
        moveInterval = 1000; // 1秒移动一次
        break;
    }

    if (timeSinceLastAction < moveInterval) {
      return null; // 还没到移动时间
    }

    // 生成新的随机位置
    const moveDistance = 50 + Math.random() * 100; // 50-150像素的移动距离
    const angle = Math.random() * Math.PI * 2;
    
    let newX = bot.position.x + Math.cos(angle) * moveDistance;
    let newY = bot.position.y + Math.sin(angle) * moveDistance;

    // 边界检查
    newX = Math.max(50, Math.min(GAME_CONFIG.GAME_WIDTH - 50, newX));
    newY = Math.max(100, Math.min(GAME_CONFIG.GAME_HEIGHT - 100, newY));

    bot.position.x = newX;
    bot.position.y = newY;
    bot.lastAction = now;

    return { x: newX, y: newY };
  }

  // 批量创建机器人
  public createMultipleBots(count: number, behavior?: BotBehavior): Bot[] {
    const bots: Bot[] = [];
    for (let i = 0; i < count; i++) {
      const botBehavior = behavior || this.getRandomBehavior();
      bots.push(this.createBot(botBehavior));
    }
    return bots;
  }

  // 随机获取机器人行为
  private getRandomBehavior(): BotBehavior {
    const behaviors = [BotBehavior.PASSIVE, BotBehavior.ACTIVE, BotBehavior.AGGRESSIVE];
    return behaviors[Math.floor(Math.random() * behaviors.length)];
  }

  // 清理所有机器人
  public clearAllBots(): void {
    const botCount = this.bots.size;
    this.bots.clear();
    console.log(`🧹 清理了 ${botCount} 个机器人`);
  }

  // 获取机器人统计
  public getStats() {
    const bots = this.getAllBots();
    return {
      totalBots: bots.length,
      readyBots: bots.filter(bot => bot.isReady).length,
      passiveBots: bots.filter(bot => bot.behavior === BotBehavior.PASSIVE).length,
      activeBots: bots.filter(bot => bot.behavior === BotBehavior.ACTIVE).length,
      aggressiveBots: bots.filter(bot => bot.behavior === BotBehavior.AGGRESSIVE).length
    };
  }

  // 机器人智能行为更新
  public updateBotBehaviors(gameState: GameState): void {
    this.bots.forEach(bot => {
      switch (gameState) {
        case GameState.WAITING:
          // 在等待状态下，机器人有概率自动准备
          if (!bot.isReady && Math.random() < 0.3) { // 30%概率准备
            this.autoPrepareBot(bot.id, Math.random() * 3000 + 1000); // 1-4秒后准备
          }
          break;
        case GameState.PLAYING:
          // 在游戏状态下，机器人会移动
          this.auteMoveBot(bot.id);
          break;
      }
    });
  }
}
