import { User } from '@multiplayer-game/shared';

// 简单的内存用户存储（生产环境应使用数据库）
export class UserService {
  private static instance: UserService;
  private users = new Map<string, { id: string; username: string; password: string }>();
  private onlineUsers = new Map<string, User>(); // 在线用户

  private constructor() {}

  public static getInstance(): UserService {
    if (!UserService.instance) {
      UserService.instance = new UserService();
    }
    return UserService.instance;
  }

  // 创建用户
  public createUser(id: string, username: string, hashedPassword: string): void {
    this.users.set(username, {
      id,
      username,
      password: hashedPassword
    });
  }

  // 根据用户名查找用户
  public findUserByUsername(username: string) {
    return this.users.get(username);
  }

  // 检查用户名是否存在
  public userExists(username: string): boolean {
    return this.users.has(username);
  }

  // 用户上线
  public setUserOnline(user: User): void {
    this.onlineUsers.set(user.id, {
      ...user,
      isOnline: true
    });
  }

  // 用户下线
  public setUserOffline(userId: string): void {
    const user = this.onlineUsers.get(userId);
    if (user) {
      user.isOnline = false;
      user.currentRoomId = undefined;
    }
  }

  // 获取在线用户
  public getOnlineUser(userId: string): User | undefined {
    return this.onlineUsers.get(userId);
  }

  // 获取所有在线用户
  public getAllOnlineUsers(): User[] {
    return Array.from(this.onlineUsers.values()).filter(user => user.isOnline);
  }

  // 设置用户当前房间
  public setUserRoom(userId: string, roomId: string | undefined): void {
    const user = this.onlineUsers.get(userId);
    if (user) {
      user.currentRoomId = roomId;
    }
  }

  // 获取用户数量统计
  public getStats() {
    return {
      totalUsers: this.users.size,
      onlineUsers: this.getAllOnlineUsers().length
    };
  }
}
