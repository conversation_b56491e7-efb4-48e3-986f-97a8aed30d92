import { Server } from 'colyseus';
import { createServer } from 'http';
import express from 'express';
import cors from 'cors';
import { GameRoom } from './rooms/GameRoom';
import { authRoutes } from './routes/auth';
import { roomRoutes } from './routes/rooms';
import { matchmakingRoutes } from './routes/matchmaking';
import { botRoutes } from './routes/bots';

const app = express();
const port = process.env.PORT || 2567;

// 中间件
app.use(cors());
app.use(express.json());

// 路由
app.use('/auth', authRoutes);
app.use('/rooms', roomRoutes);
app.use('/matchmaking', matchmakingRoutes);
app.use('/bots', botRoutes);

// 创建HTTP服务器
const server = createServer(app);

// 创建Colyseus服务器
const gameServer = new Server({
  server,
});

// 注册房间类型
gameServer.define('game_room', GameRoom);

// 启动服务器
gameServer.listen(port);

console.log(`🎮 游戏服务器启动在端口 ${port}`);
console.log(`📡 WebSocket 地址: ws://localhost:${port}`);
console.log(`🌐 HTTP API 地址: http://localhost:${port}`);
