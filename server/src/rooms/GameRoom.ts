import { Room, Client } from 'colyseus';
import { GameState, Player, GAME_CONFIG, MessageType } from '@multiplayer-game/shared';
import { GameRoomState } from '../schema/GameRoomState';
import { RoomService } from '../services/RoomService';
import { UserService } from '../services/UserService';
import { BotService, BotBehavior } from '../services/BotService';
import { verifyToken } from '../middleware/auth';

export class GameRoom extends Room<GameRoomState> {
  maxClients = GAME_CONFIG.MAX_PLAYERS_PER_ROOM;
  private roomService = RoomService.getInstance();
  private userService = UserService.getInstance();
  private botService = BotService.getInstance();
  private hostId: string = '';
  private botUpdateInterval: NodeJS.Timeout | null = null;
  
  onCreate(options: any) {
    console.log('🏠 游戏房间创建:', this.roomId);

    // 初始化房间状态
    this.setState(new GameRoomState());

    // 设置房间元数据
    const roomName = options.name || `房间 ${this.roomId.substring(0, 6)}`;
    this.setMetadata({
      name: roomName,
      gameState: GameState.WAITING,
      playerCount: 0,
      maxPlayers: this.maxClients,
      createdAt: new Date().toISOString()
    });

    // 注册房间到RoomService
    this.roomService.registerRoom(this.roomId, {
      name: roomName,
      hostId: '',
      hostUsername: '',
      playerCount: 0,
      maxPlayers: this.maxClients,
      gameState: GameState.WAITING,
      isPrivate: options.isPrivate || false,
      createdAt: new Date()
    });

    // 监听消息
    this.onMessage(MessageType.PLAYER_READY, (client, message) => {
      this.handlePlayerReady(client, message);
    });

    this.onMessage(MessageType.START_GAME, (client, message) => {
      this.handleStartGame(client);
    });

    this.onMessage(MessageType.PLAYER_MOVE, (client, message) => {
      this.handlePlayerMove(client, message);
    });

    this.onMessage('pause_game', (client, message) => {
      this.handlePauseGame(client);
    });

    this.onMessage('get_game_state', (client, message) => {
      this.handleGetGameState(client);
    });

    // 机器人相关消息
    this.onMessage('add_bots', (client, message) => {
      this.handleAddBots(client, message);
    });

    this.onMessage('remove_bots', (client, message) => {
      this.handleRemoveBots(client);
    });

    // 设置房间更新频率 (60 FPS)
    this.setSimulationInterval((deltaTime) => this.update(deltaTime), 1000 / 60);

    // 启动机器人更新循环
    this.startBotUpdateLoop();
  }

  onJoin(client: Client, options: any) {
    console.log(`👤 玩家加入房间: ${client.sessionId}`, options.user?.username);

    // 验证用户token
    let username = `玩家${client.sessionId.substring(0, 4)}`;
    if (options.token) {
      const tokenData = verifyToken(options.token);
      if (tokenData) {
        username = tokenData.username;
        // 设置用户当前房间
        this.userService.setUserRoom(tokenData.userId, this.roomId);
      }
    }

    // 设置房主（第一个加入的玩家）
    if (this.state.getPlayerCount() === 0) {
      this.hostId = client.sessionId;
      this.state.setHost(client.sessionId);
      this.roomService.updateRoom(this.roomId, {
        hostId: client.sessionId,
        hostUsername: username
      });
    }

    // 创建玩家对象
    const player: Player = {
      id: client.sessionId,
      username,
      isReady: false,
      position: {
        x: Math.random() * (GAME_CONFIG.GAME_WIDTH - 100) + 50,
        y: Math.random() * (GAME_CONFIG.GAME_HEIGHT - 100) + 50
      },
      score: 0
    };

    // 添加玩家到房间状态
    this.state.addPlayer(player);
    
    // 更新房间元数据
    this.setMetadata({
      ...this.metadata,
      playerCount: this.state.players.size
    });

    // 向客户端发送欢迎消息
    client.send('welcome', {
      message: `欢迎 ${player.username} 加入游戏！`,
      roomId: this.roomId,
      playerId: client.sessionId
    });

    // 向其他玩家广播新玩家加入
    this.broadcast('player_joined', {
      player: player,
      message: `${player.username} 加入了游戏`
    }, { except: client });
  }

  onLeave(client: Client, consented: boolean) {
    console.log(`👋 玩家离开房间: ${client.sessionId}`);
    
    const player = this.state.players.get(client.sessionId);
    if (player) {
      // 清除用户房间信息
      if (player.username) {
        const user = this.userService.getAllOnlineUsers().find(u => u.username === player.username);
        if (user) {
          this.userService.setUserRoom(user.id, undefined);
        }
      }

      // 向其他玩家广播玩家离开
      this.broadcast('player_left', {
        playerId: client.sessionId,
        message: `${player.username} 离开了游戏`
      });

      // 从房间状态中移除玩家
      this.state.removePlayer(client.sessionId);
    }

    // 更新房间元数据
    const playerCount = this.state.getPlayerCount();
    this.setMetadata({
      ...this.metadata,
      playerCount
    });

    // 更新RoomService中的房间信息
    this.roomService.updateRoom(this.roomId, {
      playerCount
    });

    // 如果房主离开，选择新房主
    if (client.sessionId === this.hostId && playerCount > 0) {
      const players = this.state.getAllPlayers();
      if (players.length > 0) {
        this.hostId = players[0].id;
        this.state.setHost(this.hostId);
        this.roomService.updateRoom(this.roomId, {
          hostId: this.hostId,
          hostUsername: players[0].username
        });

        this.broadcast('new_host', {
          hostId: this.hostId,
          hostUsername: players[0].username,
          message: `${players[0].username} 成为新房主`
        });
      }
    }

    // 如果房间为空，重置游戏状态
    if (playerCount === 0) {
      this.state.gameState = GameState.WAITING;
      this.setMetadata({
        ...this.metadata,
        gameState: GameState.WAITING
      });
      this.roomService.updateRoom(this.roomId, {
        gameState: GameState.WAITING
      });
    }
  }

  onDispose() {
    console.log('🗑️ 房间销毁:', this.roomId);
    // 停止机器人更新循环
    if (this.botUpdateInterval) {
      clearInterval(this.botUpdateInterval);
    }
    // 清理机器人
    this.botService.clearAllBots();
    // 从RoomService中移除房间
    this.roomService.removeRoom(this.roomId);
  }

  private handlePlayerReady(client: Client, message: any) {
    const player = this.state.getPlayer(client.sessionId);
    if (player) {
      player.isReady = !player.isReady;

      this.broadcast('player_ready_changed', {
        playerId: client.sessionId,
        isReady: player.isReady,
        username: player.username
      });

      // 检查是否所有玩家都准备好了
      this.checkAllPlayersReady();
    }
  }

  private handleStartGame(client: Client) {
    // 只有房主可以开始游戏
    if (this.state.isHost(client.sessionId)) {
      // 检查是否有足够的玩家
      if (this.state.getPlayerCount() >= GAME_CONFIG.MIN_PLAYERS_TO_START) {
        this.startGame();
      } else {
        client.send('error', {
          message: `至少需要 ${GAME_CONFIG.MIN_PLAYERS_TO_START} 名玩家才能开始游戏`
        });
      }
    } else {
      client.send('error', {
        message: '只有房主可以开始游戏'
      });
    }
  }

  private handlePlayerMove(client: Client, message: any) {
    const player = this.state.getPlayer(client.sessionId);
    if (player && this.state.gameState === GameState.PLAYING) {
      // 更新玩家位置
      if (message.x !== undefined) player.x = message.x;
      if (message.y !== undefined) player.y = message.y;

      // 广播玩家移动
      this.broadcast('player_moved', {
        playerId: client.sessionId,
        position: { x: player.x, y: player.y }
      }, { except: client });
    }
  }

  private checkAllPlayersReady() {
    if (this.state.getPlayerCount() >= GAME_CONFIG.MIN_PLAYERS_TO_START &&
        this.state.areAllPlayersReady()) {

      this.broadcast('all_players_ready', {
        message: '所有玩家已准备就绪！游戏即将开始...',
        countdown: 3
      });

      // 3秒后自动开始游戏
      setTimeout(() => {
        this.startGame();
      }, 3000);
    }
  }

  private startGame() {
    if (this.state.gameState !== GameState.WAITING) {
      return;
    }

    console.log('🎮 游戏开始:', this.roomId);
    
    this.state.gameState = GameState.PLAYING;
    this.setMetadata({
      ...this.metadata,
      gameState: GameState.PLAYING
    });

    // 更新RoomService中的游戏状态
    this.roomService.updateRoom(this.roomId, {
      gameState: GameState.PLAYING
    });

    // 重置所有玩家状态
    this.state.resetPlayersState();

    this.broadcast('game_started', {
      message: '游戏开始！',
      gameState: GameState.PLAYING,
      gameInfo: this.state.getGameSummary()
    });
  }

  private update(deltaTime: number) {
    // 游戏逻辑更新
    if (this.state.gameState === GameState.PLAYING) {
      // 更新游戏时间
      this.state.updateGameTime(deltaTime);

      // 这里可以添加游戏逻辑更新
      // 例如：物理模拟、碰撞检测、游戏规则等

      // 每秒广播一次游戏状态更新
      if (Math.floor(this.state.gameTime / 1000) % 1 === 0) {
        this.broadcast('game_update', {
          gameTime: this.state.gameTime,
          gameInfo: this.state.getGameSummary()
        });
      }
    }
  }

  private handlePauseGame(client: Client) {
    // 只有房主可以暂停游戏
    if (this.state.isHost(client.sessionId) && this.state.gameState === GameState.PLAYING) {
      this.state.togglePause();

      this.broadcast('game_paused', {
        isPaused: this.state.isPaused,
        message: this.state.isPaused ? '游戏已暂停' : '游戏已恢复',
        pausedBy: this.state.getPlayer(client.sessionId)?.username
      });
    }
  }

  private handleGetGameState(client: Client) {
    client.send('game_state_response', {
      gameInfo: this.state.getGameSummary(),
      players: this.state.getAllPlayers().map(p => ({
        id: p.id,
        username: p.username,
        isReady: p.isReady,
        score: p.score,
        position: { x: p.x, y: p.y }
      }))
    });
  }

  // 机器人相关方法
  private handleAddBots(client: Client, message: any) {
    // 只有房主可以添加机器人
    if (!this.state.isHost(client.sessionId)) {
      client.send('error', { message: '只有房主可以添加机器人' });
      return;
    }

    const count = message.count || 3; // 默认添加3个机器人
    const behavior = message.behavior || BotBehavior.ACTIVE;

    // 计算可以添加的机器人数量
    const currentPlayers = this.state.getPlayerCount();
    const maxBots = Math.min(count, this.maxClients - currentPlayers);

    if (maxBots <= 0) {
      client.send('error', { message: '房间已满，无法添加机器人' });
      return;
    }

    // 创建机器人
    const bots = this.botService.createMultipleBots(maxBots, behavior);

    // 将机器人添加到房间
    bots.forEach(bot => {
      const player: Player = {
        id: bot.id,
        username: bot.username,
        isReady: false,
        position: bot.position,
        score: bot.score
      };

      this.state.addPlayer(player);
    });

    // 更新房间信息
    this.updateRoomMetadata();

    // 广播机器人加入消息
    this.broadcast('bots_added', {
      message: `添加了 ${bots.length} 个机器人`,
      bots: bots.map(bot => ({ id: bot.id, username: bot.username }))
    });

    console.log(`🤖 房间 ${this.roomId} 添加了 ${bots.length} 个机器人`);
  }

  private handleRemoveBots(client: Client) {
    // 只有房主可以移除机器人
    if (!this.state.isHost(client.sessionId)) {
      client.send('error', { message: '只有房主可以移除机器人' });
      return;
    }

    const bots = this.botService.getAllBots();
    let removedCount = 0;

    // 移除所有机器人
    bots.forEach(bot => {
      this.state.removePlayer(bot.id);
      this.botService.removeBot(bot.id);
      removedCount++;
    });

    // 更新房间信息
    this.updateRoomMetadata();

    // 广播机器人移除消息
    this.broadcast('bots_removed', {
      message: `移除了 ${removedCount} 个机器人`
    });

    console.log(`🗑️ 房间 ${this.roomId} 移除了 ${removedCount} 个机器人`);
  }

  private startBotUpdateLoop() {
    this.botUpdateInterval = setInterval(() => {
      // 更新机器人行为
      this.botService.updateBotBehaviors(this.state.gameState);

      // 处理机器人移动
      const bots = this.botService.getAllBots();
      bots.forEach(bot => {
        const newPosition = this.botService.auteMoveBot(bot.id);
        if (newPosition && this.state.gameState === GameState.PLAYING) {
          const player = this.state.getPlayer(bot.id);
          if (player) {
            player.x = newPosition.x;
            player.y = newPosition.y;

            // 广播机器人移动
            this.broadcast('player_moved', {
              playerId: bot.id,
              position: newPosition
            });
          }
        }

        // 处理机器人准备状态
        if (this.state.gameState === GameState.WAITING && !bot.isReady) {
          const player = this.state.getPlayer(bot.id);
          if (player && Math.random() < 0.1) { // 10%概率每次更新时准备
            player.isReady = true;
            bot.isReady = true;

            this.broadcast('player_ready_changed', {
              playerId: bot.id,
              isReady: true,
              username: bot.username
            });

            // 检查是否所有玩家都准备好了
            this.checkAllPlayersReady();
          }
        }
      });
    }, 2000); // 每2秒更新一次机器人
  }

  private updateRoomMetadata() {
    const playerCount = this.state.getPlayerCount();
    this.setMetadata({
      ...this.metadata,
      playerCount
    });

    this.roomService.updateRoom(this.roomId, {
      playerCount
    });
  }
}
