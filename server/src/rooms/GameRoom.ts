import { Room, Client } from 'colyseus';
import { GameState, Player, GAME_CONFIG, MessageType } from '@multiplayer-game/shared';
import { GameRoomState } from '../schema/GameRoomState';

export class GameRoom extends Room<GameRoomState> {
  maxClients = GAME_CONFIG.MAX_PLAYERS_PER_ROOM;
  
  onCreate(options: any) {
    console.log('🏠 游戏房间创建:', this.roomId);
    
    // 初始化房间状态
    this.setState(new GameRoomState());
    
    // 设置房间元数据
    this.setMetadata({
      name: options.name || `房间 ${this.roomId.substring(0, 6)}`,
      gameState: GameState.WAITING,
      playerCount: 0,
      maxPlayers: this.maxClients,
      createdAt: new Date().toISOString()
    });

    // 监听消息
    this.onMessage(MessageType.PLAYER_READY, (client, message) => {
      this.handlePlayerReady(client, message);
    });

    this.onMessage(MessageType.START_GAME, (client, message) => {
      this.handleStartGame(client);
    });

    this.onMessage(MessageType.PLAYER_MOVE, (client, message) => {
      this.handlePlayerMove(client, message);
    });

    // 设置房间更新频率 (60 FPS)
    this.setSimulationInterval((deltaTime) => this.update(deltaTime), 1000 / 60);
  }

  onJoin(client: Client, options: any) {
    console.log(`👤 玩家加入房间: ${client.sessionId}`, options.user?.username);
    
    // 创建玩家对象
    const player: Player = {
      id: client.sessionId,
      username: options.user?.username || `玩家${client.sessionId.substring(0, 4)}`,
      isReady: false,
      position: {
        x: Math.random() * (GAME_CONFIG.GAME_WIDTH - 100) + 50,
        y: Math.random() * (GAME_CONFIG.GAME_HEIGHT - 100) + 50
      },
      score: 0
    };

    // 添加玩家到房间状态
    this.state.addPlayer(player);
    
    // 更新房间元数据
    this.setMetadata({
      ...this.metadata,
      playerCount: this.state.players.size
    });

    // 向客户端发送欢迎消息
    client.send('welcome', {
      message: `欢迎 ${player.username} 加入游戏！`,
      roomId: this.roomId,
      playerId: client.sessionId
    });

    // 向其他玩家广播新玩家加入
    this.broadcast('player_joined', {
      player: player,
      message: `${player.username} 加入了游戏`
    }, { except: client });
  }

  onLeave(client: Client, consented: boolean) {
    console.log(`👋 玩家离开房间: ${client.sessionId}`);
    
    const player = this.state.players.get(client.sessionId);
    if (player) {
      // 向其他玩家广播玩家离开
      this.broadcast('player_left', {
        playerId: client.sessionId,
        message: `${player.username} 离开了游戏`
      });

      // 从房间状态中移除玩家
      this.state.removePlayer(client.sessionId);
    }

    // 更新房间元数据
    this.setMetadata({
      ...this.metadata,
      playerCount: this.state.getPlayerCount()
    });

    // 如果房间为空，重置游戏状态
    if (this.state.getPlayerCount() === 0) {
      this.state.gameState = GameState.WAITING;
      this.setMetadata({
        ...this.metadata,
        gameState: GameState.WAITING
      });
    }
  }

  onDispose() {
    console.log('🗑️ 房间销毁:', this.roomId);
  }

  private handlePlayerReady(client: Client, message: any) {
    const player = this.state.getPlayer(client.sessionId);
    if (player) {
      player.isReady = !player.isReady;

      this.broadcast('player_ready_changed', {
        playerId: client.sessionId,
        isReady: player.isReady,
        username: player.username
      });

      // 检查是否所有玩家都准备好了
      this.checkAllPlayersReady();
    }
  }

  private handleStartGame(client: Client) {
    // 只有房主可以开始游戏（第一个加入的玩家）
    const players = this.state.getAllPlayers();
    if (players.length > 0 && players[0].id === client.sessionId) {
      this.startGame();
    }
  }

  private handlePlayerMove(client: Client, message: any) {
    const player = this.state.getPlayer(client.sessionId);
    if (player && this.state.gameState === GameState.PLAYING) {
      // 更新玩家位置
      if (message.x !== undefined) player.x = message.x;
      if (message.y !== undefined) player.y = message.y;

      // 广播玩家移动
      this.broadcast('player_moved', {
        playerId: client.sessionId,
        position: { x: player.x, y: player.y }
      }, { except: client });
    }
  }

  private checkAllPlayersReady() {
    const players = this.state.getAllPlayers();
    const readyPlayers = players.filter(p => p.isReady);

    if (players.length >= GAME_CONFIG.MIN_PLAYERS_TO_START &&
        readyPlayers.length === players.length) {

      this.broadcast('all_players_ready', {
        message: '所有玩家已准备就绪！游戏即将开始...'
      });

      // 3秒后自动开始游戏
      setTimeout(() => {
        this.startGame();
      }, 3000);
    }
  }

  private startGame() {
    if (this.state.gameState !== GameState.WAITING) {
      return;
    }

    console.log('🎮 游戏开始:', this.roomId);
    
    this.state.gameState = GameState.PLAYING;
    this.setMetadata({
      ...this.metadata,
      gameState: GameState.PLAYING
    });

    // 重置所有玩家状态
    this.state.getAllPlayers().forEach(player => {
      player.isReady = false;
      player.score = 0;
    });

    this.broadcast('game_started', {
      message: '游戏开始！',
      gameState: GameState.PLAYING
    });
  }

  private update(deltaTime: number) {
    // 游戏逻辑更新
    if (this.state.gameState === GameState.PLAYING) {
      // 这里可以添加游戏逻辑更新
      // 例如：物理模拟、碰撞检测、游戏规则等
    }
  }
}
