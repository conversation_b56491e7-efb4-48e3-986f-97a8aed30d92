import { Room, Client } from 'colyseus';
import { WorldMapManager } from '@multiplayer-game/shared';

/**
 * 世界房间
 * 管理持久化的世界地图和多人在线
 */

interface WorldState {
  worldMap: any;
  players: Map<string, any>;
  currentTurn: number;
  currentPlayer: string | null;
}

export class WorldRoom extends Room<WorldState> {
  private worldManager: WorldMapManager;
  private turnTimer?: NodeJS.Timeout;

  onCreate(options: any) {
    console.log('🌍 创建世界房间');
    
    // 初始化世界管理器
    this.worldManager = new WorldMapManager();
    
    // 设置房间属性
    this.maxClients = 8;
    this.autoDispose = false; // 世界房间不自动销毁
    
    // 初始化状态
    this.setState({
      worldMap: null,
      players: new Map(),
      currentTurn: 1,
      currentPlayer: null
    });

    // 设置消息处理
    this.setupMessageHandlers();
    
    // 开始回合计时器
    this.startTurnTimer();
  }

  /**
   * 设置消息处理器
   */
  private setupMessageHandlers(): void {
    // 添加行动到计划
    this.onMessage('add_action', (client, message) => {
      const { action } = message;
      action.playerId = client.sessionId; // 确保行动属于发送者
      action.id = `action_${Date.now()}_${Math.random()}`;

      const result = this.worldManager.addPlayerAction(client.sessionId, action);

      if (result.success) {
        client.send('action_added', {
          actionId: action.id,
          message: result.message
        });
      } else {
        client.send('action_error', { message: result.message });
      }

      // 广播更新的世界状态
      this.broadcastWorldState();
    });

    // 玩家确认回合
    this.onMessage('player_ready', (client) => {
      const success = this.worldManager.playerReady(client.sessionId);

      if (success) {
        this.broadcast('player_confirmed', {
          playerId: client.sessionId
        });
      }

      this.broadcastWorldState();
    });

    // 移除行动
    this.onMessage('remove_action', (client, message) => {
      const { actionId } = message;
      const success = this.worldManager.removePlayerAction(client.sessionId, actionId);

      if (success) {
        client.send('action_removed', { actionId });
      } else {
        client.send('action_error', { message: '无法移除行动' });
      }

      this.broadcastWorldState();
    });

    // 清除所有行动
    this.onMessage('clear_actions', (client) => {
      this.worldManager.clearPlayerActions(client.sessionId);
      client.send('actions_cleared');
      this.broadcastWorldState();
    });

    // 请求地图数据
    this.onMessage('request_map', (client, message) => {
      const { centerX, centerY, radius } = message;
      const visibleMap = this.worldManager.getVisibleMap(client.sessionId, centerX, centerY, radius);
      
      client.send('map_data', { tiles: visibleMap });
    });

    // 聊天消息
    this.onMessage('chat', (client, message) => {
      this.broadcast('chat_message', {
        playerId: client.sessionId,
        playerName: this.getPlayerName(client.sessionId),
        message: message.text,
        timestamp: Date.now()
      });
    });
  }

  /**
   * 处理战斗
   */
  private handleBattle(client: Client, battleData: any): void {
    console.log('⚔️ 触发战斗:', battleData);
    
    // 通知相关玩家进入战斗
    const attackerClient = client;
    const defenderClient = this.clients.find(c => c.sessionId === battleData.defender.playerId);
    
    if (defenderClient) {
      // 通知双方进入战斗场景
      attackerClient.send('battle_start', {
        role: 'attacker',
        battleId: `battle_${Date.now()}`,
        enemy: battleData.defender,
        battleTile: battleData.battleTile
      });
      
      defenderClient.send('battle_start', {
        role: 'defender', 
        battleId: `battle_${Date.now()}`,
        enemy: battleData.attacker,
        battleTile: battleData.battleTile
      });
    } else {
      // 如果防守方不在线，自动战斗
      this.handleAutoBattle(battleData);
    }
  }

  /**
   * 处理自动战斗（对方不在线时）
   */
  private handleAutoBattle(battleData: any): void {
    // 简单的自动战斗逻辑
    const attackerWins = Math.random() > 0.5;
    
    setTimeout(() => {
      this.broadcast('battle_result', {
        battleId: `battle_${Date.now()}`,
        winner: attackerWins ? battleData.attacker.playerId : battleData.defender.playerId,
        loser: attackerWins ? battleData.defender.playerId : battleData.attacker.playerId,
        isAutoBattle: true
      });
      
      this.broadcastWorldState();
    }, 3000);
  }

  /**
   * 开始回合计时器
   */
  private startTurnTimer(): void {
    if (this.turnTimer) {
      clearTimeout(this.turnTimer);
    }

    this.turnTimer = setTimeout(() => {
      // 计划时间到，强制执行所有行动
      console.log('⏰ 计划时间到，强制执行所有行动');
      this.worldManager.forceTurnEnd();

      this.broadcast('turn_timeout', {
        message: '计划时间到，执行所有行动'
      });

      this.broadcastWorldState();
      this.startTurnTimer(); // 开始下一个回合计时
    }, 60000); // 60秒计划时间
  }

  /**
   * 广播世界状态
   */
  private broadcastWorldState(): void {
    const worldState = this.worldManager.getWorldState();
    this.broadcast('world_update', worldState);
  }

  /**
   * 玩家加入
   */
  onJoin(client: Client, options: any) {
    console.log(`👤 玩家 ${options.username} 加入世界`);
    
    // 添加玩家到世界
    const success = this.worldManager.addPlayer(client.sessionId, options.username);
    
    if (success) {
      // 发送欢迎消息
      client.send('welcome', {
        message: `欢迎来到三国世界！`,
        playerId: client.sessionId
      });

      // 发送初始世界状态
      const worldState = this.worldManager.getWorldState();
      client.send('world_state', worldState);
      
      // 通知其他玩家
      this.broadcast('player_joined', {
        playerId: client.sessionId,
        username: options.username
      }, { except: client });

      // 广播更新的世界状态
      this.broadcastWorldState();
    } else {
      client.send('join_error', { message: '加入世界失败' });
    }
  }

  /**
   * 玩家离开
   */
  onLeave(client: Client, consented: boolean) {
    console.log(`👋 玩家 ${client.sessionId} 离开世界`);
    
    // 标记玩家为离线（但不删除，保持世界持久性）
    const worldState = this.worldManager.getWorldState();
    const player = worldState.players.find(p => p.id === client.sessionId);
    if (player) {
      player.isOnline = false;
      player.lastActionTime = Date.now();
    }

    // 通知其他玩家
    this.broadcast('player_left', {
      playerId: client.sessionId
    });

    this.broadcastWorldState();
  }

  /**
   * 房间销毁
   */
  onDispose() {
    console.log('🌍 世界房间销毁');
    
    if (this.turnTimer) {
      clearTimeout(this.turnTimer);
    }
  }

  /**
   * 获取玩家名称
   */
  private getPlayerName(playerId: string): string {
    const worldState = this.worldManager.getWorldState();
    const player = worldState.players.find(p => p.id === playerId);
    return player?.username || '未知玩家';
  }

  /**
   * 获取房间统计信息
   */
  public getRoomStats(): any {
    const worldState = this.worldManager.getWorldState();
    
    return {
      totalPlayers: worldState.players.length,
      onlinePlayers: worldState.players.filter(p => p.isOnline).length,
      currentTurn: worldState.currentTurn,
      currentPlayer: worldState.currentPlayer,
      uptime: Date.now() - this.createdAt
    };
  }
}
