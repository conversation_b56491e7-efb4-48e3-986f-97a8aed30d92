import express from 'express';
import cors from 'cors';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

const app = express();
const PORT = 2567;
const JWT_SECRET = 'your-secret-key';

// 中间件
app.use(cors());
app.use(express.json());

// 简单的内存存储
const users = new Map<string, { id: string; username: string; password: string }>();
const onlineUsers = new Map<string, any>();

// 生成ID
const generateId = () => Math.random().toString(36).substring(2) + Date.now().toString(36);

// 注册接口
app.post('/auth/register', async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.json({
        success: false,
        message: '用户名和密码不能为空'
      });
    }

    if (users.has(username)) {
      return res.json({
        success: false,
        message: '用户名已存在'
      });
    }

    const hashedPassword = await bcrypt.hash(password, 10);
    const userId = generateId();
    
    users.set(username, {
      id: userId,
      username,
      password: hashedPassword
    });

    const token = jwt.sign({ userId, username }, JWT_SECRET, { expiresIn: '24h' });

    const user = {
      id: userId,
      username,
      isOnline: true
    };
    onlineUsers.set(userId, user);

    res.json({
      success: true,
      user,
      token
    });

  } catch (error) {
    console.error('注册错误:', error);
    res.json({
      success: false,
      message: '注册失败'
    });
  }
});

// 登录接口
app.post('/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.json({
        success: false,
        message: '用户名和密码不能为空'
      });
    }

    const user = users.get(username);
    if (!user) {
      return res.json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    const token = jwt.sign({ userId: user.id, username }, JWT_SECRET, { expiresIn: '24h' });

    const userInfo = {
      id: user.id,
      username,
      isOnline: true
    };
    onlineUsers.set(user.id, userInfo);

    res.json({
      success: true,
      user: userInfo,
      token
    });

  } catch (error) {
    console.error('登录错误:', error);
    res.json({
      success: false,
      message: '登录失败'
    });
  }
});

// 房间列表接口
app.get('/rooms/list', (req, res) => {
  res.json({
    success: true,
    data: [] // 暂时返回空列表
  });
});

// 房间统计接口
app.get('/rooms/stats/overview', (req, res) => {
  res.json({
    success: true,
    data: {
      totalRooms: 0,
      activeRooms: 0,
      waitingRooms: 0,
      totalPlayers: 0,
      totalUsers: users.size,
      onlineUsers: onlineUsers.size
    }
  });
});

// 可用房间接口
app.get('/rooms/available', (req, res) => {
  res.json({
    success: true,
    data: []
  });
});

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: '服务器运行正常',
    timestamp: new Date().toISOString()
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log('🎮 简化游戏服务器启动成功！');
  console.log(`📡 HTTP API 地址: http://localhost:${PORT}`);
  console.log('🌟 支持的功能:');
  console.log('  - 用户注册/登录');
  console.log('  - 房间列表查询');
  console.log('  - 统计信息');
  console.log('');
  console.log('📋 可用接口:');
  console.log('  POST /auth/register - 用户注册');
  console.log('  POST /auth/login - 用户登录');
  console.log('  GET /rooms/list - 房间列表');
  console.log('  GET /rooms/stats/overview - 统计信息');
  console.log('  GET /health - 健康检查');
});
