import { Schema, MapSchema, type } from '@colyseus/schema';
import { GameState, Player } from '@multiplayer-game/shared';

export class PlayerSchema extends Schema implements Player {
  @type('string') id: string = '';
  @type('string') username: string = '';
  @type('boolean') isReady: boolean = false;
  @type('number') score: number = 0;
  
  // 位置信息
  @type('number') x: number = 0;
  @type('number') y: number = 0;

  // 实现 Player 接口的 position 属性
  get position() {
    return { x: this.x, y: this.y };
  }

  set position(pos: { x: number; y: number }) {
    this.x = pos.x;
    this.y = pos.y;
  }
}

export class GameRoomState extends Schema {
  @type({ map: PlayerSchema }) players = new MapSchema<PlayerSchema>();
  @type('string') gameState: GameState = GameState.WAITING;
  @type('number') gameTime: number = 0;
  @type('number') maxPlayers: number = 8;
  @type('string') hostId: string = '';
  @type('number') roundNumber: number = 0;
  @type('boolean') isPaused: boolean = false;

  // 添加玩家
  addPlayer(player: Player): void {
    const playerSchema = new PlayerSchema();
    playerSchema.id = player.id;
    playerSchema.username = player.username;
    playerSchema.isReady = player.isReady;
    playerSchema.score = player.score || 0;
    
    if (player.position) {
      playerSchema.x = player.position.x;
      playerSchema.y = player.position.y;
    }

    this.players.set(player.id, playerSchema);
  }

  // 移除玩家
  removePlayer(playerId: string): void {
    this.players.delete(playerId);
  }

  // 获取玩家
  getPlayer(playerId: string): PlayerSchema | undefined {
    return this.players.get(playerId);
  }

  // 获取所有玩家
  getAllPlayers(): PlayerSchema[] {
    return Array.from(this.players.values());
  }

  // 获取玩家数量
  getPlayerCount(): number {
    return this.players.size;
  }

  // 设置房主
  setHost(hostId: string): void {
    this.hostId = hostId;
  }

  // 检查是否为房主
  isHost(playerId: string): boolean {
    return this.hostId === playerId;
  }

  // 获取准备好的玩家数量
  getReadyPlayerCount(): number {
    return this.getAllPlayers().filter(player => player.isReady).length;
  }

  // 检查所有玩家是否都准备好
  areAllPlayersReady(): boolean {
    const players = this.getAllPlayers();
    return players.length > 0 && players.every(player => player.isReady);
  }

  // 重置所有玩家状态
  resetPlayersState(): void {
    this.getAllPlayers().forEach(player => {
      player.isReady = false;
      player.score = 0;
    });
  }

  // 更新游戏时间
  updateGameTime(deltaTime: number): void {
    if (this.gameState === GameState.PLAYING && !this.isPaused) {
      this.gameTime += deltaTime;
    }
  }

  // 暂停/恢复游戏
  togglePause(): void {
    if (this.gameState === GameState.PLAYING) {
      this.isPaused = !this.isPaused;
    }
  }

  // 开始新回合
  startNewRound(): void {
    this.roundNumber++;
    this.gameTime = 0;
    this.resetPlayersState();
  }

  // 获取游戏状态摘要
  getGameSummary() {
    return {
      gameState: this.gameState,
      gameTime: this.gameTime,
      roundNumber: this.roundNumber,
      playerCount: this.getPlayerCount(),
      readyPlayerCount: this.getReadyPlayerCount(),
      hostId: this.hostId,
      isPaused: this.isPaused
    };
  }
}
