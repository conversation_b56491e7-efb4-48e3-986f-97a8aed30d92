import { Schema, MapSchema, type } from '@colyseus/schema';
import { GameState, Player } from '@multiplayer-game/shared';

export class PlayerSchema extends Schema implements Player {
  @type('string') id: string = '';
  @type('string') username: string = '';
  @type('boolean') isReady: boolean = false;
  @type('number') score: number = 0;
  
  // 位置信息
  @type('number') x: number = 0;
  @type('number') y: number = 0;

  // 实现 Player 接口的 position 属性
  get position() {
    return { x: this.x, y: this.y };
  }

  set position(pos: { x: number; y: number }) {
    this.x = pos.x;
    this.y = pos.y;
  }
}

export class GameRoomState extends Schema {
  @type({ map: PlayerSchema }) players = new MapSchema<PlayerSchema>();
  @type('string') gameState: GameState = GameState.WAITING;
  @type('number') gameTime: number = 0;
  @type('number') maxPlayers: number = 8;

  // 添加玩家
  addPlayer(player: Player): void {
    const playerSchema = new PlayerSchema();
    playerSchema.id = player.id;
    playerSchema.username = player.username;
    playerSchema.isReady = player.isReady;
    playerSchema.score = player.score || 0;
    
    if (player.position) {
      playerSchema.x = player.position.x;
      playerSchema.y = player.position.y;
    }

    this.players.set(player.id, playerSchema);
  }

  // 移除玩家
  removePlayer(playerId: string): void {
    this.players.delete(playerId);
  }

  // 获取玩家
  getPlayer(playerId: string): PlayerSchema | undefined {
    return this.players.get(playerId);
  }

  // 获取所有玩家
  getAllPlayers(): PlayerSchema[] {
    return Array.from(this.players.values());
  }

  // 获取玩家数量
  getPlayerCount(): number {
    return this.players.size;
  }
}
