import { Router } from 'express';
import { CreateRoomRequest, JoinRoomRequest, GAME_CONFIG } from '@multiplayer-game/shared';
import { authenticateToken, AuthRequest } from '../middleware/auth';
import { RoomService } from '../services/RoomService';
import { UserService } from '../services/UserService';

const router = Router();
const roomService = RoomService.getInstance();
const userService = UserService.getInstance();

// 获取房间列表
router.get('/list', (req, res) => {
  try {
    const rooms = roomService.getPublicRooms();
    res.json({
      success: true,
      data: rooms
    });
  } catch (error) {
    console.error('获取房间列表错误:', error);
    res.json({
      success: false,
      message: '获取房间列表失败'
    });
  }
});

// 获取可用房间（用于匹配）
router.get('/available', (req, res) => {
  try {
    const rooms = roomService.getAvailableRooms();
    res.json({
      success: true,
      data: rooms
    });
  } catch (error) {
    console.error('获取可用房间错误:', error);
    res.json({
      success: false,
      message: '获取可用房间失败'
    });
  }
});

// 获取房间详情
router.get('/:roomId', (req, res) => {
  try {
    const { roomId } = req.params;
    const room = roomService.getRoom(roomId);
    
    if (!room) {
      return res.json({
        success: false,
        message: '房间不存在'
      });
    }

    res.json({
      success: true,
      data: room
    });
  } catch (error) {
    console.error('获取房间详情错误:', error);
    res.json({
      success: false,
      message: '获取房间详情失败'
    });
  }
});

// 获取房间统计
router.get('/stats/overview', (req, res) => {
  try {
    const roomStats = roomService.getStats();
    const userStats = userService.getStats();
    
    res.json({
      success: true,
      data: {
        ...roomStats,
        ...userStats
      }
    });
  } catch (error) {
    console.error('获取统计信息错误:', error);
    res.json({
      success: false,
      message: '获取统计信息失败'
    });
  }
});

// 查找匹配房间
router.get('/match/find', authenticateToken, (req: AuthRequest, res) => {
  try {
    const room = roomService.findMatchRoom();
    
    if (room) {
      res.json({
        success: true,
        data: room,
        message: '找到匹配房间'
      });
    } else {
      res.json({
        success: false,
        message: '暂无可用房间，建议创建新房间'
      });
    }
  } catch (error) {
    console.error('查找匹配房间错误:', error);
    res.json({
      success: false,
      message: '查找匹配房间失败'
    });
  }
});

export { router as roomRoutes };
