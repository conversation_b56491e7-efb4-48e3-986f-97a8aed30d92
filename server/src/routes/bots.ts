import { Router } from 'express';

const router = Router();

// 简单的内存存储机器人数据
const bots = new Map<string, any>();

// 机器人名称
const botNames = [
  '智能助手Alpha', '机器人Beta', '自动玩家Gamma', '虚拟战士Delta',
  '数字精灵Epsilon', '代码骑士Zeta', '算法大师Eta', '程序守护Theta'
];

// 生成ID
const generateId = () => Math.random().toString(36).substring(2) + Date.now().toString(36);

// 创建机器人
const createBot = (roomId: string) => {
  const botId = generateId();
  const availableNames = botNames.filter(name => 
    !Array.from(bots.values()).some(bot => bot.username === name)
  );
  
  const botName = availableNames.length > 0 
    ? availableNames[Math.floor(Math.random() * availableNames.length)]
    : `机器人${Math.floor(Math.random() * 1000)}`;

  const bot = {
    id: botId,
    username: botName,
    isReady: false,
    position: {
      x: Math.random() * 800 + 100,
      y: Math.random() * 600 + 100
    },
    score: 0,
    roomId: roomId,
    behavior: 'active'
  };

  bots.set(botId, bot);
  return bot;
};

// 添加机器人
router.post('/add', (req, res) => {
  try {
    const { roomId, count = 3 } = req.body;
    const createdBots = [];
    
    for (let i = 0; i < count; i++) {
      const bot = createBot(roomId || 'default');
      createdBots.push(bot);
    }
    
    res.json({
      success: true,
      message: `成功添加 ${createdBots.length} 个机器人`,
      bots: createdBots
    });
    
    console.log(`🤖 添加了 ${createdBots.length} 个机器人到房间 ${roomId}`);
  } catch (error) {
    console.error('添加机器人错误:', error);
    res.status(500).json({
      success: false,
      message: '添加机器人失败'
    });
  }
});

// 移除机器人
router.post('/remove', (req, res) => {
  try {
    const { roomId } = req.body;
    let removedCount = 0;
    
    // 移除指定房间的所有机器人
    for (const [botId, bot] of bots.entries()) {
      if (!roomId || bot.roomId === roomId) {
        bots.delete(botId);
        removedCount++;
      }
    }
    
    res.json({
      success: true,
      message: `成功移除 ${removedCount} 个机器人`
    });
    
    console.log(`🗑️ 移除了 ${removedCount} 个机器人`);
  } catch (error) {
    console.error('移除机器人错误:', error);
    res.status(500).json({
      success: false,
      message: '移除机器人失败'
    });
  }
});

// 获取机器人列表
router.get('/list', (req, res) => {
  try {
    const { roomId } = req.query;
    let botList = Array.from(bots.values());
    
    if (roomId) {
      botList = botList.filter(bot => bot.roomId === roomId);
    }
    
    res.json({
      success: true,
      data: botList
    });
  } catch (error) {
    console.error('获取机器人列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取机器人列表失败'
    });
  }
});

export { router as botRoutes };
