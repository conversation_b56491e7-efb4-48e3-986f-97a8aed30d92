import { Router } from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { AuthResponse, LoginRequest, RegisterRequest, validateUsername, validatePassword, generateId } from '@multiplayer-game/shared';
import { UserService } from '../services/UserService';
import { authenticateToken, AuthRequest } from '../middleware/auth';

const router = Router();
const userService = UserService.getInstance();
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// 注册
router.post('/register', async (req, res) => {
  try {
    const { username, password }: RegisterRequest = req.body;

    // 验证输入
    if (!validateUsername(username)) {
      return res.json({
        success: false,
        message: '用户名必须是3-20个字符，只能包含字母、数字和下划线'
      } as AuthResponse);
    }

    if (!validatePassword(password)) {
      return res.json({
        success: false,
        message: '密码至少需要6个字符'
      } as AuthResponse);
    }

    // 检查用户是否已存在
    if (userService.userExists(username)) {
      return res.json({
        success: false,
        message: '用户名已存在'
      } as AuthResponse);
    }

    // 创建用户
    const hashedPassword = await bcrypt.hash(password, 10);
    const userId = generateId();

    userService.createUser(userId, username, hashedPassword);

    // 生成JWT token
    const token = jwt.sign({ userId, username }, JWT_SECRET, { expiresIn: '24h' });

    // 设置用户在线状态
    const user = {
      id: userId,
      username,
      isOnline: true
    };
    userService.setUserOnline(user);

    res.json({
      success: true,
      user,
      token
    } as AuthResponse);

  } catch (error) {
    console.error('注册错误:', error);
    res.json({
      success: false,
      message: '服务器错误'
    } as AuthResponse);
  }
});

// 登录
router.post('/login', async (req, res) => {
  try {
    const { username, password }: LoginRequest = req.body;

    // 查找用户
    const user = userService.findUserByUsername(username);
    if (!user) {
      return res.json({
        success: false,
        message: '用户名或密码错误'
      } as AuthResponse);
    }

    // 验证密码
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.json({
        success: false,
        message: '用户名或密码错误'
      } as AuthResponse);
    }

    // 生成JWT token
    const token = jwt.sign({ userId: user.id, username }, JWT_SECRET, { expiresIn: '24h' });

    // 设置用户在线状态
    const userInfo = {
      id: user.id,
      username,
      isOnline: true
    };
    userService.setUserOnline(userInfo);

    res.json({
      success: true,
      user: userInfo,
      token
    } as AuthResponse);

  } catch (error) {
    console.error('登录错误:', error);
    res.json({
      success: false,
      message: '服务器错误'
    } as AuthResponse);
  }
});

// 验证token有效性
router.get('/verify', authenticateToken, (req: AuthRequest, res) => {
  const user = userService.getOnlineUser(req.user!.userId);
  if (user) {
    res.json({
      success: true,
      user
    } as AuthResponse);
  } else {
    res.json({
      success: false,
      message: '用户未找到'
    } as AuthResponse);
  }
});

// 登出
router.post('/logout', authenticateToken, (req: AuthRequest, res) => {
  userService.setUserOffline(req.user!.userId);
  res.json({
    success: true,
    message: '登出成功'
  });
});

// 获取在线用户统计
router.get('/stats', (req, res) => {
  const stats = userService.getStats();
  res.json({
    success: true,
    data: stats
  });
});

export { router as authRoutes };
