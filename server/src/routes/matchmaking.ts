import { Router } from 'express';
import { authenticateToken, AuthRequest } from '../middleware/auth';
import { MatchmakingService } from '../services/MatchmakingService';
import { UserService } from '../services/UserService';

const router = Router();
const matchmakingService = MatchmakingService.getInstance();
const userService = UserService.getInstance();

// 开始匹配
router.post('/start', authenticateToken, (req: AuthRequest, res) => {
  try {
    const { userId, username } = req.user!;
    const { preferences } = req.body;

    // 检查用户是否已在匹配队列中
    if (matchmakingService.isInQueue(userId)) {
      return res.json({
        success: false,
        message: '您已在匹配队列中'
      });
    }

    // 检查用户是否已在房间中
    const user = userService.getOnlineUser(userId);
    if (user && user.currentRoomId) {
      return res.json({
        success: false,
        message: '您已在房间中，请先离开当前房间'
      });
    }

    // 添加到匹配队列
    matchmakingService.addToQueue({
      userId,
      username,
      timestamp: Date.now(),
      preferences
    });

    res.json({
      success: true,
      message: '开始匹配，请等待...',
      data: {
        queueStatus: matchmakingService.getQueueStatus()
      }
    });

  } catch (error) {
    console.error('开始匹配错误:', error);
    res.json({
      success: false,
      message: '开始匹配失败'
    });
  }
});

// 取消匹配
router.post('/cancel', authenticateToken, (req: AuthRequest, res) => {
  try {
    const { userId, username } = req.user!;

    const removed = matchmakingService.removeFromQueue(userId);
    
    if (removed) {
      res.json({
        success: true,
        message: '已取消匹配'
      });
    } else {
      res.json({
        success: false,
        message: '您不在匹配队列中'
      });
    }

  } catch (error) {
    console.error('取消匹配错误:', error);
    res.json({
      success: false,
      message: '取消匹配失败'
    });
  }
});

// 获取匹配状态
router.get('/status', authenticateToken, (req: AuthRequest, res) => {
  try {
    const { userId } = req.user!;
    const isInQueue = matchmakingService.isInQueue(userId);
    const queueStatus = matchmakingService.getQueueStatus();

    res.json({
      success: true,
      data: {
        isInQueue,
        queueStatus
      }
    });

  } catch (error) {
    console.error('获取匹配状态错误:', error);
    res.json({
      success: false,
      message: '获取匹配状态失败'
    });
  }
});

// 获取匹配统计
router.get('/stats', (req, res) => {
  try {
    const stats = matchmakingService.getStats();
    
    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('获取匹配统计错误:', error);
    res.json({
      success: false,
      message: '获取匹配统计失败'
    });
  }
});

export { router as matchmakingRoutes };
