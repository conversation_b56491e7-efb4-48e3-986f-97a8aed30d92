

## 宏观游戏流程（从登录到结算）
```mermaid

flowchart TD
    A[玩家登录/注册] --> B[进入匹配房间]
    B --> C[选择偏好剧本、身份、卡组]
    C --> D[系统分配身份与初始地块]
    D --> E[进入SLG回合制游戏（8人）]
    E --> F[游戏进行中]
    F --> G[是否达成胜利条件]
    G -->|是| H[游戏结束，结算积分]
    G -->|否| F
    H --> I[更新游戏外等级与成就]
    I --> J[返回大厅或继续游戏]
```


## 局内单个回合流程（We-Go 回合制）

```mermaid
flowchart TD
    A[回合开始（第K回合，60秒）] --> 系统更新ui，玩家可以查看战报 --> B[自动资源收集]
    B --> C[玩家在该回合提交行动指令]
    C --> D{是否触发战斗？}
    D -->|无主地块| E[自动占领，消耗行动力]
    D -->|PVE/PVP冲突| F[进入部署阶段]
    F --> G[部署阶段：选择卡牌+布阵（限时）]
    G --> H[回合结束，系统统一结算战斗]
    H --> I[返回战报与地块变更]
    I --> 进入第K+1回合 --> A

```

## 战斗流程（部署 + 自动战斗 + 结算）

```mermaid
flowchart TD
    A[战斗触发] --> B[生成战斗棋盘（基于地形）]
    B --> C[双方进入部署阶段]
    C --> D[选择出战卡牌（英雄/法术/战略）]
    D --> E[布阵：放置棋子+激活法术/战略卡]
    E --> F[部署结束，进入自动战斗]
    F --> G[系统模拟战斗（不可干预）]
    G --> H{胜负判定}
    H -->|胜利方| I[占领地块]
    H -->|失败方| J[失去地块]
    I --> K[战斗结算：奖励/经验/身份触发]
    J --> K
```

## 身份系统触发流程（以主公死亡为例）
```mermaid
flowchart TD
    A[主公地块被攻破] --> B[触发“崩殂”技能]
    B --> C{击杀者身份}
    C -->|反贼| D[反贼获得10金币+攻速+20%]
    C -->|忠臣| E[忠臣棋子获得负面状态]
    C -->|内奸| F[内奸获得所有法术卡+刷新商店]
    F --> G[内奸身份变主公]
```