<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游戏测试页面</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #27ae60; }
        .error { background: #e74c3c; }
        .info { background: #3498db; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            background: #3498db;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #2980b9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 多人在线游戏框架测试</h1>
        
        <div id="status"></div>
        
        <h2>服务器连接测试</h2>
        <button onclick="testServer()">测试服务器连接</button>
        <button onclick="testAuth()">测试用户认证</button>
        <button onclick="testRooms()">测试房间列表</button>
        
        <h2>游戏启动</h2>
        <button onclick="startGame()">启动游戏</button>
        
        <div id="game-container" style="margin-top: 20px;"></div>
    </div>

    <script>
        function addStatus(message, type = 'info') {
            const status = document.getElementById('status');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = new Date().toLocaleTimeString() + ' - ' + message;
            status.appendChild(div);
            status.scrollTop = status.scrollHeight;
        }

        async function testServer() {
            try {
                addStatus('测试服务器连接...', 'info');
                const response = await fetch('http://localhost:2567/rooms/stats/overview');
                const data = await response.json();
                addStatus('服务器连接成功: ' + JSON.stringify(data), 'success');
            } catch (error) {
                addStatus('服务器连接失败: ' + error.message, 'error');
            }
        }

        async function testAuth() {
            try {
                addStatus('测试用户认证...', 'info');
                const response = await fetch('http://localhost:2567/auth/register', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'test' + Date.now(), password: '123456' })
                });
                const data = await response.json();
                addStatus('用户认证测试: ' + (data.success ? '成功' : '失败'), data.success ? 'success' : 'error');
            } catch (error) {
                addStatus('用户认证测试失败: ' + error.message, 'error');
            }
        }

        async function testRooms() {
            try {
                addStatus('测试房间列表...', 'info');
                const response = await fetch('http://localhost:2567/rooms/list');
                const data = await response.json();
                addStatus('房间列表获取成功: ' + data.data.length + ' 个房间', 'success');
            } catch (error) {
                addStatus('房间列表获取失败: ' + error.message, 'error');
            }
        }

        async function startGame() {
            try {
                addStatus('启动游戏...', 'info');
                
                // 清空游戏容器
                const container = document.getElementById('game-container');
                container.innerHTML = '<div style="text-align: center; padding: 20px;">正在加载游戏...</div>';
                
                // 动态加载游戏脚本
                const script = document.createElement('script');
                script.type = 'module';
                script.src = '/src/main.ts';
                document.head.appendChild(script);
                
                addStatus('游戏脚本已加载', 'success');
                
            } catch (error) {
                addStatus('游戏启动失败: ' + error.message, 'error');
            }
        }

        // 页面加载完成后自动测试服务器连接
        window.addEventListener('load', () => {
            addStatus('页面加载完成', 'success');
            testServer();
        });
    </script>
</body>
</html>
