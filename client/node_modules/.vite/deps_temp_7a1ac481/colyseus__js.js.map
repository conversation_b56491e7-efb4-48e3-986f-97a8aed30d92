{"version": 3, "sources": ["../../../../node_modules/colyseus.js/src/legacy.ts", "../../../../node_modules/colyseus.js/src/errors/ServerError.ts", "../../../../node_modules/colyseus.js/src/msgpack/index.ts", "../../../../node_modules/colyseus.js/node_modules/ws/browser.js", "../../../../node_modules/colyseus.js/src/transport/WebSocketTransport.ts", "../../../../node_modules/colyseus.js/src/Connection.ts", "../../../../node_modules/colyseus.js/src/Protocol.ts", "../../../../node_modules/colyseus.js/src/serializer/Serializer.ts", "../../../../node_modules/colyseus.js/src/core/nanoevents.ts", "../../../../node_modules/colyseus.js/src/core/signal.ts", "../../../../node_modules/@colyseus/schema/build/umd/index.js", "../../../../node_modules/colyseus.js/src/Room.ts", "../../../../node_modules/httpie/xhr/index.mjs", "../../../../node_modules/colyseus.js/src/HTTP.ts", "../../../../node_modules/colyseus.js/src/Storage.ts", "../../../../node_modules/colyseus.js/src/Auth.ts", "../../../../node_modules/colyseus.js/src/3rd_party/discord.ts", "../../../../node_modules/colyseus.js/src/Client.ts", "../../../../node_modules/colyseus.js/src/serializer/SchemaSerializer.ts", "../../../../node_modules/colyseus.js/src/serializer/NoneSerializer.ts", "../../../../node_modules/colyseus.js/src/index.ts"], "sourcesContent": ["//\n// Polyfills for legacy environments\n//\n\n/*\n * Support Android 4.4.x\n */\nif (!ArrayBuffer.isView) {\n    ArrayBuffer.isView = (a: any): a is ArrayBufferView => {\n        return a !== null && typeof (a) === 'object' && a.buffer instanceof ArrayBuffer;\n    };\n}\n\n// Define globalThis if not available.\n// https://github.com/colyseus/colyseus.js/issues/86\nif (\n    typeof (globalThis) === \"undefined\" &&\n    typeof (window) !== \"undefined\"\n) {\n    // @ts-ignore\n    window['globalThis'] = window;\n}", "export enum CloseCode {\n    CONSENTED = 4000,\n    DEVMODE_RESTART = 4010\n}\n\nexport class ServerError extends Error {\n  public code: number;\n\n  constructor(code: number, message: string) {\n    super(message);\n\n    this.name = \"ServerError\";\n    this.code = code;\n  }\n}\n", "/**\n * Copyright (c) 2014 Ion Drive Software Ltd.\n * https://github.com/darrachequesne/notepack/\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\n\n/**\n * Patch for Colyseus:\n * -------------------\n * notepack.io@3.0.1\n *\n * added `offset` on Decoder constructor, for messages arriving with a code\n * before actual msgpack data\n */\n\n//\n// DECODER\n//\n\nfunction Decoder(buffer, offset) {\n    this._offset = offset;\n    if (buffer instanceof ArrayBuffer) {\n        this._buffer = buffer;\n        this._view = new DataView(this._buffer);\n    } else if (ArrayBuffer.isView(buffer)) {\n        this._buffer = buffer.buffer;\n        this._view = new DataView(this._buffer, buffer.byteOffset, buffer.byteLength);\n    } else {\n        throw new Error('Invalid argument');\n    }\n}\n\nfunction utf8Read(view, offset, length) {\n    var string = '', chr = 0;\n    for (var i = offset, end = offset + length; i < end; i++) {\n        var byte = view.getUint8(i);\n        if ((byte & 0x80) === 0x00) {\n            string += String.fromCharCode(byte);\n            continue;\n        }\n        if ((byte & 0xe0) === 0xc0) {\n            string += String.fromCharCode(\n                ((byte & 0x1f) << 6) |\n                (view.getUint8(++i) & 0x3f)\n            );\n            continue;\n        }\n        if ((byte & 0xf0) === 0xe0) {\n            string += String.fromCharCode(\n                ((byte & 0x0f) << 12) |\n                ((view.getUint8(++i) & 0x3f) << 6) |\n                ((view.getUint8(++i) & 0x3f) << 0)\n            );\n            continue;\n        }\n        if ((byte & 0xf8) === 0xf0) {\n            chr = ((byte & 0x07) << 18) |\n                ((view.getUint8(++i) & 0x3f) << 12) |\n                ((view.getUint8(++i) & 0x3f) << 6) |\n                ((view.getUint8(++i) & 0x3f) << 0);\n            if (chr >= 0x010000) { // surrogate pair\n                chr -= 0x010000;\n                string += String.fromCharCode((chr >>> 10) + 0xD800, (chr & 0x3FF) + 0xDC00);\n            } else {\n                string += String.fromCharCode(chr);\n            }\n            continue;\n        }\n        throw new Error('Invalid byte ' + byte.toString(16));\n    }\n    return string;\n}\n\nDecoder.prototype._array = function (length) {\n    var value = new Array(length);\n    for (var i = 0; i < length; i++) {\n        value[i] = this._parse();\n    }\n    return value;\n};\n\nDecoder.prototype._map = function (length) {\n    var key = '', value = {};\n    for (var i = 0; i < length; i++) {\n        key = this._parse();\n        value[key] = this._parse();\n    }\n    return value;\n};\n\nDecoder.prototype._str = function (length) {\n    var value = utf8Read(this._view, this._offset, length);\n    this._offset += length;\n    return value;\n};\n\nDecoder.prototype._bin = function (length) {\n    var value = this._buffer.slice(this._offset, this._offset + length);\n    this._offset += length;\n    return value;\n};\n\nDecoder.prototype._parse = function () {\n    var prefix = this._view.getUint8(this._offset++);\n    var value, length = 0, type = 0, hi = 0, lo = 0;\n\n    if (prefix < 0xc0) {\n        // positive fixint\n        if (prefix < 0x80) {\n            return prefix;\n        }\n        // fixmap\n        if (prefix < 0x90) {\n            return this._map(prefix & 0x0f);\n        }\n        // fixarray\n        if (prefix < 0xa0) {\n            return this._array(prefix & 0x0f);\n        }\n        // fixstr\n        return this._str(prefix & 0x1f);\n    }\n\n    // negative fixint\n    if (prefix > 0xdf) {\n        return (0xff - prefix + 1) * -1;\n    }\n\n    switch (prefix) {\n            // nil\n        case 0xc0:\n            return null;\n            // false\n        case 0xc2:\n            return false;\n            // true\n        case 0xc3:\n            return true;\n\n            // bin\n        case 0xc4:\n            length = this._view.getUint8(this._offset);\n            this._offset += 1;\n            return this._bin(length);\n        case 0xc5:\n            length = this._view.getUint16(this._offset);\n            this._offset += 2;\n            return this._bin(length);\n        case 0xc6:\n            length = this._view.getUint32(this._offset);\n            this._offset += 4;\n            return this._bin(length);\n\n            // ext\n        case 0xc7:\n            length = this._view.getUint8(this._offset);\n            type = this._view.getInt8(this._offset + 1);\n            this._offset += 2;\n            if (type === -1) {\n                // timestamp 96\n                var ns = this._view.getUint32(this._offset);\n                hi = this._view.getInt32(this._offset + 4);\n                lo = this._view.getUint32(this._offset + 8);\n                this._offset += 12;\n                return new Date((hi * 0x100000000 + lo) * 1e3 + ns / 1e6);\n            }\n            return [type, this._bin(length)];\n        case 0xc8:\n            length = this._view.getUint16(this._offset);\n            type = this._view.getInt8(this._offset + 2);\n            this._offset += 3;\n            return [type, this._bin(length)];\n        case 0xc9:\n            length = this._view.getUint32(this._offset);\n            type = this._view.getInt8(this._offset + 4);\n            this._offset += 5;\n            return [type, this._bin(length)];\n\n            // float\n        case 0xca:\n            value = this._view.getFloat32(this._offset);\n            this._offset += 4;\n            return value;\n        case 0xcb:\n            value = this._view.getFloat64(this._offset);\n            this._offset += 8;\n            return value;\n\n            // uint\n        case 0xcc:\n            value = this._view.getUint8(this._offset);\n            this._offset += 1;\n            return value;\n        case 0xcd:\n            value = this._view.getUint16(this._offset);\n            this._offset += 2;\n            return value;\n        case 0xce:\n            value = this._view.getUint32(this._offset);\n            this._offset += 4;\n            return value;\n        case 0xcf:\n            hi = this._view.getUint32(this._offset) * Math.pow(2, 32);\n            lo = this._view.getUint32(this._offset + 4);\n            this._offset += 8;\n            return hi + lo;\n\n            // int\n        case 0xd0:\n            value = this._view.getInt8(this._offset);\n            this._offset += 1;\n            return value;\n        case 0xd1:\n            value = this._view.getInt16(this._offset);\n            this._offset += 2;\n            return value;\n        case 0xd2:\n            value = this._view.getInt32(this._offset);\n            this._offset += 4;\n            return value;\n        case 0xd3:\n            hi = this._view.getInt32(this._offset) * Math.pow(2, 32);\n            lo = this._view.getUint32(this._offset + 4);\n            this._offset += 8;\n            return hi + lo;\n\n            // fixext\n        case 0xd4:\n            type = this._view.getInt8(this._offset);\n            this._offset += 1;\n            if (type === 0x00) {\n                // custom encoding for 'undefined' (kept for backward-compatibility)\n                this._offset += 1;\n                return void 0;\n            }\n            return [type, this._bin(1)];\n        case 0xd5:\n            type = this._view.getInt8(this._offset);\n            this._offset += 1;\n            return [type, this._bin(2)];\n        case 0xd6:\n            type = this._view.getInt8(this._offset);\n            this._offset += 1;\n            if (type === -1) {\n                // timestamp 32\n                value = this._view.getUint32(this._offset);\n                this._offset += 4;\n                return new Date(value * 1e3);\n            }\n            return [type, this._bin(4)];\n        case 0xd7:\n            type = this._view.getInt8(this._offset);\n            this._offset += 1;\n            if (type === 0x00) {\n                // custom date encoding (kept for backward-compatibility)\n                hi = this._view.getInt32(this._offset) * Math.pow(2, 32);\n                lo = this._view.getUint32(this._offset + 4);\n                this._offset += 8;\n                return new Date(hi + lo);\n            }\n            if (type === -1) {\n                // timestamp 64\n                hi = this._view.getUint32(this._offset);\n                lo = this._view.getUint32(this._offset + 4);\n                this._offset += 8;\n                var s = (hi & 0x3) * 0x100000000 + lo;\n                return new Date(s * 1e3 + (hi >>> 2) / 1e6);\n            }\n            return [type, this._bin(8)];\n        case 0xd8:\n            type = this._view.getInt8(this._offset);\n            this._offset += 1;\n            return [type, this._bin(16)];\n\n            // str\n        case 0xd9:\n            length = this._view.getUint8(this._offset);\n            this._offset += 1;\n            return this._str(length);\n        case 0xda:\n            length = this._view.getUint16(this._offset);\n            this._offset += 2;\n            return this._str(length);\n        case 0xdb:\n            length = this._view.getUint32(this._offset);\n            this._offset += 4;\n            return this._str(length);\n\n            // array\n        case 0xdc:\n            length = this._view.getUint16(this._offset);\n            this._offset += 2;\n            return this._array(length);\n        case 0xdd:\n            length = this._view.getUint32(this._offset);\n            this._offset += 4;\n            return this._array(length);\n\n            // map\n        case 0xde:\n            length = this._view.getUint16(this._offset);\n            this._offset += 2;\n            return this._map(length);\n        case 0xdf:\n            length = this._view.getUint32(this._offset);\n            this._offset += 4;\n            return this._map(length);\n    }\n\n    throw new Error('Could not parse');\n};\n\nfunction decode(buffer, offset = 0) {\n    var decoder = new Decoder(buffer, offset);\n    var value = decoder._parse();\n    if (decoder._offset !== buffer.byteLength) {\n        throw new Error((buffer.byteLength - decoder._offset) + ' trailing bytes');\n    }\n    return value;\n}\n\n//\n// ENCODER\n//\n\nvar TIMESTAMP32_MAX_SEC = 0x100000000 - 1; // 32-bit unsigned int\nvar TIMESTAMP64_MAX_SEC = 0x400000000 - 1; // 34-bit unsigned int\n\nfunction utf8Write(view, offset, str) {\n    var c = 0;\n    for (var i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            view.setUint8(offset++, c);\n        }\n        else if (c < 0x800) {\n            view.setUint8(offset++, 0xc0 | (c >> 6));\n            view.setUint8(offset++, 0x80 | (c & 0x3f));\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            view.setUint8(offset++, 0xe0 | (c >> 12));\n            view.setUint8(offset++, 0x80 | (c >> 6) & 0x3f);\n            view.setUint8(offset++, 0x80 | (c & 0x3f));\n        }\n        else {\n            i++;\n            c = 0x10000 + (((c & 0x3ff) << 10) | (str.charCodeAt(i) & 0x3ff));\n            view.setUint8(offset++, 0xf0 | (c >> 18));\n            view.setUint8(offset++, 0x80 | (c >> 12) & 0x3f);\n            view.setUint8(offset++, 0x80 | (c >> 6) & 0x3f);\n            view.setUint8(offset++, 0x80 | (c & 0x3f));\n        }\n    }\n}\n\nfunction utf8Length(str) {\n    var c = 0, length = 0;\n    for (var i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n\nfunction _encode(bytes, defers, value) {\n    var type = typeof value, i = 0, l = 0, hi = 0, lo = 0, length = 0, size = 0;\n\n    if (type === 'string') {\n        length = utf8Length(value);\n\n        // fixstr\n        if (length < 0x20) {\n            bytes.push(length | 0xa0);\n            size = 1;\n        }\n        // str 8\n        else if (length < 0x100) {\n            bytes.push(0xd9, length);\n            size = 2;\n        }\n        // str 16\n        else if (length < 0x10000) {\n            bytes.push(0xda, length >> 8, length);\n            size = 3;\n        }\n        // str 32\n        else if (length < 0x100000000) {\n            bytes.push(0xdb, length >> 24, length >> 16, length >> 8, length);\n            size = 5;\n        } else {\n            throw new Error('String too long');\n        }\n        defers.push({ _str: value, _length: length, _offset: bytes.length });\n        return size + length;\n    }\n    if (type === 'number') {\n        // TODO: encode to float 32?\n\n        // float 64\n        if (Math.floor(value) !== value || !isFinite(value)) {\n            bytes.push(0xcb);\n            defers.push({ _float: value, _length: 8, _offset: bytes.length });\n            return 9;\n        }\n\n        if (value >= 0) {\n            // positive fixnum\n            if (value < 0x80) {\n                bytes.push(value);\n                return 1;\n            }\n            // uint 8\n            if (value < 0x100) {\n                bytes.push(0xcc, value);\n                return 2;\n            }\n            // uint 16\n            if (value < 0x10000) {\n                bytes.push(0xcd, value >> 8, value);\n                return 3;\n            }\n            // uint 32\n            if (value < 0x100000000) {\n                bytes.push(0xce, value >> 24, value >> 16, value >> 8, value);\n                return 5;\n            }\n            // uint 64\n            hi = (value / Math.pow(2, 32)) >> 0;\n            lo = value >>> 0;\n            bytes.push(0xcf, hi >> 24, hi >> 16, hi >> 8, hi, lo >> 24, lo >> 16, lo >> 8, lo);\n            return 9;\n        } else {\n            // negative fixnum\n            if (value >= -0x20) {\n                bytes.push(value);\n                return 1;\n            }\n            // int 8\n            if (value >= -0x80) {\n                bytes.push(0xd0, value);\n                return 2;\n            }\n            // int 16\n            if (value >= -0x8000) {\n                bytes.push(0xd1, value >> 8, value);\n                return 3;\n            }\n            // int 32\n            if (value >= -0x80000000) {\n                bytes.push(0xd2, value >> 24, value >> 16, value >> 8, value);\n                return 5;\n            }\n            // int 64\n            hi = Math.floor(value / Math.pow(2, 32));\n            lo = value >>> 0;\n            bytes.push(0xd3, hi >> 24, hi >> 16, hi >> 8, hi, lo >> 24, lo >> 16, lo >> 8, lo);\n            return 9;\n        }\n    }\n    if (type === 'object') {\n        // nil\n        if (value === null) {\n            bytes.push(0xc0);\n            return 1;\n        }\n\n        if (Array.isArray(value)) {\n            length = value.length;\n\n            // fixarray\n            if (length < 0x10) {\n                bytes.push(length | 0x90);\n                size = 1;\n            }\n            // array 16\n            else if (length < 0x10000) {\n                bytes.push(0xdc, length >> 8, length);\n                size = 3;\n            }\n            // array 32\n            else if (length < 0x100000000) {\n                bytes.push(0xdd, length >> 24, length >> 16, length >> 8, length);\n                size = 5;\n            } else {\n                throw new Error('Array too large');\n            }\n            for (i = 0; i < length; i++) {\n                size += _encode(bytes, defers, value[i]);\n            }\n            return size;\n        }\n\n        if (value instanceof Date) {\n            var ms = value.getTime();\n            var s = Math.floor(ms / 1e3);\n            var ns = (ms - s * 1e3) * 1e6;\n\n            if (s >= 0 && ns >= 0 && s <= TIMESTAMP64_MAX_SEC) {\n                if (ns === 0 && s <= TIMESTAMP32_MAX_SEC) {\n                    // timestamp 32\n                    bytes.push(0xd6, 0xff, s >> 24, s >> 16, s >> 8, s);\n                    return 6;\n                } else {\n                    // timestamp 64\n                    hi = s / 0x100000000;\n                    lo = s & 0xffffffff;\n                    bytes.push(0xd7, 0xff, ns >> 22, ns >> 14, ns >> 6, hi, lo >> 24, lo >> 16, lo >> 8, lo);\n                    return 10;\n                }\n            } else {\n                // timestamp 96\n                hi = Math.floor(s / 0x100000000);\n                lo = s >>> 0;\n                bytes.push(0xc7, 0x0c, 0xff, ns >> 24, ns >> 16, ns >> 8, ns, hi >> 24, hi >> 16, hi >> 8, hi, lo >> 24, lo >> 16, lo >> 8, lo);\n                return 15;\n            }\n        }\n\n        if (value instanceof ArrayBuffer) {\n            length = value.byteLength;\n\n            // bin 8\n            if (length < 0x100) {\n                bytes.push(0xc4, length);\n                size = 2;\n            } else\n                // bin 16\n                if (length < 0x10000) {\n                    bytes.push(0xc5, length >> 8, length);\n                    size = 3;\n                } else\n                // bin 32\n                if (length < 0x100000000) {\n                    bytes.push(0xc6, length >> 24, length >> 16, length >> 8, length);\n                    size = 5;\n                } else {\n                    throw new Error('Buffer too large');\n                }\n            defers.push({ _bin: value, _length: length, _offset: bytes.length });\n            return size + length;\n        }\n\n        if (typeof value.toJSON === 'function') {\n            return _encode(bytes, defers, value.toJSON());\n        }\n\n        var keys = [], key = '';\n\n        var allKeys = Object.keys(value);\n        for (i = 0, l = allKeys.length; i < l; i++) {\n            key = allKeys[i];\n            if (value[key] !== undefined && typeof value[key] !== 'function') {\n                keys.push(key);\n            }\n        }\n        length = keys.length;\n\n        // fixmap\n        if (length < 0x10) {\n            bytes.push(length | 0x80);\n            size = 1;\n        }\n        // map 16\n        else if (length < 0x10000) {\n            bytes.push(0xde, length >> 8, length);\n            size = 3;\n        }\n        // map 32\n        else if (length < 0x100000000) {\n            bytes.push(0xdf, length >> 24, length >> 16, length >> 8, length);\n            size = 5;\n        } else {\n            throw new Error('Object too large');\n        }\n\n        for (i = 0; i < length; i++) {\n            key = keys[i];\n            size += _encode(bytes, defers, key);\n            size += _encode(bytes, defers, value[key]);\n        }\n        return size;\n    }\n    // false/true\n    if (type === 'boolean') {\n        bytes.push(value ? 0xc3 : 0xc2);\n        return 1;\n    }\n    if (type === 'undefined') {\n        bytes.push(0xc0);\n        return 1;\n    }\n    // custom types like BigInt (typeof value === 'bigint')\n    if (typeof value.toJSON === 'function') {\n        return _encode(bytes, defers, value.toJSON());\n    }\n    throw new Error('Could not encode');\n}\n\nfunction encode(value) {\n    var bytes = [];\n    var defers = [];\n    var size = _encode(bytes, defers, value);\n    var buf = new ArrayBuffer(size);\n    var view = new DataView(buf);\n\n    var deferIndex = 0;\n    var deferWritten = 0;\n    var nextOffset = -1;\n    if (defers.length > 0) {\n        nextOffset = defers[0]._offset;\n    }\n\n    var defer, deferLength = 0, offset = 0;\n    for (var i = 0, l = bytes.length; i < l; i++) {\n        view.setUint8(deferWritten + i, bytes[i]);\n        if (i + 1 !== nextOffset) { continue; }\n        defer = defers[deferIndex];\n        deferLength = defer._length;\n        offset = deferWritten + nextOffset;\n        if (defer._bin) {\n            var bin = new Uint8Array(defer._bin);\n            for (var j = 0; j < deferLength; j++) {\n                view.setUint8(offset + j, bin[j]);\n            }\n        } else if (defer._str) {\n            utf8Write(view, offset, defer._str);\n        } else if (defer._float !== undefined) {\n            view.setFloat64(offset, defer._float);\n        }\n        deferIndex++;\n        deferWritten += deferLength;\n        if (defers[deferIndex]) {\n            nextOffset = defers[deferIndex]._offset;\n        }\n    }\n    return buf;\n}\n\nexport { encode, decode };\n", "'use strict';\n\nmodule.exports = function () {\n  throw new Error(\n    'ws does not work in the browser. Browser clients must use the native ' +\n      'WebSocket object'\n  );\n};\n", "import NodeWebSocket from \"ws\";\nimport { ITransport, ITransportEventMap } from \"./ITransport\";\n\nconst WebSocket = globalThis.WebSocket || NodeWebSocket;\n\nexport class WebSocketTransport implements ITransport {\n    ws: WebSocket | NodeWebSocket;\n    protocols?: string | string[];\n\n    constructor(public events: ITransportEventMap) {}\n\n    public send(data: ArrayBuffer | Array<number>): void {\n        if (data instanceof ArrayBuffer) {\n            this.ws.send(data);\n\n        } else if (Array.isArray(data)) {\n            this.ws.send((new Uint8Array(data)).buffer);\n        }\n    }\n\n    /**\n     * @param url URL to connect to\n     * @param headers custom headers to send with the connection (only supported in Node.js. Web Browsers do not allow setting custom headers)\n     */\n    public connect(url: string, headers?: any): void {\n        try {\n            // Node or Bun environments (supports custom headers)\n            this.ws = new WebSocket(url, { headers, protocols: this.protocols });\n\n        } catch (e) {\n            // browser environment (custom headers not supported)\n            this.ws = new WebSocket(url, this.protocols);\n        }\n\n        this.ws.binaryType = 'arraybuffer';\n        this.ws.onopen = this.events.onopen;\n        this.ws.onmessage = this.events.onmessage;\n        this.ws.onclose = this.events.onclose;\n        this.ws.onerror = this.events.onerror;\n    }\n\n    public close(code?: number, reason?: string) {\n        this.ws.close(code, reason);\n    }\n\n    get isOpen() {\n        return this.ws.readyState === WebSocket.OPEN;\n    }\n\n}\n", "import { ITransport, ITransportEventMap } from \"./transport/ITransport\";\nimport { WebSocketTransport } from \"./transport/WebSocketTransport\";\n\nexport class Connection implements ITransport {\n    transport: ITransport;\n    events: ITransportEventMap = {};\n\n    constructor() {\n        this.transport = new WebSocketTransport(this.events);\n    }\n\n    send(data: ArrayBuffer | Array<number>): void {\n        this.transport.send(data);\n    }\n\n\n    connect(url: string, options: any): void {\n        this.transport.connect(url, options);\n    }\n\n    close(code?: number, reason?: string): void {\n        this.transport.close(code, reason);\n    }\n\n    get isOpen() {\n        return this.transport.isOpen;\n    }\n\n}\n", "// Use codes between 0~127 for lesser throughput (1 byte)\n\nexport enum Protocol {\n    // Room-related (10~19)\n    HANDSHAKE = 9,\n    JOIN_ROOM = 10,\n    ERROR = 11,\n    LEAVE_ROOM = 12,\n    ROOM_DATA = 13,\n    ROOM_STATE = 14,\n    ROOM_STATE_PATCH = 15,\n    ROOM_DATA_SCHEMA = 16,\n    ROOM_DATA_BYTES = 17,\n}\n\nexport enum ErrorCode {\n    MATCHMAKE_NO_HANDLER = 4210,\n    MATCHMAKE_INVALID_CRITERIA = 4211,\n    MATCHMAKE_INVALID_ROOM_ID = 4212,\n    MATCHMAKE_UNHANDLED = 4213,\n    MATCHMAKE_EXPIRED = 4214,\n\n    AUTH_FAILED = 4215,\n    APPLICATION_ERROR = 4216,\n}\n\nexport function utf8Read(view: number[], offset: number) {\n  const length = view[offset++];\n\n  var string = '', chr = 0;\n  for (var i = offset, end = offset + length; i < end; i++) {\n    var byte = view[i];\n    if ((byte & 0x80) === 0x00) {\n      string += String.fromCharCode(byte);\n      continue;\n    }\n    if ((byte & 0xe0) === 0xc0) {\n      string += String.fromCharCode(\n        ((byte & 0x1f) << 6) |\n        (view[++i] & 0x3f)\n      );\n      continue;\n    }\n    if ((byte & 0xf0) === 0xe0) {\n      string += String.fromCharCode(\n        ((byte & 0x0f) << 12) |\n        ((view[++i] & 0x3f) << 6) |\n        ((view[++i] & 0x3f) << 0)\n      );\n      continue;\n    }\n    if ((byte & 0xf8) === 0xf0) {\n      chr = ((byte & 0x07) << 18) |\n        ((view[++i] & 0x3f) << 12) |\n        ((view[++i] & 0x3f) << 6) |\n        ((view[++i] & 0x3f) << 0);\n      if (chr >= 0x010000) { // surrogate pair\n        chr -= 0x010000;\n        string += String.fromCharCode((chr >>> 10) + 0xD800, (chr & 0x3FF) + 0xDC00);\n      } else {\n        string += String.fromCharCode(chr);\n      }\n      continue;\n    }\n    throw new Error('Invalid byte ' + byte.toString(16));\n  }\n  return string;\n}\n\n// Faster for short strings than Buffer.byteLength\nexport function utf8Length(str: string = '') {\n  let c = 0;\n  let length = 0;\n  for (let i = 0, l = str.length; i < l; i++) {\n    c = str.charCodeAt(i);\n    if (c < 0x80) {\n      length += 1;\n    } else if (c < 0x800) {\n      length += 2;\n    } else if (c < 0xd800 || c >= 0xe000) {\n      length += 3;\n    } else {\n      i++;\n      length += 4;\n    }\n  }\n  return length + 1;\n}\n", "export interface Serializer<State> {\n    setState(data: any): void;\n    getState(): State;\n\n    patch(data: any): void;\n    teardown(): void;\n\n    handshake?(bytes: number[], it?: any): void;\n}\n\nconst serializers: { [id: string]: any } = {};\n\nexport function registerSerializer (id: string, serializer: any) {\n    serializers[id] = serializer;\n}\n\nexport function getSerializer (id: string) {\n    const serializer = serializers[id];\n    if (!serializer) { throw new Error(\"missing serializer: \" + id); }\n    return serializer;\n}", "/**\n * The MIT License (MIT)\n *\n * Copyright 2016 <PERSON><PERSON> <<EMAIL>>\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy of\n * this software and associated documentation files (the \"Software\"), to deal in\n * the Software without restriction, including without limitation the rights to\n * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\n * the Software, and to permit persons to whom the Software is furnished to do so,\n * subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\n * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\n * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\n * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n */\n\nexport const createNanoEvents = () => ({\n    emit(event: string, ...args: any[]) {\n        let callbacks = this.events[event] || []\n        for (let i = 0, length = callbacks.length; i < length; i++) {\n            callbacks[i](...args)\n        }\n    },\n    events: {},\n    on(event: string, cb: (...args: any[]) => void) {\n        this.events[event]?.push(cb) || (this.events[event] = [cb])\n        return () => {\n            this.events[event] = this.events[event]?.filter(i => cb !== i)\n        }\n    }\n});", "type FunctionParameters<T extends (...args: any[]) => any> =\n  T extends (...args: infer P) => any\n    ? P\n    : never;\n\nexport class EventEmitter<CallbackSignature extends (...args: any[]) => any> {\n  handlers: Array<CallbackSignature> = [];\n\n  register(cb: CallbackSignature, once: boolean = false) {\n    this.handlers.push(cb);\n    return this;\n  }\n\n  invoke(...args: FunctionParameters<CallbackSignature>) {\n    this.handlers.forEach((handler) => handler.apply(this, args));\n  }\n\n  invokeAsync(...args: FunctionParameters<CallbackSignature>) {\n    return Promise.all(this.handlers.map((handler) => handler.apply(this, args)));\n  }\n\n  remove (cb: CallbackSignature) {\n    const index = this.handlers.indexOf(cb);\n    this.handlers[index] = this.handlers[this.handlers.length - 1];\n    this.handlers.pop();\n  }\n\n  clear() {\n    this.handlers = [];\n  }\n}\n\nexport function createSignal<CallbackSignature extends (...args: any[]) => void | Promise<any>>()\n  : \n   {\n    once: (cb: CallbackSignature) => void;\n    remove: (cb: CallbackSignature) => void,\n    invoke: (...args: FunctionParameters<CallbackSignature>) => void,\n    invokeAsync: (...args: FunctionParameters<CallbackSignature>) => Promise<any[]>,\n    clear: () => void,\n  } & ((this: any, cb: CallbackSignature) => EventEmitter<CallbackSignature> )\n  {\n  const emitter = new EventEmitter<CallbackSignature>();\n\n  function register(this: any, cb: CallbackSignature): EventEmitter<CallbackSignature> {\n    return emitter.register(cb, this === null);\n  };\n\n  register.once = (cb: CallbackSignature) => {\n    const callback: any = function (...args: any[]) {\n      cb.apply(this, args);\n      emitter.remove(callback);\n    }\n    emitter.register(callback);\n  }\n  register.remove = (cb: CallbackSignature) => emitter.remove(cb)\n  register.invoke = (...args: FunctionParameters<CallbackSignature>) => emitter.invoke(...args);\n  register.invokeAsync = (...args: FunctionParameters<CallbackSignature>) => emitter.invokeAsync(...args);\n  register.clear = () => emitter.clear();\n\n  return register;\n}", "(function (global, factory) {\n    typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :\n    typeof define === 'function' && define.amd ? define(['exports'], factory) :\n    (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.schema = {}));\n})(this, (function (exports) { 'use strict';\n\n    /******************************************************************************\r\n    Copyright (c) Microsoft Corporation.\r\n\r\n    Permission to use, copy, modify, and/or distribute this software for any\r\n    purpose with or without fee is hereby granted.\r\n\r\n    THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\n    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\n    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\n    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\n    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\n    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\n    PERFORMANCE OF THIS SOFTWARE.\r\n    ***************************************************************************** */\r\n    /* global Reflect, Promise, SuppressedError, Symbol, Iterator */\r\n\r\n    var extendStatics = function(d, b) {\r\n        extendStatics = Object.setPrototypeOf ||\r\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n        return extendStatics(d, b);\r\n    };\r\n\r\n    function __extends(d, b) {\r\n        if (typeof b !== \"function\" && b !== null)\r\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n        extendStatics(d, b);\r\n        function __() { this.constructor = d; }\r\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n    }\r\n\r\n    function __decorate(decorators, target, key, desc) {\r\n        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n        if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n        return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n    }\r\n\r\n    function __spreadArray(to, from, pack) {\r\n        if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n            if (ar || !(i in from)) {\r\n                if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n                ar[i] = from[i];\r\n            }\r\n        }\r\n        return to.concat(ar || Array.prototype.slice.call(from));\r\n    }\r\n\r\n    typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n        var e = new Error(message);\r\n        return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n    };\n\n    // export const SWITCH_TO_STRUCTURE = 193; (easily collides with DELETE_AND_ADD + fieldIndex = 2)\n    var SWITCH_TO_STRUCTURE = 255; // (decoding collides with DELETE_AND_ADD + fieldIndex = 63)\n    var TYPE_ID = 213;\n    /**\n     * Encoding Schema field operations.\n     */\n    exports.OPERATION = void 0;\n    (function (OPERATION) {\n        // add new structure/primitive\n        OPERATION[OPERATION[\"ADD\"] = 128] = \"ADD\";\n        // replace structure/primitive\n        OPERATION[OPERATION[\"REPLACE\"] = 0] = \"REPLACE\";\n        // delete field\n        OPERATION[OPERATION[\"DELETE\"] = 64] = \"DELETE\";\n        // DELETE field, followed by an ADD\n        OPERATION[OPERATION[\"DELETE_AND_ADD\"] = 192] = \"DELETE_AND_ADD\";\n        // TOUCH is used to determine hierarchy of nested Schema structures during serialization.\n        // touches are NOT encoded.\n        OPERATION[OPERATION[\"TOUCH\"] = 1] = \"TOUCH\";\n        // MapSchema Operations\n        OPERATION[OPERATION[\"CLEAR\"] = 10] = \"CLEAR\";\n    })(exports.OPERATION || (exports.OPERATION = {}));\n    // export enum OPERATION {\n    //     // add new structure/primitive\n    //     // (128)\n    //     ADD = 128, // 10000000,\n    //     // replace structure/primitive\n    //     REPLACE = 1,// 00000001\n    //     // delete field\n    //     DELETE = 192, // 11000000\n    //     // DELETE field, followed by an ADD\n    //     DELETE_AND_ADD = 224, // 11100000\n    //     // TOUCH is used to determine hierarchy of nested Schema structures during serialization.\n    //     // touches are NOT encoded.\n    //     TOUCH = 0, // 00000000\n    //     // MapSchema Operations\n    //     CLEAR = 10,\n    // }\n\n    var ChangeTree = /** @class */ (function () {\n        function ChangeTree(ref, parent, root) {\n            this.changed = false;\n            this.changes = new Map();\n            this.allChanges = new Set();\n            // cached indexes for filtering\n            this.caches = {};\n            this.currentCustomOperation = 0;\n            this.ref = ref;\n            this.setParent(parent, root);\n        }\n        ChangeTree.prototype.setParent = function (parent, root, parentIndex) {\n            var _this = this;\n            if (!this.indexes) {\n                this.indexes = (this.ref instanceof Schema)\n                    ? this.ref['_definition'].indexes\n                    : {};\n            }\n            this.parent = parent;\n            this.parentIndex = parentIndex;\n            // avoid setting parents with empty `root`\n            if (!root) {\n                return;\n            }\n            this.root = root;\n            //\n            // assign same parent on child structures\n            //\n            if (this.ref instanceof Schema) {\n                var definition = this.ref['_definition'];\n                for (var field in definition.schema) {\n                    var value = this.ref[field];\n                    if (value && value['$changes']) {\n                        var parentIndex_1 = definition.indexes[field];\n                        value['$changes'].setParent(this.ref, root, parentIndex_1);\n                    }\n                }\n            }\n            else if (typeof (this.ref) === \"object\") {\n                this.ref.forEach(function (value, key) {\n                    if (value instanceof Schema) {\n                        var changeTreee = value['$changes'];\n                        var parentIndex_2 = _this.ref['$changes'].indexes[key];\n                        changeTreee.setParent(_this.ref, _this.root, parentIndex_2);\n                    }\n                });\n            }\n        };\n        ChangeTree.prototype.operation = function (op) {\n            this.changes.set(--this.currentCustomOperation, op);\n        };\n        ChangeTree.prototype.change = function (fieldName, operation) {\n            if (operation === void 0) { operation = exports.OPERATION.ADD; }\n            var index = (typeof (fieldName) === \"number\")\n                ? fieldName\n                : this.indexes[fieldName];\n            this.assertValidIndex(index, fieldName);\n            var previousChange = this.changes.get(index);\n            if (!previousChange ||\n                previousChange.op === exports.OPERATION.DELETE ||\n                previousChange.op === exports.OPERATION.TOUCH // (mazmorra.io's BattleAction issue)\n            ) {\n                this.changes.set(index, {\n                    op: (!previousChange)\n                        ? operation\n                        : (previousChange.op === exports.OPERATION.DELETE)\n                            ? exports.OPERATION.DELETE_AND_ADD\n                            : operation,\n                    // : OPERATION.REPLACE,\n                    index: index\n                });\n            }\n            this.allChanges.add(index);\n            this.changed = true;\n            this.touchParents();\n        };\n        ChangeTree.prototype.touch = function (fieldName) {\n            var index = (typeof (fieldName) === \"number\")\n                ? fieldName\n                : this.indexes[fieldName];\n            this.assertValidIndex(index, fieldName);\n            if (!this.changes.has(index)) {\n                this.changes.set(index, { op: exports.OPERATION.TOUCH, index: index });\n            }\n            this.allChanges.add(index);\n            // ensure touch is placed until the $root is found.\n            this.touchParents();\n        };\n        ChangeTree.prototype.touchParents = function () {\n            if (this.parent) {\n                this.parent['$changes'].touch(this.parentIndex);\n            }\n        };\n        ChangeTree.prototype.getType = function (index) {\n            if (this.ref['_definition']) {\n                var definition = this.ref['_definition'];\n                return definition.schema[definition.fieldsByIndex[index]];\n            }\n            else {\n                var definition = this.parent['_definition'];\n                var parentType = definition.schema[definition.fieldsByIndex[this.parentIndex]];\n                //\n                // Get the child type from parent structure.\n                // - [\"string\"] => \"string\"\n                // - { map: \"string\" } => \"string\"\n                // - { set: \"string\" } => \"string\"\n                //\n                return Object.values(parentType)[0];\n            }\n        };\n        ChangeTree.prototype.getChildrenFilter = function () {\n            var childFilters = this.parent['_definition'].childFilters;\n            return childFilters && childFilters[this.parentIndex];\n        };\n        //\n        // used during `.encode()`\n        //\n        ChangeTree.prototype.getValue = function (index) {\n            return this.ref['getByIndex'](index);\n        };\n        ChangeTree.prototype.delete = function (fieldName) {\n            var index = (typeof (fieldName) === \"number\")\n                ? fieldName\n                : this.indexes[fieldName];\n            if (index === undefined) {\n                console.warn(\"@colyseus/schema \".concat(this.ref.constructor.name, \": trying to delete non-existing index: \").concat(fieldName, \" (\").concat(index, \")\"));\n                return;\n            }\n            var previousValue = this.getValue(index);\n            // console.log(\"$changes.delete =>\", { fieldName, index, previousValue });\n            this.changes.set(index, { op: exports.OPERATION.DELETE, index: index });\n            this.allChanges.delete(index);\n            // delete cache\n            delete this.caches[index];\n            // remove `root` reference\n            if (previousValue && previousValue['$changes']) {\n                previousValue['$changes'].parent = undefined;\n            }\n            this.changed = true;\n            this.touchParents();\n        };\n        ChangeTree.prototype.discard = function (changed, discardAll) {\n            var _this = this;\n            if (changed === void 0) { changed = false; }\n            if (discardAll === void 0) { discardAll = false; }\n            //\n            // Map, Array, etc:\n            // Remove cached key to ensure ADD operations is unsed instead of\n            // REPLACE in case same key is used on next patches.\n            //\n            // TODO: refactor this. this is not relevant for Collection and Set.\n            //\n            if (!(this.ref instanceof Schema)) {\n                this.changes.forEach(function (change) {\n                    if (change.op === exports.OPERATION.DELETE) {\n                        var index = _this.ref['getIndex'](change.index);\n                        delete _this.indexes[index];\n                    }\n                });\n            }\n            this.changes.clear();\n            this.changed = changed;\n            if (discardAll) {\n                this.allChanges.clear();\n            }\n            // re-set `currentCustomOperation`\n            this.currentCustomOperation = 0;\n        };\n        /**\n         * Recursively discard all changes from this, and child structures.\n         */\n        ChangeTree.prototype.discardAll = function () {\n            var _this = this;\n            this.changes.forEach(function (change) {\n                var value = _this.getValue(change.index);\n                if (value && value['$changes']) {\n                    value['$changes'].discardAll();\n                }\n            });\n            this.discard();\n        };\n        // cache(field: number, beginIndex: number, endIndex: number) {\n        ChangeTree.prototype.cache = function (field, cachedBytes) {\n            this.caches[field] = cachedBytes;\n        };\n        ChangeTree.prototype.clone = function () {\n            return new ChangeTree(this.ref, this.parent, this.root);\n        };\n        ChangeTree.prototype.ensureRefId = function () {\n            // skip if refId is already set.\n            if (this.refId !== undefined) {\n                return;\n            }\n            this.refId = this.root.getNextUniqueId();\n        };\n        ChangeTree.prototype.assertValidIndex = function (index, fieldName) {\n            if (index === undefined) {\n                throw new Error(\"ChangeTree: missing index for field \\\"\".concat(fieldName, \"\\\"\"));\n            }\n        };\n        return ChangeTree;\n    }());\n\n    function addCallback($callbacks, op, callback, existing) {\n        // initialize list of callbacks\n        if (!$callbacks[op]) {\n            $callbacks[op] = [];\n        }\n        $callbacks[op].push(callback);\n        //\n        // Trigger callback for existing elements\n        // - OPERATION.ADD\n        // - OPERATION.REPLACE\n        //\n        existing === null || existing === void 0 ? void 0 : existing.forEach(function (item, key) { return callback(item, key); });\n        return function () { return spliceOne($callbacks[op], $callbacks[op].indexOf(callback)); };\n    }\n    function removeChildRefs(changes) {\n        var _this = this;\n        var needRemoveRef = (typeof (this.$changes.getType()) !== \"string\");\n        this.$items.forEach(function (item, key) {\n            changes.push({\n                refId: _this.$changes.refId,\n                op: exports.OPERATION.DELETE,\n                field: key,\n                value: undefined,\n                previousValue: item\n            });\n            if (needRemoveRef) {\n                _this.$changes.root.removeRef(item['$changes'].refId);\n            }\n        });\n    }\n    function spliceOne(arr, index) {\n        // manually splice an array\n        if (index === -1 || index >= arr.length) {\n            return false;\n        }\n        var len = arr.length - 1;\n        for (var i = index; i < len; i++) {\n            arr[i] = arr[i + 1];\n        }\n        arr.length = len;\n        return true;\n    }\n\n    var DEFAULT_SORT = function (a, b) {\n        var A = a.toString();\n        var B = b.toString();\n        if (A < B)\n            return -1;\n        else if (A > B)\n            return 1;\n        else\n            return 0;\n    };\n    function getArrayProxy(value) {\n        value['$proxy'] = true;\n        //\n        // compatibility with @colyseus/schema 0.5.x\n        // - allow `map[\"key\"]`\n        // - allow `map[\"key\"] = \"xxx\"`\n        // - allow `delete map[\"key\"]`\n        //\n        value = new Proxy(value, {\n            get: function (obj, prop) {\n                if (typeof (prop) !== \"symbol\" &&\n                    !isNaN(prop) // https://stackoverflow.com/a/175787/892698\n                ) {\n                    return obj.at(prop);\n                }\n                else {\n                    return obj[prop];\n                }\n            },\n            set: function (obj, prop, setValue) {\n                if (typeof (prop) !== \"symbol\" &&\n                    !isNaN(prop)) {\n                    var indexes = Array.from(obj['$items'].keys());\n                    var key = parseInt(indexes[prop] || prop);\n                    if (setValue === undefined || setValue === null) {\n                        obj.deleteAt(key);\n                    }\n                    else {\n                        obj.setAt(key, setValue);\n                    }\n                }\n                else {\n                    obj[prop] = setValue;\n                }\n                return true;\n            },\n            deleteProperty: function (obj, prop) {\n                if (typeof (prop) === \"number\") {\n                    obj.deleteAt(prop);\n                }\n                else {\n                    delete obj[prop];\n                }\n                return true;\n            },\n            has: function (obj, key) {\n                if (typeof (key) !== \"symbol\" &&\n                    !isNaN(Number(key))) {\n                    return obj['$items'].has(Number(key));\n                }\n                return Reflect.has(obj, key);\n            }\n        });\n        return value;\n    }\n    var ArraySchema = /** @class */ (function () {\n        function ArraySchema() {\n            var items = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                items[_i] = arguments[_i];\n            }\n            this.$changes = new ChangeTree(this);\n            this.$items = new Map();\n            this.$indexes = new Map();\n            this.$refId = 0;\n            this.push.apply(this, items);\n        }\n        ArraySchema.prototype.onAdd = function (callback, triggerAll) {\n            if (triggerAll === void 0) { triggerAll = true; }\n            return addCallback((this.$callbacks || (this.$callbacks = {})), exports.OPERATION.ADD, callback, (triggerAll)\n                ? this.$items\n                : undefined);\n        };\n        ArraySchema.prototype.onRemove = function (callback) { return addCallback(this.$callbacks || (this.$callbacks = {}), exports.OPERATION.DELETE, callback); };\n        ArraySchema.prototype.onChange = function (callback) { return addCallback(this.$callbacks || (this.$callbacks = {}), exports.OPERATION.REPLACE, callback); };\n        ArraySchema.is = function (type) {\n            return (\n            // type format: [\"string\"]\n            Array.isArray(type) ||\n                // type format: { array: \"string\" }\n                (type['array'] !== undefined));\n        };\n        Object.defineProperty(ArraySchema.prototype, \"length\", {\n            get: function () {\n                return this.$items.size;\n            },\n            set: function (value) {\n                if (value === 0) {\n                    this.clear();\n                }\n                else {\n                    this.splice(value, this.length - value);\n                }\n            },\n            enumerable: false,\n            configurable: true\n        });\n        ArraySchema.prototype.push = function () {\n            var _this = this;\n            var values = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                values[_i] = arguments[_i];\n            }\n            var lastIndex;\n            values.forEach(function (value) {\n                // set \"index\" for reference.\n                lastIndex = _this.$refId++;\n                _this.setAt(lastIndex, value);\n            });\n            return lastIndex;\n        };\n        /**\n         * Removes the last element from an array and returns it.\n         */\n        ArraySchema.prototype.pop = function () {\n            var key = Array.from(this.$indexes.values()).pop();\n            if (key === undefined) {\n                return undefined;\n            }\n            this.$changes.delete(key);\n            this.$indexes.delete(key);\n            var value = this.$items.get(key);\n            this.$items.delete(key);\n            return value;\n        };\n        ArraySchema.prototype.at = function (index) {\n            //\n            // FIXME: this should be O(1)\n            //\n            index = Math.trunc(index) || 0;\n            // Allow negative indexing from the end\n            if (index < 0)\n                index += this.length;\n            // OOB access is guaranteed to return undefined\n            if (index < 0 || index >= this.length)\n                return undefined;\n            var key = Array.from(this.$items.keys())[index];\n            return this.$items.get(key);\n        };\n        ArraySchema.prototype.setAt = function (index, value) {\n            var _a, _b;\n            if (value === undefined || value === null) {\n                console.error(\"ArraySchema items cannot be null nor undefined; Use `deleteAt(index)` instead.\");\n                return;\n            }\n            // skip if the value is the same as cached.\n            if (this.$items.get(index) === value) {\n                return;\n            }\n            if (value['$changes'] !== undefined) {\n                value['$changes'].setParent(this, this.$changes.root, index);\n            }\n            var operation = (_b = (_a = this.$changes.indexes[index]) === null || _a === void 0 ? void 0 : _a.op) !== null && _b !== void 0 ? _b : exports.OPERATION.ADD;\n            this.$changes.indexes[index] = index;\n            this.$indexes.set(index, index);\n            this.$items.set(index, value);\n            this.$changes.change(index, operation);\n        };\n        ArraySchema.prototype.deleteAt = function (index) {\n            var key = Array.from(this.$items.keys())[index];\n            if (key === undefined) {\n                return false;\n            }\n            return this.$deleteAt(key);\n        };\n        ArraySchema.prototype.$deleteAt = function (index) {\n            // delete at internal index\n            this.$changes.delete(index);\n            this.$indexes.delete(index);\n            return this.$items.delete(index);\n        };\n        ArraySchema.prototype.clear = function (changes) {\n            // discard previous operations.\n            this.$changes.discard(true, true);\n            this.$changes.indexes = {};\n            // clear previous indexes\n            this.$indexes.clear();\n            //\n            // When decoding:\n            // - enqueue items for DELETE callback.\n            // - flag child items for garbage collection.\n            //\n            if (changes) {\n                removeChildRefs.call(this, changes);\n            }\n            // clear items\n            this.$items.clear();\n            this.$changes.operation({ index: 0, op: exports.OPERATION.CLEAR });\n            // touch all structures until reach root\n            this.$changes.touchParents();\n        };\n        /**\n         * Combines two or more arrays.\n         * @param items Additional items to add to the end of array1.\n         */\n        // @ts-ignore\n        ArraySchema.prototype.concat = function () {\n            var _a;\n            var items = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                items[_i] = arguments[_i];\n            }\n            return new (ArraySchema.bind.apply(ArraySchema, __spreadArray([void 0], (_a = Array.from(this.$items.values())).concat.apply(_a, items), false)))();\n        };\n        /**\n         * Adds all the elements of an array separated by the specified separator string.\n         * @param separator A string used to separate one element of an array from the next in the resulting String. If omitted, the array elements are separated with a comma.\n         */\n        ArraySchema.prototype.join = function (separator) {\n            return Array.from(this.$items.values()).join(separator);\n        };\n        /**\n         * Reverses the elements in an Array.\n         */\n        // @ts-ignore\n        ArraySchema.prototype.reverse = function () {\n            var _this = this;\n            var indexes = Array.from(this.$items.keys());\n            var reversedItems = Array.from(this.$items.values()).reverse();\n            reversedItems.forEach(function (item, i) {\n                _this.setAt(indexes[i], item);\n            });\n            return this;\n        };\n        /**\n         * Removes the first element from an array and returns it.\n         */\n        ArraySchema.prototype.shift = function () {\n            var indexes = Array.from(this.$items.keys());\n            var shiftAt = indexes.shift();\n            if (shiftAt === undefined) {\n                return undefined;\n            }\n            var value = this.$items.get(shiftAt);\n            this.$deleteAt(shiftAt);\n            return value;\n        };\n        /**\n         * Returns a section of an array.\n         * @param start The beginning of the specified portion of the array.\n         * @param end The end of the specified portion of the array. This is exclusive of the element at the index 'end'.\n         */\n        ArraySchema.prototype.slice = function (start, end) {\n            var sliced = new ArraySchema();\n            sliced.push.apply(sliced, Array.from(this.$items.values()).slice(start, end));\n            return sliced;\n        };\n        /**\n         * Sorts an array.\n         * @param compareFn Function used to determine the order of the elements. It is expected to return\n         * a negative value if first argument is less than second argument, zero if they're equal and a positive\n         * value otherwise. If omitted, the elements are sorted in ascending, ASCII character order.\n         * ```ts\n         * [11,2,22,1].sort((a, b) => a - b)\n         * ```\n         */\n        ArraySchema.prototype.sort = function (compareFn) {\n            var _this = this;\n            if (compareFn === void 0) { compareFn = DEFAULT_SORT; }\n            var indexes = Array.from(this.$items.keys());\n            var sortedItems = Array.from(this.$items.values()).sort(compareFn);\n            sortedItems.forEach(function (item, i) {\n                _this.setAt(indexes[i], item);\n            });\n            return this;\n        };\n        /**\n         * Removes elements from an array and, if necessary, inserts new elements in their place, returning the deleted elements.\n         * @param start The zero-based location in the array from which to start removing elements.\n         * @param deleteCount The number of elements to remove.\n         * @param items Elements to insert into the array in place of the deleted elements.\n         */\n        ArraySchema.prototype.splice = function (start, deleteCount) {\n            if (deleteCount === void 0) { deleteCount = this.length - start; }\n            var items = [];\n            for (var _i = 2; _i < arguments.length; _i++) {\n                items[_i - 2] = arguments[_i];\n            }\n            var indexes = Array.from(this.$items.keys());\n            var removedItems = [];\n            for (var i = start; i < start + deleteCount; i++) {\n                removedItems.push(this.$items.get(indexes[i]));\n                this.$deleteAt(indexes[i]);\n            }\n            for (var i = 0; i < items.length; i++) {\n                this.setAt(start + i, items[i]);\n            }\n            return removedItems;\n        };\n        /**\n         * Inserts new elements at the start of an array.\n         * @param items  Elements to insert at the start of the Array.\n         */\n        ArraySchema.prototype.unshift = function () {\n            var _this = this;\n            var items = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                items[_i] = arguments[_i];\n            }\n            var length = this.length;\n            var addedLength = items.length;\n            // const indexes = Array.from(this.$items.keys());\n            var previousValues = Array.from(this.$items.values());\n            items.forEach(function (item, i) {\n                _this.setAt(i, item);\n            });\n            previousValues.forEach(function (previousValue, i) {\n                _this.setAt(addedLength + i, previousValue);\n            });\n            return length + addedLength;\n        };\n        /**\n         * Returns the index of the first occurrence of a value in an array.\n         * @param searchElement The value to locate in the array.\n         * @param fromIndex The array index at which to begin the search. If fromIndex is omitted, the search starts at index 0.\n         */\n        ArraySchema.prototype.indexOf = function (searchElement, fromIndex) {\n            return Array.from(this.$items.values()).indexOf(searchElement, fromIndex);\n        };\n        /**\n         * Returns the index of the last occurrence of a specified value in an array.\n         * @param searchElement The value to locate in the array.\n         * @param fromIndex The array index at which to begin the search. If fromIndex is omitted, the search starts at the last index in the array.\n         */\n        ArraySchema.prototype.lastIndexOf = function (searchElement, fromIndex) {\n            if (fromIndex === void 0) { fromIndex = this.length - 1; }\n            return Array.from(this.$items.values()).lastIndexOf(searchElement, fromIndex);\n        };\n        ArraySchema.prototype.every = function (callbackfn, thisArg) {\n            return Array.from(this.$items.values()).every(callbackfn, thisArg);\n        };\n        /**\n         * Determines whether the specified callback function returns true for any element of an array.\n         * @param callbackfn A function that accepts up to three arguments. The some method calls\n         * the callbackfn function for each element in the array until the callbackfn returns a value\n         * which is coercible to the Boolean value true, or until the end of the array.\n         * @param thisArg An object to which the this keyword can refer in the callbackfn function.\n         * If thisArg is omitted, undefined is used as the this value.\n         */\n        ArraySchema.prototype.some = function (callbackfn, thisArg) {\n            return Array.from(this.$items.values()).some(callbackfn, thisArg);\n        };\n        /**\n         * Performs the specified action for each element in an array.\n         * @param callbackfn  A function that accepts up to three arguments. forEach calls the callbackfn function one time for each element in the array.\n         * @param thisArg  An object to which the this keyword can refer in the callbackfn function. If thisArg is omitted, undefined is used as the this value.\n         */\n        ArraySchema.prototype.forEach = function (callbackfn, thisArg) {\n            Array.from(this.$items.values()).forEach(callbackfn, thisArg);\n        };\n        /**\n         * Calls a defined callback function on each element of an array, and returns an array that contains the results.\n         * @param callbackfn A function that accepts up to three arguments. The map method calls the callbackfn function one time for each element in the array.\n         * @param thisArg An object to which the this keyword can refer in the callbackfn function. If thisArg is omitted, undefined is used as the this value.\n         */\n        ArraySchema.prototype.map = function (callbackfn, thisArg) {\n            return Array.from(this.$items.values()).map(callbackfn, thisArg);\n        };\n        ArraySchema.prototype.filter = function (callbackfn, thisArg) {\n            return Array.from(this.$items.values()).filter(callbackfn, thisArg);\n        };\n        /**\n         * Calls the specified callback function for all the elements in an array. The return value of the callback function is the accumulated result, and is provided as an argument in the next call to the callback function.\n         * @param callbackfn A function that accepts up to four arguments. The reduce method calls the callbackfn function one time for each element in the array.\n         * @param initialValue If initialValue is specified, it is used as the initial value to start the accumulation. The first call to the callbackfn function provides this value as an argument instead of an array value.\n         */\n        ArraySchema.prototype.reduce = function (callbackfn, initialValue) {\n            return Array.prototype.reduce.apply(Array.from(this.$items.values()), arguments);\n        };\n        /**\n         * Calls the specified callback function for all the elements in an array, in descending order. The return value of the callback function is the accumulated result, and is provided as an argument in the next call to the callback function.\n         * @param callbackfn A function that accepts up to four arguments. The reduceRight method calls the callbackfn function one time for each element in the array.\n         * @param initialValue If initialValue is specified, it is used as the initial value to start the accumulation. The first call to the callbackfn function provides this value as an argument instead of an array value.\n         */\n        ArraySchema.prototype.reduceRight = function (callbackfn, initialValue) {\n            return Array.prototype.reduceRight.apply(Array.from(this.$items.values()), arguments);\n        };\n        /**\n         * Returns the value of the first element in the array where predicate is true, and undefined\n         * otherwise.\n         * @param predicate find calls predicate once for each element of the array, in ascending\n         * order, until it finds one where predicate returns true. If such an element is found, find\n         * immediately returns that element value. Otherwise, find returns undefined.\n         * @param thisArg If provided, it will be used as the this value for each invocation of\n         * predicate. If it is not provided, undefined is used instead.\n         */\n        ArraySchema.prototype.find = function (predicate, thisArg) {\n            return Array.from(this.$items.values()).find(predicate, thisArg);\n        };\n        /**\n         * Returns the index of the first element in the array where predicate is true, and -1\n         * otherwise.\n         * @param predicate find calls predicate once for each element of the array, in ascending\n         * order, until it finds one where predicate returns true. If such an element is found,\n         * findIndex immediately returns that element index. Otherwise, findIndex returns -1.\n         * @param thisArg If provided, it will be used as the this value for each invocation of\n         * predicate. If it is not provided, undefined is used instead.\n         */\n        ArraySchema.prototype.findIndex = function (predicate, thisArg) {\n            return Array.from(this.$items.values()).findIndex(predicate, thisArg);\n        };\n        /**\n         * Returns the this object after filling the section identified by start and end with value\n         * @param value value to fill array section with\n         * @param start index to start filling the array at. If start is negative, it is treated as\n         * length+start where length is the length of the array.\n         * @param end index to stop filling the array at. If end is negative, it is treated as\n         * length+end.\n         */\n        ArraySchema.prototype.fill = function (value, start, end) {\n            //\n            // TODO\n            //\n            throw new Error(\"ArraySchema#fill() not implemented\");\n        };\n        /**\n         * Returns the this object after copying a section of the array identified by start and end\n         * to the same array starting at position target\n         * @param target If target is negative, it is treated as length+target where length is the\n         * length of the array.\n         * @param start If start is negative, it is treated as length+start. If end is negative, it\n         * is treated as length+end.\n         * @param end If not specified, length of the this object is used as its default value.\n         */\n        ArraySchema.prototype.copyWithin = function (target, start, end) {\n            //\n            // TODO\n            //\n            throw new Error(\"ArraySchema#copyWithin() not implemented\");\n        };\n        /**\n         * Returns a string representation of an array.\n         */\n        ArraySchema.prototype.toString = function () { return this.$items.toString(); };\n        /**\n         * Returns a string representation of an array. The elements are converted to string using their toLocalString methods.\n         */\n        ArraySchema.prototype.toLocaleString = function () { return this.$items.toLocaleString(); };\n        /** Iterator */\n        ArraySchema.prototype[Symbol.iterator] = function () {\n            return Array.from(this.$items.values())[Symbol.iterator]();\n        };\n        Object.defineProperty(ArraySchema, Symbol.species, {\n            get: function () {\n                return ArraySchema;\n            },\n            enumerable: false,\n            configurable: true\n        });\n        /**\n         * Returns an iterable of key, value pairs for every entry in the array\n         */\n        ArraySchema.prototype.entries = function () { return this.$items.entries(); };\n        /**\n         * Returns an iterable of keys in the array\n         */\n        ArraySchema.prototype.keys = function () { return this.$items.keys(); };\n        /**\n         * Returns an iterable of values in the array\n         */\n        ArraySchema.prototype.values = function () { return this.$items.values(); };\n        /**\n         * Determines whether an array includes a certain element, returning true or false as appropriate.\n         * @param searchElement The element to search for.\n         * @param fromIndex The position in this array at which to begin searching for searchElement.\n         */\n        ArraySchema.prototype.includes = function (searchElement, fromIndex) {\n            return Array.from(this.$items.values()).includes(searchElement, fromIndex);\n        };\n        //\n        // ES2022\n        //\n        /**\n         * Calls a defined callback function on each element of an array. Then, flattens the result into\n         * a new array.\n         * This is identical to a map followed by flat with depth 1.\n         *\n         * @param callback A function that accepts up to three arguments. The flatMap method calls the\n         * callback function one time for each element in the array.\n         * @param thisArg An object to which the this keyword can refer in the callback function. If\n         * thisArg is omitted, undefined is used as the this value.\n         */\n        // @ts-ignore\n        ArraySchema.prototype.flatMap = function (callback, thisArg) {\n            // @ts-ignore\n            throw new Error(\"ArraySchema#flatMap() is not supported.\");\n        };\n        /**\n         * Returns a new array with all sub-array elements concatenated into it recursively up to the\n         * specified depth.\n         *\n         * @param depth The maximum recursion depth\n         */\n        // @ts-ignore\n        ArraySchema.prototype.flat = function (depth) {\n            throw new Error(\"ArraySchema#flat() is not supported.\");\n        };\n        ArraySchema.prototype.findLast = function () {\n            var arr = Array.from(this.$items.values());\n            // @ts-ignore\n            return arr.findLast.apply(arr, arguments);\n        };\n        ArraySchema.prototype.findLastIndex = function () {\n            var arr = Array.from(this.$items.values());\n            // @ts-ignore\n            return arr.findLastIndex.apply(arr, arguments);\n        };\n        //\n        // ES2023\n        //\n        ArraySchema.prototype.with = function (index, value) {\n            var copy = Array.from(this.$items.values());\n            copy[index] = value;\n            return new (ArraySchema.bind.apply(ArraySchema, __spreadArray([void 0], copy, false)))();\n        };\n        ArraySchema.prototype.toReversed = function () {\n            return Array.from(this.$items.values()).reverse();\n        };\n        ArraySchema.prototype.toSorted = function (compareFn) {\n            return Array.from(this.$items.values()).sort(compareFn);\n        };\n        // @ts-ignore\n        ArraySchema.prototype.toSpliced = function (start, deleteCount) {\n            var copy = Array.from(this.$items.values());\n            // @ts-ignore\n            return copy.toSpliced.apply(copy, arguments);\n        };\n        ArraySchema.prototype.setIndex = function (index, key) {\n            this.$indexes.set(index, key);\n        };\n        ArraySchema.prototype.getIndex = function (index) {\n            return this.$indexes.get(index);\n        };\n        ArraySchema.prototype.getByIndex = function (index) {\n            return this.$items.get(this.$indexes.get(index));\n        };\n        ArraySchema.prototype.deleteByIndex = function (index) {\n            var key = this.$indexes.get(index);\n            this.$items.delete(key);\n            this.$indexes.delete(index);\n        };\n        ArraySchema.prototype.toArray = function () {\n            return Array.from(this.$items.values());\n        };\n        ArraySchema.prototype.toJSON = function () {\n            return this.toArray().map(function (value) {\n                return (typeof (value['toJSON']) === \"function\")\n                    ? value['toJSON']()\n                    : value;\n            });\n        };\n        //\n        // Decoding utilities\n        //\n        ArraySchema.prototype.clone = function (isDecoding) {\n            var cloned;\n            if (isDecoding) {\n                cloned = new (ArraySchema.bind.apply(ArraySchema, __spreadArray([void 0], Array.from(this.$items.values()), false)))();\n            }\n            else {\n                cloned = new (ArraySchema.bind.apply(ArraySchema, __spreadArray([void 0], this.map(function (item) { return ((item['$changes'])\n                    ? item.clone()\n                    : item); }), false)))();\n            }\n            return cloned;\n        };\n        return ArraySchema;\n    }());\n\n    function getMapProxy(value) {\n        value['$proxy'] = true;\n        value = new Proxy(value, {\n            get: function (obj, prop) {\n                if (typeof (prop) !== \"symbol\" && // accessing properties\n                    typeof (obj[prop]) === \"undefined\") {\n                    return obj.get(prop);\n                }\n                else {\n                    return obj[prop];\n                }\n            },\n            set: function (obj, prop, setValue) {\n                if (typeof (prop) !== \"symbol\" &&\n                    (prop.indexOf(\"$\") === -1 &&\n                        prop !== \"onAdd\" &&\n                        prop !== \"onRemove\" &&\n                        prop !== \"onChange\")) {\n                    obj.set(prop, setValue);\n                }\n                else {\n                    obj[prop] = setValue;\n                }\n                return true;\n            },\n            deleteProperty: function (obj, prop) {\n                obj.delete(prop);\n                return true;\n            },\n        });\n        return value;\n    }\n    var MapSchema = /** @class */ (function () {\n        function MapSchema(initialValues) {\n            var _this = this;\n            this.$changes = new ChangeTree(this);\n            this.$items = new Map();\n            this.$indexes = new Map();\n            this.$refId = 0;\n            if (initialValues) {\n                if (initialValues instanceof Map ||\n                    initialValues instanceof MapSchema) {\n                    initialValues.forEach(function (v, k) { return _this.set(k, v); });\n                }\n                else {\n                    for (var k in initialValues) {\n                        this.set(k, initialValues[k]);\n                    }\n                }\n            }\n        }\n        MapSchema.prototype.onAdd = function (callback, triggerAll) {\n            if (triggerAll === void 0) { triggerAll = true; }\n            return addCallback((this.$callbacks || (this.$callbacks = {})), exports.OPERATION.ADD, callback, (triggerAll)\n                ? this.$items\n                : undefined);\n        };\n        MapSchema.prototype.onRemove = function (callback) { return addCallback(this.$callbacks || (this.$callbacks = {}), exports.OPERATION.DELETE, callback); };\n        MapSchema.prototype.onChange = function (callback) { return addCallback(this.$callbacks || (this.$callbacks = {}), exports.OPERATION.REPLACE, callback); };\n        MapSchema.is = function (type) {\n            return type['map'] !== undefined;\n        };\n        /** Iterator */\n        MapSchema.prototype[Symbol.iterator] = function () { return this.$items[Symbol.iterator](); };\n        Object.defineProperty(MapSchema.prototype, Symbol.toStringTag, {\n            get: function () { return this.$items[Symbol.toStringTag]; },\n            enumerable: false,\n            configurable: true\n        });\n        Object.defineProperty(MapSchema, Symbol.species, {\n            get: function () {\n                return MapSchema;\n            },\n            enumerable: false,\n            configurable: true\n        });\n        MapSchema.prototype.set = function (key, value) {\n            if (value === undefined || value === null) {\n                throw new Error(\"MapSchema#set('\".concat(key, \"', \").concat(value, \"): trying to set \").concat(value, \" value on '\").concat(key, \"'.\"));\n            }\n            // Force \"key\" as string\n            // See: https://github.com/colyseus/colyseus/issues/561#issuecomment-1646733468\n            key = key.toString();\n            // get \"index\" for this value.\n            var hasIndex = typeof (this.$changes.indexes[key]) !== \"undefined\";\n            var index = (hasIndex)\n                ? this.$changes.indexes[key]\n                : this.$refId++;\n            var operation = (hasIndex)\n                ? exports.OPERATION.REPLACE\n                : exports.OPERATION.ADD;\n            var isRef = (value['$changes']) !== undefined;\n            if (isRef) {\n                value['$changes'].setParent(this, this.$changes.root, index);\n            }\n            //\n            // (encoding)\n            // set a unique id to relate directly with this key/value.\n            //\n            if (!hasIndex) {\n                this.$changes.indexes[key] = index;\n                this.$indexes.set(index, key);\n            }\n            else if (!isRef &&\n                this.$items.get(key) === value) {\n                // if value is the same, avoid re-encoding it.\n                return;\n            }\n            else if (isRef && // if is schema, force ADD operation if value differ from previous one.\n                this.$items.get(key) !== value) {\n                operation = exports.OPERATION.ADD;\n            }\n            this.$items.set(key, value);\n            this.$changes.change(key, operation);\n            return this;\n        };\n        MapSchema.prototype.get = function (key) {\n            return this.$items.get(key);\n        };\n        MapSchema.prototype.delete = function (key) {\n            //\n            // TODO: add a \"purge\" method after .encode() runs, to cleanup removed `$indexes`\n            //\n            // We don't remove $indexes to allow setting the same key in the same patch\n            // (See \"should allow to remove and set an item in the same place\" test)\n            //\n            // // const index = this.$changes.indexes[key];\n            // // this.$indexes.delete(index);\n            this.$changes.delete(key.toString());\n            return this.$items.delete(key);\n        };\n        MapSchema.prototype.clear = function (changes) {\n            // discard previous operations.\n            this.$changes.discard(true, true);\n            this.$changes.indexes = {};\n            // clear previous indexes\n            this.$indexes.clear();\n            //\n            // When decoding:\n            // - enqueue items for DELETE callback.\n            // - flag child items for garbage collection.\n            //\n            if (changes) {\n                removeChildRefs.call(this, changes);\n            }\n            // clear items\n            this.$items.clear();\n            this.$changes.operation({ index: 0, op: exports.OPERATION.CLEAR });\n            // touch all structures until reach root\n            this.$changes.touchParents();\n        };\n        MapSchema.prototype.has = function (key) {\n            return this.$items.has(key);\n        };\n        MapSchema.prototype.forEach = function (callbackfn) {\n            this.$items.forEach(callbackfn);\n        };\n        MapSchema.prototype.entries = function () {\n            return this.$items.entries();\n        };\n        MapSchema.prototype.keys = function () {\n            return this.$items.keys();\n        };\n        MapSchema.prototype.values = function () {\n            return this.$items.values();\n        };\n        Object.defineProperty(MapSchema.prototype, \"size\", {\n            get: function () {\n                return this.$items.size;\n            },\n            enumerable: false,\n            configurable: true\n        });\n        MapSchema.prototype.setIndex = function (index, key) {\n            this.$indexes.set(index, key);\n        };\n        MapSchema.prototype.getIndex = function (index) {\n            return this.$indexes.get(index);\n        };\n        MapSchema.prototype.getByIndex = function (index) {\n            return this.$items.get(this.$indexes.get(index));\n        };\n        MapSchema.prototype.deleteByIndex = function (index) {\n            var key = this.$indexes.get(index);\n            this.$items.delete(key);\n            this.$indexes.delete(index);\n        };\n        MapSchema.prototype.toJSON = function () {\n            var map = {};\n            this.forEach(function (value, key) {\n                map[key] = (typeof (value['toJSON']) === \"function\")\n                    ? value['toJSON']()\n                    : value;\n            });\n            return map;\n        };\n        //\n        // Decoding utilities\n        //\n        MapSchema.prototype.clone = function (isDecoding) {\n            var cloned;\n            if (isDecoding) {\n                // client-side\n                cloned = Object.assign(new MapSchema(), this);\n            }\n            else {\n                // server-side\n                cloned = new MapSchema();\n                this.forEach(function (value, key) {\n                    if (value['$changes']) {\n                        cloned.set(key, value['clone']());\n                    }\n                    else {\n                        cloned.set(key, value);\n                    }\n                });\n            }\n            return cloned;\n        };\n        return MapSchema;\n    }());\n\n    var registeredTypes = {};\n    function registerType(identifier, definition) {\n        registeredTypes[identifier] = definition;\n    }\n    function getType(identifier) {\n        return registeredTypes[identifier];\n    }\n\n    var SchemaDefinition = /** @class */ (function () {\n        function SchemaDefinition() {\n            //\n            // TODO: use a \"field\" structure combining all these properties per-field.\n            //\n            this.indexes = {};\n            this.fieldsByIndex = {};\n            this.deprecated = {};\n            this.descriptors = {};\n        }\n        SchemaDefinition.create = function (parent) {\n            var definition = new SchemaDefinition();\n            // support inheritance\n            definition.schema = Object.assign({}, parent && parent.schema || {});\n            definition.indexes = Object.assign({}, parent && parent.indexes || {});\n            definition.fieldsByIndex = Object.assign({}, parent && parent.fieldsByIndex || {});\n            definition.descriptors = Object.assign({}, parent && parent.descriptors || {});\n            definition.deprecated = Object.assign({}, parent && parent.deprecated || {});\n            return definition;\n        };\n        SchemaDefinition.prototype.addField = function (field, type) {\n            var index = this.getNextFieldIndex();\n            this.fieldsByIndex[index] = field;\n            this.indexes[field] = index;\n            this.schema[field] = (Array.isArray(type))\n                ? { array: type[0] }\n                : type;\n        };\n        SchemaDefinition.prototype.hasField = function (field) {\n            return this.indexes[field] !== undefined;\n        };\n        SchemaDefinition.prototype.addFilter = function (field, cb) {\n            if (!this.filters) {\n                this.filters = {};\n                this.indexesWithFilters = [];\n            }\n            this.filters[this.indexes[field]] = cb;\n            this.indexesWithFilters.push(this.indexes[field]);\n            return true;\n        };\n        SchemaDefinition.prototype.addChildrenFilter = function (field, cb) {\n            var index = this.indexes[field];\n            var type = this.schema[field];\n            if (getType(Object.keys(type)[0])) {\n                if (!this.childFilters) {\n                    this.childFilters = {};\n                }\n                this.childFilters[index] = cb;\n                return true;\n            }\n            else {\n                console.warn(\"@filterChildren: field '\".concat(field, \"' can't have children. Ignoring filter.\"));\n            }\n        };\n        SchemaDefinition.prototype.getChildrenFilter = function (field) {\n            return this.childFilters && this.childFilters[this.indexes[field]];\n        };\n        SchemaDefinition.prototype.getNextFieldIndex = function () {\n            return Object.keys(this.schema || {}).length;\n        };\n        return SchemaDefinition;\n    }());\n    function hasFilter(klass) {\n        return klass._context && klass._context.useFilters;\n    }\n    var Context = /** @class */ (function () {\n        function Context() {\n            this.types = {};\n            this.schemas = new Map();\n            this.useFilters = false;\n        }\n        Context.prototype.has = function (schema) {\n            return this.schemas.has(schema);\n        };\n        Context.prototype.get = function (typeid) {\n            return this.types[typeid];\n        };\n        Context.prototype.add = function (schema, typeid) {\n            if (typeid === void 0) { typeid = this.schemas.size; }\n            // FIXME: move this to somewhere else?\n            // support inheritance\n            schema._definition = SchemaDefinition.create(schema._definition);\n            schema._typeid = typeid;\n            this.types[typeid] = schema;\n            this.schemas.set(schema, typeid);\n        };\n        Context.create = function (options) {\n            if (options === void 0) { options = {}; }\n            return function (definition) {\n                if (!options.context) {\n                    options.context = new Context();\n                }\n                return type(definition, options);\n            };\n        };\n        return Context;\n    }());\n    var globalContext = new Context();\n    /**\n     * [See documentation](https://docs.colyseus.io/state/schema/)\n     *\n     * Annotate a Schema property to be serializeable.\n     * \\@type()'d fields are automatically flagged as \"dirty\" for the next patch.\n     *\n     * @example Standard usage, with automatic change tracking.\n     * ```\n     * \\@type(\"string\") propertyName: string;\n     * ```\n     *\n     * @example You can provide the \"manual\" option if you'd like to manually control your patches via .setDirty().\n     * ```\n     * \\@type(\"string\", { manual: true })\n     * ```\n     */\n    function type(type, options) {\n        if (options === void 0) { options = {}; }\n        return function (target, field) {\n            var context = options.context || globalContext;\n            var constructor = target.constructor;\n            constructor._context = context;\n            if (!type) {\n                throw new Error(\"\".concat(constructor.name, \": @type() reference provided for \\\"\").concat(field, \"\\\" is undefined. Make sure you don't have any circular dependencies.\"));\n            }\n            /*\n             * static schema\n             */\n            if (!context.has(constructor)) {\n                context.add(constructor);\n            }\n            var definition = constructor._definition;\n            definition.addField(field, type);\n            /**\n             * skip if descriptor already exists for this field (`@deprecated()`)\n             */\n            if (definition.descriptors[field]) {\n                if (definition.deprecated[field]) {\n                    // do not create accessors for deprecated properties.\n                    return;\n                }\n                else {\n                    // trying to define same property multiple times across inheritance.\n                    // https://github.com/colyseus/colyseus-unity3d/issues/131#issuecomment-814308572\n                    try {\n                        throw new Error(\"@colyseus/schema: Duplicate '\".concat(field, \"' definition on '\").concat(constructor.name, \"'.\\nCheck @type() annotation\"));\n                    }\n                    catch (e) {\n                        var definitionAtLine = e.stack.split(\"\\n\")[4].trim();\n                        throw new Error(\"\".concat(e.message, \" \").concat(definitionAtLine));\n                    }\n                }\n            }\n            var isArray = ArraySchema.is(type);\n            var isMap = !isArray && MapSchema.is(type);\n            // TODO: refactor me.\n            // Allow abstract intermediary classes with no fields to be serialized\n            // (See \"should support an inheritance with a Schema type without fields\" test)\n            if (typeof (type) !== \"string\" && !Schema.is(type)) {\n                var childType = Object.values(type)[0];\n                if (typeof (childType) !== \"string\" && !context.has(childType)) {\n                    context.add(childType);\n                }\n            }\n            if (options.manual) {\n                // do not declare getter/setter descriptor\n                definition.descriptors[field] = {\n                    enumerable: true,\n                    configurable: true,\n                    writable: true,\n                };\n                return;\n            }\n            var fieldCached = \"_\".concat(field);\n            definition.descriptors[fieldCached] = {\n                enumerable: false,\n                configurable: false,\n                writable: true,\n            };\n            definition.descriptors[field] = {\n                get: function () {\n                    return this[fieldCached];\n                },\n                set: function (value) {\n                    /**\n                     * Create Proxy for array or map items\n                     */\n                    // skip if value is the same as cached.\n                    if (value === this[fieldCached]) {\n                        return;\n                    }\n                    if (value !== undefined &&\n                        value !== null) {\n                        // automaticallty transform Array into ArraySchema\n                        if (isArray && !(value instanceof ArraySchema)) {\n                            value = new (ArraySchema.bind.apply(ArraySchema, __spreadArray([void 0], value, false)))();\n                        }\n                        // automaticallty transform Map into MapSchema\n                        if (isMap && !(value instanceof MapSchema)) {\n                            value = new MapSchema(value);\n                        }\n                        // try to turn provided structure into a Proxy\n                        if (value['$proxy'] === undefined) {\n                            if (isMap) {\n                                value = getMapProxy(value);\n                            }\n                            else if (isArray) {\n                                value = getArrayProxy(value);\n                            }\n                        }\n                        // flag the change for encoding.\n                        this.$changes.change(field);\n                        //\n                        // call setParent() recursively for this and its child\n                        // structures.\n                        //\n                        if (value['$changes']) {\n                            value['$changes'].setParent(this, this.$changes.root, this._definition.indexes[field]);\n                        }\n                    }\n                    else if (this[fieldCached] !== undefined) {\n                        //\n                        // Setting a field to `null` or `undefined` will delete it.\n                        //\n                        this.$changes.delete(field);\n                    }\n                    this[fieldCached] = value;\n                },\n                enumerable: true,\n                configurable: true\n            };\n        };\n    }\n    /**\n     * `@filter()` decorator for defining data filters per client\n     */\n    function filter(cb) {\n        return function (target, field) {\n            var constructor = target.constructor;\n            var definition = constructor._definition;\n            if (definition.addFilter(field, cb)) {\n                constructor._context.useFilters = true;\n            }\n        };\n    }\n    function filterChildren(cb) {\n        return function (target, field) {\n            var constructor = target.constructor;\n            var definition = constructor._definition;\n            if (definition.addChildrenFilter(field, cb)) {\n                constructor._context.useFilters = true;\n            }\n        };\n    }\n    /**\n     * `@deprecated()` flag a field as deprecated.\n     * The previous `@type()` annotation should remain along with this one.\n     */\n    function deprecated(throws) {\n        if (throws === void 0) { throws = true; }\n        return function (target, field) {\n            var constructor = target.constructor;\n            var definition = constructor._definition;\n            definition.deprecated[field] = true;\n            if (throws) {\n                definition.descriptors[field] = {\n                    get: function () { throw new Error(\"\".concat(field, \" is deprecated.\")); },\n                    set: function (value) { },\n                    enumerable: false,\n                    configurable: true\n                };\n            }\n        };\n    }\n    function defineTypes(target, fields, options) {\n        if (options === void 0) { options = {}; }\n        if (!options.context) {\n            options.context = target._context || options.context || globalContext;\n        }\n        for (var field in fields) {\n            type(fields[field], options)(target.prototype, field);\n        }\n        return target;\n    }\n\n    /**\n     * Copyright (c) 2018 Endel Dreyer\n     * Copyright (c) 2014 Ion Drive Software Ltd.\n     *\n     * Permission is hereby granted, free of charge, to any person obtaining a copy\n     * of this software and associated documentation files (the \"Software\"), to deal\n     * in the Software without restriction, including without limitation the rights\n     * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n     * copies of the Software, and to permit persons to whom the Software is\n     * furnished to do so, subject to the following conditions:\n     *\n     * The above copyright notice and this permission notice shall be included in all\n     * copies or substantial portions of the Software.\n     *\n     * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n     * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n     * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n     * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n     * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n     * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n     * SOFTWARE\n     */\n    /**\n     * msgpack implementation highly based on notepack.io\n     * https://github.com/darrachequesne/notepack\n     */\n    function utf8Length(str) {\n        var c = 0, length = 0;\n        for (var i = 0, l = str.length; i < l; i++) {\n            c = str.charCodeAt(i);\n            if (c < 0x80) {\n                length += 1;\n            }\n            else if (c < 0x800) {\n                length += 2;\n            }\n            else if (c < 0xd800 || c >= 0xe000) {\n                length += 3;\n            }\n            else {\n                i++;\n                length += 4;\n            }\n        }\n        return length;\n    }\n    function utf8Write(view, offset, str) {\n        var c = 0;\n        for (var i = 0, l = str.length; i < l; i++) {\n            c = str.charCodeAt(i);\n            if (c < 0x80) {\n                view[offset++] = c;\n            }\n            else if (c < 0x800) {\n                view[offset++] = 0xc0 | (c >> 6);\n                view[offset++] = 0x80 | (c & 0x3f);\n            }\n            else if (c < 0xd800 || c >= 0xe000) {\n                view[offset++] = 0xe0 | (c >> 12);\n                view[offset++] = 0x80 | (c >> 6 & 0x3f);\n                view[offset++] = 0x80 | (c & 0x3f);\n            }\n            else {\n                i++;\n                c = 0x10000 + (((c & 0x3ff) << 10) | (str.charCodeAt(i) & 0x3ff));\n                view[offset++] = 0xf0 | (c >> 18);\n                view[offset++] = 0x80 | (c >> 12 & 0x3f);\n                view[offset++] = 0x80 | (c >> 6 & 0x3f);\n                view[offset++] = 0x80 | (c & 0x3f);\n            }\n        }\n    }\n    function int8$1(bytes, value) {\n        bytes.push(value & 255);\n    }\n    function uint8$1(bytes, value) {\n        bytes.push(value & 255);\n    }\n    function int16$1(bytes, value) {\n        bytes.push(value & 255);\n        bytes.push((value >> 8) & 255);\n    }\n    function uint16$1(bytes, value) {\n        bytes.push(value & 255);\n        bytes.push((value >> 8) & 255);\n    }\n    function int32$1(bytes, value) {\n        bytes.push(value & 255);\n        bytes.push((value >> 8) & 255);\n        bytes.push((value >> 16) & 255);\n        bytes.push((value >> 24) & 255);\n    }\n    function uint32$1(bytes, value) {\n        var b4 = value >> 24;\n        var b3 = value >> 16;\n        var b2 = value >> 8;\n        var b1 = value;\n        bytes.push(b1 & 255);\n        bytes.push(b2 & 255);\n        bytes.push(b3 & 255);\n        bytes.push(b4 & 255);\n    }\n    function int64$1(bytes, value) {\n        var high = Math.floor(value / Math.pow(2, 32));\n        var low = value >>> 0;\n        uint32$1(bytes, low);\n        uint32$1(bytes, high);\n    }\n    function uint64$1(bytes, value) {\n        var high = (value / Math.pow(2, 32)) >> 0;\n        var low = value >>> 0;\n        uint32$1(bytes, low);\n        uint32$1(bytes, high);\n    }\n    function float32$1(bytes, value) {\n        writeFloat32(bytes, value);\n    }\n    function float64$1(bytes, value) {\n        writeFloat64(bytes, value);\n    }\n    var _int32$1 = new Int32Array(2);\n    var _float32$1 = new Float32Array(_int32$1.buffer);\n    var _float64$1 = new Float64Array(_int32$1.buffer);\n    function writeFloat32(bytes, value) {\n        _float32$1[0] = value;\n        int32$1(bytes, _int32$1[0]);\n    }\n    function writeFloat64(bytes, value) {\n        _float64$1[0] = value;\n        int32$1(bytes, _int32$1[0 ]);\n        int32$1(bytes, _int32$1[1 ]);\n    }\n    function boolean$1(bytes, value) {\n        return uint8$1(bytes, value ? 1 : 0);\n    }\n    function string$1(bytes, value) {\n        // encode `null` strings as empty.\n        if (!value) {\n            value = \"\";\n        }\n        var length = utf8Length(value);\n        var size = 0;\n        // fixstr\n        if (length < 0x20) {\n            bytes.push(length | 0xa0);\n            size = 1;\n        }\n        // str 8\n        else if (length < 0x100) {\n            bytes.push(0xd9);\n            uint8$1(bytes, length);\n            size = 2;\n        }\n        // str 16\n        else if (length < 0x10000) {\n            bytes.push(0xda);\n            uint16$1(bytes, length);\n            size = 3;\n        }\n        // str 32\n        else if (length < 0x100000000) {\n            bytes.push(0xdb);\n            uint32$1(bytes, length);\n            size = 5;\n        }\n        else {\n            throw new Error('String too long');\n        }\n        utf8Write(bytes, bytes.length, value);\n        return size + length;\n    }\n    function number$1(bytes, value) {\n        if (isNaN(value)) {\n            return number$1(bytes, 0);\n        }\n        else if (!isFinite(value)) {\n            return number$1(bytes, (value > 0) ? Number.MAX_SAFE_INTEGER : -Number.MAX_SAFE_INTEGER);\n        }\n        else if (value !== (value | 0)) {\n            bytes.push(0xcb);\n            writeFloat64(bytes, value);\n            return 9;\n            // TODO: encode float 32?\n            // is it possible to differentiate between float32 / float64 here?\n            // // float 32\n            // bytes.push(0xca);\n            // writeFloat32(bytes, value);\n            // return 5;\n        }\n        if (value >= 0) {\n            // positive fixnum\n            if (value < 0x80) {\n                uint8$1(bytes, value);\n                return 1;\n            }\n            // uint 8\n            if (value < 0x100) {\n                bytes.push(0xcc);\n                uint8$1(bytes, value);\n                return 2;\n            }\n            // uint 16\n            if (value < 0x10000) {\n                bytes.push(0xcd);\n                uint16$1(bytes, value);\n                return 3;\n            }\n            // uint 32\n            if (value < 0x100000000) {\n                bytes.push(0xce);\n                uint32$1(bytes, value);\n                return 5;\n            }\n            // uint 64\n            bytes.push(0xcf);\n            uint64$1(bytes, value);\n            return 9;\n        }\n        else {\n            // negative fixnum\n            if (value >= -32) {\n                bytes.push(0xe0 | (value + 0x20));\n                return 1;\n            }\n            // int 8\n            if (value >= -128) {\n                bytes.push(0xd0);\n                int8$1(bytes, value);\n                return 2;\n            }\n            // int 16\n            if (value >= -32768) {\n                bytes.push(0xd1);\n                int16$1(bytes, value);\n                return 3;\n            }\n            // int 32\n            if (value >= -2147483648) {\n                bytes.push(0xd2);\n                int32$1(bytes, value);\n                return 5;\n            }\n            // int 64\n            bytes.push(0xd3);\n            int64$1(bytes, value);\n            return 9;\n        }\n    }\n\n    var encode = /*#__PURE__*/Object.freeze({\n        __proto__: null,\n        boolean: boolean$1,\n        float32: float32$1,\n        float64: float64$1,\n        int16: int16$1,\n        int32: int32$1,\n        int64: int64$1,\n        int8: int8$1,\n        number: number$1,\n        string: string$1,\n        uint16: uint16$1,\n        uint32: uint32$1,\n        uint64: uint64$1,\n        uint8: uint8$1,\n        utf8Write: utf8Write,\n        writeFloat32: writeFloat32,\n        writeFloat64: writeFloat64\n    });\n\n    /**\n     * Copyright (c) 2018 Endel Dreyer\n     * Copyright (c) 2014 Ion Drive Software Ltd.\n     *\n     * Permission is hereby granted, free of charge, to any person obtaining a copy\n     * of this software and associated documentation files (the \"Software\"), to deal\n     * in the Software without restriction, including without limitation the rights\n     * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n     * copies of the Software, and to permit persons to whom the Software is\n     * furnished to do so, subject to the following conditions:\n     *\n     * The above copyright notice and this permission notice shall be included in all\n     * copies or substantial portions of the Software.\n     *\n     * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n     * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n     * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n     * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n     * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n     * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n     * SOFTWARE\n     */\n    function utf8Read(bytes, offset, length) {\n        var string = '', chr = 0;\n        for (var i = offset, end = offset + length; i < end; i++) {\n            var byte = bytes[i];\n            if ((byte & 0x80) === 0x00) {\n                string += String.fromCharCode(byte);\n                continue;\n            }\n            if ((byte & 0xe0) === 0xc0) {\n                string += String.fromCharCode(((byte & 0x1f) << 6) |\n                    (bytes[++i] & 0x3f));\n                continue;\n            }\n            if ((byte & 0xf0) === 0xe0) {\n                string += String.fromCharCode(((byte & 0x0f) << 12) |\n                    ((bytes[++i] & 0x3f) << 6) |\n                    ((bytes[++i] & 0x3f) << 0));\n                continue;\n            }\n            if ((byte & 0xf8) === 0xf0) {\n                chr = ((byte & 0x07) << 18) |\n                    ((bytes[++i] & 0x3f) << 12) |\n                    ((bytes[++i] & 0x3f) << 6) |\n                    ((bytes[++i] & 0x3f) << 0);\n                if (chr >= 0x010000) { // surrogate pair\n                    chr -= 0x010000;\n                    string += String.fromCharCode((chr >>> 10) + 0xD800, (chr & 0x3FF) + 0xDC00);\n                }\n                else {\n                    string += String.fromCharCode(chr);\n                }\n                continue;\n            }\n            console.error('Invalid byte ' + byte.toString(16));\n            // (do not throw error to avoid server/client from crashing due to hack attemps)\n            // throw new Error('Invalid byte ' + byte.toString(16));\n        }\n        return string;\n    }\n    function int8(bytes, it) {\n        return uint8(bytes, it) << 24 >> 24;\n    }\n    function uint8(bytes, it) {\n        return bytes[it.offset++];\n    }\n    function int16(bytes, it) {\n        return uint16(bytes, it) << 16 >> 16;\n    }\n    function uint16(bytes, it) {\n        return bytes[it.offset++] | bytes[it.offset++] << 8;\n    }\n    function int32(bytes, it) {\n        return bytes[it.offset++] | bytes[it.offset++] << 8 | bytes[it.offset++] << 16 | bytes[it.offset++] << 24;\n    }\n    function uint32(bytes, it) {\n        return int32(bytes, it) >>> 0;\n    }\n    function float32(bytes, it) {\n        return readFloat32(bytes, it);\n    }\n    function float64(bytes, it) {\n        return readFloat64(bytes, it);\n    }\n    function int64(bytes, it) {\n        var low = uint32(bytes, it);\n        var high = int32(bytes, it) * Math.pow(2, 32);\n        return high + low;\n    }\n    function uint64(bytes, it) {\n        var low = uint32(bytes, it);\n        var high = uint32(bytes, it) * Math.pow(2, 32);\n        return high + low;\n    }\n    var _int32 = new Int32Array(2);\n    var _float32 = new Float32Array(_int32.buffer);\n    var _float64 = new Float64Array(_int32.buffer);\n    function readFloat32(bytes, it) {\n        _int32[0] = int32(bytes, it);\n        return _float32[0];\n    }\n    function readFloat64(bytes, it) {\n        _int32[0 ] = int32(bytes, it);\n        _int32[1 ] = int32(bytes, it);\n        return _float64[0];\n    }\n    function boolean(bytes, it) {\n        return uint8(bytes, it) > 0;\n    }\n    function string(bytes, it) {\n        var prefix = bytes[it.offset++];\n        var length;\n        if (prefix < 0xc0) {\n            // fixstr\n            length = prefix & 0x1f;\n        }\n        else if (prefix === 0xd9) {\n            length = uint8(bytes, it);\n        }\n        else if (prefix === 0xda) {\n            length = uint16(bytes, it);\n        }\n        else if (prefix === 0xdb) {\n            length = uint32(bytes, it);\n        }\n        var value = utf8Read(bytes, it.offset, length);\n        it.offset += length;\n        return value;\n    }\n    function stringCheck(bytes, it) {\n        var prefix = bytes[it.offset];\n        return (\n        // fixstr\n        (prefix < 0xc0 && prefix > 0xa0) ||\n            // str 8\n            prefix === 0xd9 ||\n            // str 16\n            prefix === 0xda ||\n            // str 32\n            prefix === 0xdb);\n    }\n    function number(bytes, it) {\n        var prefix = bytes[it.offset++];\n        if (prefix < 0x80) {\n            // positive fixint\n            return prefix;\n        }\n        else if (prefix === 0xca) {\n            // float 32\n            return readFloat32(bytes, it);\n        }\n        else if (prefix === 0xcb) {\n            // float 64\n            return readFloat64(bytes, it);\n        }\n        else if (prefix === 0xcc) {\n            // uint 8\n            return uint8(bytes, it);\n        }\n        else if (prefix === 0xcd) {\n            // uint 16\n            return uint16(bytes, it);\n        }\n        else if (prefix === 0xce) {\n            // uint 32\n            return uint32(bytes, it);\n        }\n        else if (prefix === 0xcf) {\n            // uint 64\n            return uint64(bytes, it);\n        }\n        else if (prefix === 0xd0) {\n            // int 8\n            return int8(bytes, it);\n        }\n        else if (prefix === 0xd1) {\n            // int 16\n            return int16(bytes, it);\n        }\n        else if (prefix === 0xd2) {\n            // int 32\n            return int32(bytes, it);\n        }\n        else if (prefix === 0xd3) {\n            // int 64\n            return int64(bytes, it);\n        }\n        else if (prefix > 0xdf) {\n            // negative fixint\n            return (0xff - prefix + 1) * -1;\n        }\n    }\n    function numberCheck(bytes, it) {\n        var prefix = bytes[it.offset];\n        // positive fixint - 0x00 - 0x7f\n        // float 32        - 0xca\n        // float 64        - 0xcb\n        // uint 8          - 0xcc\n        // uint 16         - 0xcd\n        // uint 32         - 0xce\n        // uint 64         - 0xcf\n        // int 8           - 0xd0\n        // int 16          - 0xd1\n        // int 32          - 0xd2\n        // int 64          - 0xd3\n        return (prefix < 0x80 ||\n            (prefix >= 0xca && prefix <= 0xd3));\n    }\n    function arrayCheck(bytes, it) {\n        return bytes[it.offset] < 0xa0;\n        // const prefix = bytes[it.offset] ;\n        // if (prefix < 0xa0) {\n        //   return prefix;\n        // // array\n        // } else if (prefix === 0xdc) {\n        //   it.offset += 2;\n        // } else if (0xdd) {\n        //   it.offset += 4;\n        // }\n        // return prefix;\n    }\n    function switchStructureCheck(bytes, it) {\n        return (\n        // previous byte should be `SWITCH_TO_STRUCTURE`\n        bytes[it.offset - 1] === SWITCH_TO_STRUCTURE &&\n            // next byte should be a number\n            (bytes[it.offset] < 0x80 || (bytes[it.offset] >= 0xca && bytes[it.offset] <= 0xd3)));\n    }\n\n    var decode = /*#__PURE__*/Object.freeze({\n        __proto__: null,\n        arrayCheck: arrayCheck,\n        boolean: boolean,\n        float32: float32,\n        float64: float64,\n        int16: int16,\n        int32: int32,\n        int64: int64,\n        int8: int8,\n        number: number,\n        numberCheck: numberCheck,\n        readFloat32: readFloat32,\n        readFloat64: readFloat64,\n        string: string,\n        stringCheck: stringCheck,\n        switchStructureCheck: switchStructureCheck,\n        uint16: uint16,\n        uint32: uint32,\n        uint64: uint64,\n        uint8: uint8\n    });\n\n    var CollectionSchema = /** @class */ (function () {\n        function CollectionSchema(initialValues) {\n            var _this = this;\n            this.$changes = new ChangeTree(this);\n            this.$items = new Map();\n            this.$indexes = new Map();\n            this.$refId = 0;\n            if (initialValues) {\n                initialValues.forEach(function (v) { return _this.add(v); });\n            }\n        }\n        CollectionSchema.prototype.onAdd = function (callback, triggerAll) {\n            if (triggerAll === void 0) { triggerAll = true; }\n            return addCallback((this.$callbacks || (this.$callbacks = [])), exports.OPERATION.ADD, callback, (triggerAll)\n                ? this.$items\n                : undefined);\n        };\n        CollectionSchema.prototype.onRemove = function (callback) { return addCallback(this.$callbacks || (this.$callbacks = []), exports.OPERATION.DELETE, callback); };\n        CollectionSchema.prototype.onChange = function (callback) { return addCallback(this.$callbacks || (this.$callbacks = []), exports.OPERATION.REPLACE, callback); };\n        CollectionSchema.is = function (type) {\n            return type['collection'] !== undefined;\n        };\n        CollectionSchema.prototype.add = function (value) {\n            // set \"index\" for reference.\n            var index = this.$refId++;\n            var isRef = (value['$changes']) !== undefined;\n            if (isRef) {\n                value['$changes'].setParent(this, this.$changes.root, index);\n            }\n            this.$changes.indexes[index] = index;\n            this.$indexes.set(index, index);\n            this.$items.set(index, value);\n            this.$changes.change(index);\n            return index;\n        };\n        CollectionSchema.prototype.at = function (index) {\n            var key = Array.from(this.$items.keys())[index];\n            return this.$items.get(key);\n        };\n        CollectionSchema.prototype.entries = function () {\n            return this.$items.entries();\n        };\n        CollectionSchema.prototype.delete = function (item) {\n            var entries = this.$items.entries();\n            var index;\n            var entry;\n            while (entry = entries.next()) {\n                if (entry.done) {\n                    break;\n                }\n                if (item === entry.value[1]) {\n                    index = entry.value[0];\n                    break;\n                }\n            }\n            if (index === undefined) {\n                return false;\n            }\n            this.$changes.delete(index);\n            this.$indexes.delete(index);\n            return this.$items.delete(index);\n        };\n        CollectionSchema.prototype.clear = function (changes) {\n            // discard previous operations.\n            this.$changes.discard(true, true);\n            this.$changes.indexes = {};\n            // clear previous indexes\n            this.$indexes.clear();\n            //\n            // When decoding:\n            // - enqueue items for DELETE callback.\n            // - flag child items for garbage collection.\n            //\n            if (changes) {\n                removeChildRefs.call(this, changes);\n            }\n            // clear items\n            this.$items.clear();\n            this.$changes.operation({ index: 0, op: exports.OPERATION.CLEAR });\n            // touch all structures until reach root\n            this.$changes.touchParents();\n        };\n        CollectionSchema.prototype.has = function (value) {\n            return Array.from(this.$items.values()).some(function (v) { return v === value; });\n        };\n        CollectionSchema.prototype.forEach = function (callbackfn) {\n            var _this = this;\n            this.$items.forEach(function (value, key, _) { return callbackfn(value, key, _this); });\n        };\n        CollectionSchema.prototype.values = function () {\n            return this.$items.values();\n        };\n        Object.defineProperty(CollectionSchema.prototype, \"size\", {\n            get: function () {\n                return this.$items.size;\n            },\n            enumerable: false,\n            configurable: true\n        });\n        CollectionSchema.prototype.setIndex = function (index, key) {\n            this.$indexes.set(index, key);\n        };\n        CollectionSchema.prototype.getIndex = function (index) {\n            return this.$indexes.get(index);\n        };\n        CollectionSchema.prototype.getByIndex = function (index) {\n            return this.$items.get(this.$indexes.get(index));\n        };\n        CollectionSchema.prototype.deleteByIndex = function (index) {\n            var key = this.$indexes.get(index);\n            this.$items.delete(key);\n            this.$indexes.delete(index);\n        };\n        CollectionSchema.prototype.toArray = function () {\n            return Array.from(this.$items.values());\n        };\n        CollectionSchema.prototype.toJSON = function () {\n            var values = [];\n            this.forEach(function (value, key) {\n                values.push((typeof (value['toJSON']) === \"function\")\n                    ? value['toJSON']()\n                    : value);\n            });\n            return values;\n        };\n        //\n        // Decoding utilities\n        //\n        CollectionSchema.prototype.clone = function (isDecoding) {\n            var cloned;\n            if (isDecoding) {\n                // client-side\n                cloned = Object.assign(new CollectionSchema(), this);\n            }\n            else {\n                // server-side\n                cloned = new CollectionSchema();\n                this.forEach(function (value) {\n                    if (value['$changes']) {\n                        cloned.add(value['clone']());\n                    }\n                    else {\n                        cloned.add(value);\n                    }\n                });\n            }\n            return cloned;\n        };\n        return CollectionSchema;\n    }());\n\n    var SetSchema = /** @class */ (function () {\n        function SetSchema(initialValues) {\n            var _this = this;\n            this.$changes = new ChangeTree(this);\n            this.$items = new Map();\n            this.$indexes = new Map();\n            this.$refId = 0;\n            if (initialValues) {\n                initialValues.forEach(function (v) { return _this.add(v); });\n            }\n        }\n        SetSchema.prototype.onAdd = function (callback, triggerAll) {\n            if (triggerAll === void 0) { triggerAll = true; }\n            return addCallback((this.$callbacks || (this.$callbacks = [])), exports.OPERATION.ADD, callback, (triggerAll)\n                ? this.$items\n                : undefined);\n        };\n        SetSchema.prototype.onRemove = function (callback) { return addCallback(this.$callbacks || (this.$callbacks = []), exports.OPERATION.DELETE, callback); };\n        SetSchema.prototype.onChange = function (callback) { return addCallback(this.$callbacks || (this.$callbacks = []), exports.OPERATION.REPLACE, callback); };\n        SetSchema.is = function (type) {\n            return type['set'] !== undefined;\n        };\n        SetSchema.prototype.add = function (value) {\n            var _a, _b;\n            // immediatelly return false if value already added.\n            if (this.has(value)) {\n                return false;\n            }\n            // set \"index\" for reference.\n            var index = this.$refId++;\n            if ((value['$changes']) !== undefined) {\n                value['$changes'].setParent(this, this.$changes.root, index);\n            }\n            var operation = (_b = (_a = this.$changes.indexes[index]) === null || _a === void 0 ? void 0 : _a.op) !== null && _b !== void 0 ? _b : exports.OPERATION.ADD;\n            this.$changes.indexes[index] = index;\n            this.$indexes.set(index, index);\n            this.$items.set(index, value);\n            this.$changes.change(index, operation);\n            return index;\n        };\n        SetSchema.prototype.entries = function () {\n            return this.$items.entries();\n        };\n        SetSchema.prototype.delete = function (item) {\n            var entries = this.$items.entries();\n            var index;\n            var entry;\n            while (entry = entries.next()) {\n                if (entry.done) {\n                    break;\n                }\n                if (item === entry.value[1]) {\n                    index = entry.value[0];\n                    break;\n                }\n            }\n            if (index === undefined) {\n                return false;\n            }\n            this.$changes.delete(index);\n            this.$indexes.delete(index);\n            return this.$items.delete(index);\n        };\n        SetSchema.prototype.clear = function (changes) {\n            // discard previous operations.\n            this.$changes.discard(true, true);\n            this.$changes.indexes = {};\n            // clear previous indexes\n            this.$indexes.clear();\n            //\n            // When decoding:\n            // - enqueue items for DELETE callback.\n            // - flag child items for garbage collection.\n            //\n            if (changes) {\n                removeChildRefs.call(this, changes);\n            }\n            // clear items\n            this.$items.clear();\n            this.$changes.operation({ index: 0, op: exports.OPERATION.CLEAR });\n            // touch all structures until reach root\n            this.$changes.touchParents();\n        };\n        SetSchema.prototype.has = function (value) {\n            var values = this.$items.values();\n            var has = false;\n            var entry;\n            while (entry = values.next()) {\n                if (entry.done) {\n                    break;\n                }\n                if (value === entry.value) {\n                    has = true;\n                    break;\n                }\n            }\n            return has;\n        };\n        SetSchema.prototype.forEach = function (callbackfn) {\n            var _this = this;\n            this.$items.forEach(function (value, key, _) { return callbackfn(value, key, _this); });\n        };\n        SetSchema.prototype.values = function () {\n            return this.$items.values();\n        };\n        Object.defineProperty(SetSchema.prototype, \"size\", {\n            get: function () {\n                return this.$items.size;\n            },\n            enumerable: false,\n            configurable: true\n        });\n        SetSchema.prototype.setIndex = function (index, key) {\n            this.$indexes.set(index, key);\n        };\n        SetSchema.prototype.getIndex = function (index) {\n            return this.$indexes.get(index);\n        };\n        SetSchema.prototype.getByIndex = function (index) {\n            return this.$items.get(this.$indexes.get(index));\n        };\n        SetSchema.prototype.deleteByIndex = function (index) {\n            var key = this.$indexes.get(index);\n            this.$items.delete(key);\n            this.$indexes.delete(index);\n        };\n        SetSchema.prototype.toArray = function () {\n            return Array.from(this.$items.values());\n        };\n        SetSchema.prototype.toJSON = function () {\n            var values = [];\n            this.forEach(function (value, key) {\n                values.push((typeof (value['toJSON']) === \"function\")\n                    ? value['toJSON']()\n                    : value);\n            });\n            return values;\n        };\n        //\n        // Decoding utilities\n        //\n        SetSchema.prototype.clone = function (isDecoding) {\n            var cloned;\n            if (isDecoding) {\n                // client-side\n                cloned = Object.assign(new SetSchema(), this);\n            }\n            else {\n                // server-side\n                cloned = new SetSchema();\n                this.forEach(function (value) {\n                    if (value['$changes']) {\n                        cloned.add(value['clone']());\n                    }\n                    else {\n                        cloned.add(value);\n                    }\n                });\n            }\n            return cloned;\n        };\n        return SetSchema;\n    }());\n\n    var ClientState = /** @class */ (function () {\n        function ClientState() {\n            this.refIds = new WeakSet();\n            this.containerIndexes = new WeakMap();\n        }\n        // containerIndexes = new Map<ChangeTree, Set<number>>();\n        ClientState.prototype.addRefId = function (changeTree) {\n            if (!this.refIds.has(changeTree)) {\n                this.refIds.add(changeTree);\n                this.containerIndexes.set(changeTree, new Set());\n            }\n        };\n        ClientState.get = function (client) {\n            if (client.$filterState === undefined) {\n                client.$filterState = new ClientState();\n            }\n            return client.$filterState;\n        };\n        return ClientState;\n    }());\n\n    var ReferenceTracker = /** @class */ (function () {\n        function ReferenceTracker() {\n            //\n            // Relation of refId => Schema structure\n            // For direct access of structures during decoding time.\n            //\n            this.refs = new Map();\n            this.refCounts = {};\n            this.deletedRefs = new Set();\n            this.nextUniqueId = 0;\n        }\n        ReferenceTracker.prototype.getNextUniqueId = function () {\n            return this.nextUniqueId++;\n        };\n        // for decoding\n        ReferenceTracker.prototype.addRef = function (refId, ref, incrementCount) {\n            if (incrementCount === void 0) { incrementCount = true; }\n            this.refs.set(refId, ref);\n            if (incrementCount) {\n                this.refCounts[refId] = (this.refCounts[refId] || 0) + 1;\n            }\n        };\n        // for decoding\n        ReferenceTracker.prototype.removeRef = function (refId) {\n            var refCount = this.refCounts[refId];\n            if (refCount === undefined) {\n                console.warn(\"trying to remove reference \".concat(refId, \" that doesn't exist\"));\n                return;\n            }\n            if (refCount === 0) {\n                console.warn(\"trying to remove reference \".concat(refId, \" with 0 refCount\"));\n                return;\n            }\n            this.refCounts[refId] = refCount - 1;\n            this.deletedRefs.add(refId);\n        };\n        ReferenceTracker.prototype.clearRefs = function () {\n            this.refs.clear();\n            this.deletedRefs.clear();\n            this.refCounts = {};\n        };\n        // for decoding\n        ReferenceTracker.prototype.garbageCollectDeletedRefs = function () {\n            var _this = this;\n            this.deletedRefs.forEach(function (refId) {\n                //\n                // Skip active references.\n                //\n                if (_this.refCounts[refId] > 0) {\n                    return;\n                }\n                var ref = _this.refs.get(refId);\n                //\n                // Ensure child schema instances have their references removed as well.\n                //\n                if (ref instanceof Schema) {\n                    for (var fieldName in ref['_definition'].schema) {\n                        if (typeof (ref['_definition'].schema[fieldName]) !== \"string\" &&\n                            ref[fieldName] &&\n                            ref[fieldName]['$changes']) {\n                            _this.removeRef(ref[fieldName]['$changes'].refId);\n                        }\n                    }\n                }\n                else {\n                    var definition = ref['$changes'].parent._definition;\n                    var type = definition.schema[definition.fieldsByIndex[ref['$changes'].parentIndex]];\n                    if (typeof (Object.values(type)[0]) === \"function\") {\n                        Array.from(ref.values())\n                            .forEach(function (child) { return _this.removeRef(child['$changes'].refId); });\n                    }\n                }\n                _this.refs.delete(refId);\n                delete _this.refCounts[refId];\n            });\n            // clear deleted refs.\n            this.deletedRefs.clear();\n        };\n        return ReferenceTracker;\n    }());\n\n    var EncodeSchemaError = /** @class */ (function (_super) {\n        __extends(EncodeSchemaError, _super);\n        function EncodeSchemaError() {\n            return _super !== null && _super.apply(this, arguments) || this;\n        }\n        return EncodeSchemaError;\n    }(Error));\n    function assertType(value, type, klass, field) {\n        var typeofTarget;\n        var allowNull = false;\n        switch (type) {\n            case \"number\":\n            case \"int8\":\n            case \"uint8\":\n            case \"int16\":\n            case \"uint16\":\n            case \"int32\":\n            case \"uint32\":\n            case \"int64\":\n            case \"uint64\":\n            case \"float32\":\n            case \"float64\":\n                typeofTarget = \"number\";\n                if (isNaN(value)) {\n                    console.log(\"trying to encode \\\"NaN\\\" in \".concat(klass.constructor.name, \"#\").concat(field));\n                }\n                break;\n            case \"string\":\n                typeofTarget = \"string\";\n                allowNull = true;\n                break;\n            case \"boolean\":\n                // boolean is always encoded as true/false based on truthiness\n                return;\n        }\n        if (typeof (value) !== typeofTarget && (!allowNull || (allowNull && value !== null))) {\n            var foundValue = \"'\".concat(JSON.stringify(value), \"'\").concat((value && value.constructor && \" (\".concat(value.constructor.name, \")\")) || '');\n            throw new EncodeSchemaError(\"a '\".concat(typeofTarget, \"' was expected, but \").concat(foundValue, \" was provided in \").concat(klass.constructor.name, \"#\").concat(field));\n        }\n    }\n    function assertInstanceType(value, type, klass, field) {\n        if (!(value instanceof type)) {\n            throw new EncodeSchemaError(\"a '\".concat(type.name, \"' was expected, but '\").concat(value.constructor.name, \"' was provided in \").concat(klass.constructor.name, \"#\").concat(field));\n        }\n    }\n    function encodePrimitiveType(type, bytes, value, klass, field) {\n        assertType(value, type, klass, field);\n        var encodeFunc = encode[type];\n        if (encodeFunc) {\n            encodeFunc(bytes, value);\n        }\n        else {\n            throw new EncodeSchemaError(\"a '\".concat(type, \"' was expected, but \").concat(value, \" was provided in \").concat(klass.constructor.name, \"#\").concat(field));\n        }\n    }\n    function decodePrimitiveType(type, bytes, it) {\n        return decode[type](bytes, it);\n    }\n    /**\n     * Schema encoder / decoder\n     */\n    var Schema = /** @class */ (function () {\n        // allow inherited classes to have a constructor\n        function Schema() {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            // fix enumerability of fields for end-user\n            Object.defineProperties(this, {\n                $changes: {\n                    value: new ChangeTree(this, undefined, new ReferenceTracker()),\n                    enumerable: false,\n                    writable: true\n                },\n                // $listeners: {\n                //     value: undefined,\n                //     enumerable: false,\n                //     writable: true\n                // },\n                $callbacks: {\n                    value: undefined,\n                    enumerable: false,\n                    writable: true\n                },\n            });\n            var descriptors = this._definition.descriptors;\n            if (descriptors) {\n                Object.defineProperties(this, descriptors);\n            }\n            //\n            // Assign initial values\n            //\n            if (args[0]) {\n                this.assign(args[0]);\n            }\n        }\n        Schema.onError = function (e) {\n            console.error(e);\n        };\n        Schema.is = function (type) {\n            return (type['_definition'] &&\n                type['_definition'].schema !== undefined);\n        };\n        Schema.prototype.onChange = function (callback) {\n            return addCallback((this.$callbacks || (this.$callbacks = {})), exports.OPERATION.REPLACE, callback);\n        };\n        Schema.prototype.onRemove = function (callback) {\n            return addCallback((this.$callbacks || (this.$callbacks = {})), exports.OPERATION.DELETE, callback);\n        };\n        Schema.prototype.assign = function (props) {\n            Object.assign(this, props);\n            return this;\n        };\n        Object.defineProperty(Schema.prototype, \"_definition\", {\n            get: function () { return this.constructor._definition; },\n            enumerable: false,\n            configurable: true\n        });\n        /**\n         * (Server-side): Flag a property to be encoded for the next patch.\n         * @param instance Schema instance\n         * @param property string representing the property name, or number representing the index of the property.\n         * @param operation OPERATION to perform (detected automatically)\n         */\n        Schema.prototype.setDirty = function (property, operation) {\n            this.$changes.change(property, operation);\n        };\n        /**\n         * Client-side: listen for changes on property.\n         * @param prop the property name\n         * @param callback callback to be triggered on property change\n         * @param immediate trigger immediatelly if property has been already set.\n         */\n        Schema.prototype.listen = function (prop, callback, immediate) {\n            var _this = this;\n            if (immediate === void 0) { immediate = true; }\n            if (!this.$callbacks) {\n                this.$callbacks = {};\n            }\n            if (!this.$callbacks[prop]) {\n                this.$callbacks[prop] = [];\n            }\n            this.$callbacks[prop].push(callback);\n            if (immediate && this[prop] !== undefined) {\n                callback(this[prop], undefined);\n            }\n            // return un-register callback.\n            return function () { return spliceOne(_this.$callbacks[prop], _this.$callbacks[prop].indexOf(callback)); };\n        };\n        Schema.prototype.decode = function (bytes, it, ref) {\n            if (it === void 0) { it = { offset: 0 }; }\n            if (ref === void 0) { ref = this; }\n            var allChanges = [];\n            var $root = this.$changes.root;\n            var totalBytes = bytes.length;\n            var refId = 0;\n            $root.refs.set(refId, this);\n            while (it.offset < totalBytes) {\n                var byte = bytes[it.offset++];\n                if (byte == SWITCH_TO_STRUCTURE) {\n                    refId = number(bytes, it);\n                    var nextRef = $root.refs.get(refId);\n                    //\n                    // Trying to access a reference that haven't been decoded yet.\n                    //\n                    if (!nextRef) {\n                        throw new Error(\"\\\"refId\\\" not found: \".concat(refId));\n                    }\n                    ref = nextRef;\n                    continue;\n                }\n                var changeTree = ref['$changes'];\n                var isSchema = (ref['_definition'] !== undefined);\n                var operation = (isSchema)\n                    ? (byte >> 6) << 6 // \"compressed\" index + operation\n                    : byte; // \"uncompressed\" index + operation (array/map items)\n                if (operation === exports.OPERATION.CLEAR) {\n                    //\n                    // TODO: refactor me!\n                    // The `.clear()` method is calling `$root.removeRef(refId)` for\n                    // each item inside this collection\n                    //\n                    ref.clear(allChanges);\n                    continue;\n                }\n                var fieldIndex = (isSchema)\n                    ? byte % (operation || 255) // if \"REPLACE\" operation (0), use 255\n                    : number(bytes, it);\n                var fieldName = (isSchema)\n                    ? (ref['_definition'].fieldsByIndex[fieldIndex])\n                    : \"\";\n                var type = changeTree.getType(fieldIndex);\n                var value = void 0;\n                var previousValue = void 0;\n                var dynamicIndex = void 0;\n                if (!isSchema) {\n                    previousValue = ref['getByIndex'](fieldIndex);\n                    if ((operation & exports.OPERATION.ADD) === exports.OPERATION.ADD) { // ADD or DELETE_AND_ADD\n                        dynamicIndex = (ref instanceof MapSchema)\n                            ? string(bytes, it)\n                            : fieldIndex;\n                        ref['setIndex'](fieldIndex, dynamicIndex);\n                    }\n                    else {\n                        // here\n                        dynamicIndex = ref['getIndex'](fieldIndex);\n                    }\n                }\n                else {\n                    previousValue = ref[\"_\".concat(fieldName)];\n                }\n                //\n                // Delete operations\n                //\n                if ((operation & exports.OPERATION.DELETE) === exports.OPERATION.DELETE) {\n                    if (operation !== exports.OPERATION.DELETE_AND_ADD) {\n                        ref['deleteByIndex'](fieldIndex);\n                    }\n                    // Flag `refId` for garbage collection.\n                    if (previousValue && previousValue['$changes']) {\n                        $root.removeRef(previousValue['$changes'].refId);\n                    }\n                    value = null;\n                }\n                if (fieldName === undefined) {\n                    console.warn(\"@colyseus/schema: definition mismatch\");\n                    //\n                    // keep skipping next bytes until reaches a known structure\n                    // by local decoder.\n                    //\n                    var nextIterator = { offset: it.offset };\n                    while (it.offset < totalBytes) {\n                        if (switchStructureCheck(bytes, it)) {\n                            nextIterator.offset = it.offset + 1;\n                            if ($root.refs.has(number(bytes, nextIterator))) {\n                                break;\n                            }\n                        }\n                        it.offset++;\n                    }\n                    continue;\n                }\n                else if (operation === exports.OPERATION.DELETE) ;\n                else if (Schema.is(type)) {\n                    var refId_1 = number(bytes, it);\n                    value = $root.refs.get(refId_1);\n                    if (operation !== exports.OPERATION.REPLACE) {\n                        var childType = this.getSchemaType(bytes, it, type);\n                        if (!value) {\n                            value = this.createTypeInstance(childType);\n                            value.$changes.refId = refId_1;\n                            if (previousValue) {\n                                value.$callbacks = previousValue.$callbacks;\n                                // value.$listeners = previousValue.$listeners;\n                                if (previousValue['$changes'].refId &&\n                                    refId_1 !== previousValue['$changes'].refId) {\n                                    $root.removeRef(previousValue['$changes'].refId);\n                                }\n                            }\n                        }\n                        $root.addRef(refId_1, value, (value !== previousValue));\n                    }\n                }\n                else if (typeof (type) === \"string\") {\n                    //\n                    // primitive value (number, string, boolean, etc)\n                    //\n                    value = decodePrimitiveType(type, bytes, it);\n                }\n                else {\n                    var typeDef = getType(Object.keys(type)[0]);\n                    var refId_2 = number(bytes, it);\n                    var valueRef = ($root.refs.has(refId_2))\n                        ? previousValue || $root.refs.get(refId_2)\n                        : new typeDef.constructor();\n                    value = valueRef.clone(true);\n                    value.$changes.refId = refId_2;\n                    // preserve schema callbacks\n                    if (previousValue) {\n                        value['$callbacks'] = previousValue['$callbacks'];\n                        if (previousValue['$changes'].refId &&\n                            refId_2 !== previousValue['$changes'].refId) {\n                            $root.removeRef(previousValue['$changes'].refId);\n                            //\n                            // Trigger onRemove if structure has been replaced.\n                            //\n                            var entries = previousValue.entries();\n                            var iter = void 0;\n                            while ((iter = entries.next()) && !iter.done) {\n                                var _a = iter.value, key = _a[0], value_1 = _a[1];\n                                allChanges.push({\n                                    refId: refId_2,\n                                    op: exports.OPERATION.DELETE,\n                                    field: key,\n                                    value: undefined,\n                                    previousValue: value_1,\n                                });\n                            }\n                        }\n                    }\n                    $root.addRef(refId_2, value, (valueRef !== previousValue));\n                }\n                if (value !== null &&\n                    value !== undefined) {\n                    if (value['$changes']) {\n                        value['$changes'].setParent(changeTree.ref, changeTree.root, fieldIndex);\n                    }\n                    if (ref instanceof Schema) {\n                        ref[fieldName] = value;\n                        // ref[`_${fieldName}`] = value;\n                    }\n                    else if (ref instanceof MapSchema) {\n                        // const key = ref['$indexes'].get(field);\n                        var key = dynamicIndex;\n                        // ref.set(key, value);\n                        ref['$items'].set(key, value);\n                        ref['$changes'].allChanges.add(fieldIndex);\n                    }\n                    else if (ref instanceof ArraySchema) {\n                        // const key = ref['$indexes'][field];\n                        // console.log(\"SETTING FOR ArraySchema =>\", { field, key, value });\n                        // ref[key] = value;\n                        ref.setAt(fieldIndex, value);\n                    }\n                    else if (ref instanceof CollectionSchema) {\n                        var index = ref.add(value);\n                        ref['setIndex'](fieldIndex, index);\n                    }\n                    else if (ref instanceof SetSchema) {\n                        var index = ref.add(value);\n                        if (index !== false) {\n                            ref['setIndex'](fieldIndex, index);\n                        }\n                    }\n                }\n                if (previousValue !== value) {\n                    allChanges.push({\n                        refId: refId,\n                        op: operation,\n                        field: fieldName,\n                        dynamicIndex: dynamicIndex,\n                        value: value,\n                        previousValue: previousValue,\n                    });\n                }\n            }\n            this._triggerChanges(allChanges);\n            // drop references of unused schemas\n            $root.garbageCollectDeletedRefs();\n            return allChanges;\n        };\n        Schema.prototype.encode = function (encodeAll, bytes, useFilters) {\n            if (encodeAll === void 0) { encodeAll = false; }\n            if (bytes === void 0) { bytes = []; }\n            if (useFilters === void 0) { useFilters = false; }\n            var rootChangeTree = this.$changes;\n            var refIdsVisited = new WeakSet();\n            var changeTrees = [rootChangeTree];\n            var numChangeTrees = 1;\n            for (var i = 0; i < numChangeTrees; i++) {\n                var changeTree = changeTrees[i];\n                var ref = changeTree.ref;\n                var isSchema = (ref instanceof Schema);\n                // Generate unique refId for the ChangeTree.\n                changeTree.ensureRefId();\n                // mark this ChangeTree as visited.\n                refIdsVisited.add(changeTree);\n                // root `refId` is skipped.\n                if (changeTree !== rootChangeTree &&\n                    (changeTree.changed || encodeAll)) {\n                    uint8$1(bytes, SWITCH_TO_STRUCTURE);\n                    number$1(bytes, changeTree.refId);\n                }\n                var changes = (encodeAll)\n                    ? Array.from(changeTree.allChanges)\n                    : Array.from(changeTree.changes.values());\n                for (var j = 0, cl = changes.length; j < cl; j++) {\n                    var operation = (encodeAll)\n                        ? { op: exports.OPERATION.ADD, index: changes[j] }\n                        : changes[j];\n                    var fieldIndex = operation.index;\n                    var field = (isSchema)\n                        ? ref['_definition'].fieldsByIndex && ref['_definition'].fieldsByIndex[fieldIndex]\n                        : fieldIndex;\n                    // cache begin index if `useFilters`\n                    var beginIndex = bytes.length;\n                    // encode field index + operation\n                    if (operation.op !== exports.OPERATION.TOUCH) {\n                        if (isSchema) {\n                            //\n                            // Compress `fieldIndex` + `operation` into a single byte.\n                            // This adds a limitaion of 64 fields per Schema structure\n                            //\n                            uint8$1(bytes, (fieldIndex | operation.op));\n                        }\n                        else {\n                            uint8$1(bytes, operation.op);\n                            // custom operations\n                            if (operation.op === exports.OPERATION.CLEAR) {\n                                continue;\n                            }\n                            // indexed operations\n                            number$1(bytes, fieldIndex);\n                        }\n                    }\n                    //\n                    // encode \"alias\" for dynamic fields (maps)\n                    //\n                    if (!isSchema &&\n                        (operation.op & exports.OPERATION.ADD) == exports.OPERATION.ADD // ADD or DELETE_AND_ADD\n                    ) {\n                        if (ref instanceof MapSchema) {\n                            //\n                            // MapSchema dynamic key\n                            //\n                            var dynamicIndex = changeTree.ref['$indexes'].get(fieldIndex);\n                            string$1(bytes, dynamicIndex);\n                        }\n                    }\n                    if (operation.op === exports.OPERATION.DELETE) {\n                        //\n                        // TODO: delete from filter cache data.\n                        //\n                        // if (useFilters) {\n                        //     delete changeTree.caches[fieldIndex];\n                        // }\n                        continue;\n                    }\n                    // const type = changeTree.childType || ref._schema[field];\n                    var type = changeTree.getType(fieldIndex);\n                    // const type = changeTree.getType(fieldIndex);\n                    var value = changeTree.getValue(fieldIndex);\n                    // Enqueue ChangeTree to be visited\n                    if (value &&\n                        value['$changes'] &&\n                        !refIdsVisited.has(value['$changes'])) {\n                        changeTrees.push(value['$changes']);\n                        value['$changes'].ensureRefId();\n                        numChangeTrees++;\n                    }\n                    if (operation.op === exports.OPERATION.TOUCH) {\n                        continue;\n                    }\n                    if (Schema.is(type)) {\n                        assertInstanceType(value, type, ref, field);\n                        //\n                        // Encode refId for this instance.\n                        // The actual instance is going to be encoded on next `changeTree` iteration.\n                        //\n                        number$1(bytes, value.$changes.refId);\n                        // Try to encode inherited TYPE_ID if it's an ADD operation.\n                        if ((operation.op & exports.OPERATION.ADD) === exports.OPERATION.ADD) {\n                            this.tryEncodeTypeId(bytes, type, value.constructor);\n                        }\n                    }\n                    else if (typeof (type) === \"string\") {\n                        //\n                        // Primitive values\n                        //\n                        encodePrimitiveType(type, bytes, value, ref, field);\n                    }\n                    else {\n                        //\n                        // Custom type (MapSchema, ArraySchema, etc)\n                        //\n                        var definition = getType(Object.keys(type)[0]);\n                        //\n                        // ensure a ArraySchema has been provided\n                        //\n                        assertInstanceType(ref[\"_\".concat(field)], definition.constructor, ref, field);\n                        //\n                        // Encode refId for this instance.\n                        // The actual instance is going to be encoded on next `changeTree` iteration.\n                        //\n                        number$1(bytes, value.$changes.refId);\n                    }\n                    if (useFilters) {\n                        // cache begin / end index\n                        changeTree.cache(fieldIndex, bytes.slice(beginIndex));\n                    }\n                }\n                if (!encodeAll && !useFilters) {\n                    changeTree.discard();\n                }\n            }\n            return bytes;\n        };\n        Schema.prototype.encodeAll = function (useFilters) {\n            return this.encode(true, [], useFilters);\n        };\n        Schema.prototype.applyFilters = function (client, encodeAll) {\n            var _a, _b;\n            if (encodeAll === void 0) { encodeAll = false; }\n            var root = this;\n            var refIdsDissallowed = new Set();\n            var $filterState = ClientState.get(client);\n            var changeTrees = [this.$changes];\n            var numChangeTrees = 1;\n            var filteredBytes = [];\n            var _loop_1 = function (i) {\n                var changeTree = changeTrees[i];\n                if (refIdsDissallowed.has(changeTree.refId)) {\n                    return \"continue\";\n                }\n                var ref = changeTree.ref;\n                var isSchema = ref instanceof Schema;\n                uint8$1(filteredBytes, SWITCH_TO_STRUCTURE);\n                number$1(filteredBytes, changeTree.refId);\n                var clientHasRefId = $filterState.refIds.has(changeTree);\n                var isEncodeAll = (encodeAll || !clientHasRefId);\n                // console.log(\"REF:\", ref.constructor.name);\n                // console.log(\"Encode all?\", isEncodeAll);\n                //\n                // include `changeTree` on list of known refIds by this client.\n                //\n                $filterState.addRefId(changeTree);\n                var containerIndexes = $filterState.containerIndexes.get(changeTree);\n                var changes = (isEncodeAll)\n                    ? Array.from(changeTree.allChanges)\n                    : Array.from(changeTree.changes.values());\n                //\n                // WORKAROUND: tries to re-evaluate previously not included @filter() attributes\n                // - see \"DELETE a field of Schema\" test case.\n                //\n                if (!encodeAll &&\n                    isSchema &&\n                    ref._definition.indexesWithFilters) {\n                    var indexesWithFilters = ref._definition.indexesWithFilters;\n                    indexesWithFilters.forEach(function (indexWithFilter) {\n                        if (!containerIndexes.has(indexWithFilter) &&\n                            changeTree.allChanges.has(indexWithFilter)) {\n                            if (isEncodeAll) {\n                                changes.push(indexWithFilter);\n                            }\n                            else {\n                                changes.push({ op: exports.OPERATION.ADD, index: indexWithFilter, });\n                            }\n                        }\n                    });\n                }\n                for (var j = 0, cl = changes.length; j < cl; j++) {\n                    var change = (isEncodeAll)\n                        ? { op: exports.OPERATION.ADD, index: changes[j] }\n                        : changes[j];\n                    // custom operations\n                    if (change.op === exports.OPERATION.CLEAR) {\n                        uint8$1(filteredBytes, change.op);\n                        continue;\n                    }\n                    var fieldIndex = change.index;\n                    //\n                    // Deleting fields: encode the operation + field index\n                    //\n                    if (change.op === exports.OPERATION.DELETE) {\n                        //\n                        // DELETE operations also need to go through filtering.\n                        //\n                        // TODO: cache the previous value so we can access the value (primitive or `refId`)\n                        // (check against `$filterState.refIds`)\n                        //\n                        if (isSchema) {\n                            uint8$1(filteredBytes, change.op | fieldIndex);\n                        }\n                        else {\n                            uint8$1(filteredBytes, change.op);\n                            number$1(filteredBytes, fieldIndex);\n                        }\n                        continue;\n                    }\n                    // indexed operation\n                    var value = changeTree.getValue(fieldIndex);\n                    var type = changeTree.getType(fieldIndex);\n                    if (isSchema) {\n                        // Is a Schema!\n                        var filter = (ref._definition.filters &&\n                            ref._definition.filters[fieldIndex]);\n                        if (filter && !filter.call(ref, client, value, root)) {\n                            if (value && value['$changes']) {\n                                refIdsDissallowed.add(value['$changes'].refId);\n                            }\n                            continue;\n                        }\n                    }\n                    else {\n                        // Is a collection! (map, array, etc.)\n                        var parent = changeTree.parent;\n                        var filter = changeTree.getChildrenFilter();\n                        if (filter && !filter.call(parent, client, ref['$indexes'].get(fieldIndex), value, root)) {\n                            if (value && value['$changes']) {\n                                refIdsDissallowed.add(value['$changes'].refId);\n                            }\n                            continue;\n                        }\n                    }\n                    // visit child ChangeTree on further iteration.\n                    if (value['$changes']) {\n                        changeTrees.push(value['$changes']);\n                        numChangeTrees++;\n                    }\n                    //\n                    // Copy cached bytes\n                    //\n                    if (change.op !== exports.OPERATION.TOUCH) {\n                        //\n                        // TODO: refactor me!\n                        //\n                        if (change.op === exports.OPERATION.ADD || isSchema) {\n                            //\n                            // use cached bytes directly if is from Schema type.\n                            //\n                            filteredBytes.push.apply(filteredBytes, (_a = changeTree.caches[fieldIndex]) !== null && _a !== void 0 ? _a : []);\n                            containerIndexes.add(fieldIndex);\n                        }\n                        else {\n                            if (containerIndexes.has(fieldIndex)) {\n                                //\n                                // use cached bytes if already has the field\n                                //\n                                filteredBytes.push.apply(filteredBytes, (_b = changeTree.caches[fieldIndex]) !== null && _b !== void 0 ? _b : []);\n                            }\n                            else {\n                                //\n                                // force ADD operation if field is not known by this client.\n                                //\n                                containerIndexes.add(fieldIndex);\n                                uint8$1(filteredBytes, exports.OPERATION.ADD);\n                                number$1(filteredBytes, fieldIndex);\n                                if (ref instanceof MapSchema) {\n                                    //\n                                    // MapSchema dynamic key\n                                    //\n                                    var dynamicIndex = changeTree.ref['$indexes'].get(fieldIndex);\n                                    string$1(filteredBytes, dynamicIndex);\n                                }\n                                if (value['$changes']) {\n                                    number$1(filteredBytes, value['$changes'].refId);\n                                }\n                                else {\n                                    // \"encodePrimitiveType\" without type checking.\n                                    // the type checking has been done on the first .encode() call.\n                                    encode[type](filteredBytes, value);\n                                }\n                            }\n                        }\n                    }\n                    else if (value['$changes'] && !isSchema) {\n                        //\n                        // TODO:\n                        // - track ADD/REPLACE/DELETE instances on `$filterState`\n                        // - do NOT always encode dynamicIndex for MapSchema.\n                        //   (If client already has that key, only the first index is necessary.)\n                        //\n                        uint8$1(filteredBytes, exports.OPERATION.ADD);\n                        number$1(filteredBytes, fieldIndex);\n                        if (ref instanceof MapSchema) {\n                            //\n                            // MapSchema dynamic key\n                            //\n                            var dynamicIndex = changeTree.ref['$indexes'].get(fieldIndex);\n                            string$1(filteredBytes, dynamicIndex);\n                        }\n                        number$1(filteredBytes, value['$changes'].refId);\n                    }\n                }\n            };\n            for (var i = 0; i < numChangeTrees; i++) {\n                _loop_1(i);\n            }\n            return filteredBytes;\n        };\n        Schema.prototype.clone = function () {\n            var _a;\n            var cloned = new (this.constructor);\n            var schema = this._definition.schema;\n            for (var field in schema) {\n                if (typeof (this[field]) === \"object\" &&\n                    typeof ((_a = this[field]) === null || _a === void 0 ? void 0 : _a.clone) === \"function\") {\n                    // deep clone\n                    cloned[field] = this[field].clone();\n                }\n                else {\n                    // primitive values\n                    cloned[field] = this[field];\n                }\n            }\n            return cloned;\n        };\n        Schema.prototype.toJSON = function () {\n            var schema = this._definition.schema;\n            var deprecated = this._definition.deprecated;\n            var obj = {};\n            for (var field in schema) {\n                if (!deprecated[field] && this[field] !== null && typeof (this[field]) !== \"undefined\") {\n                    obj[field] = (typeof (this[field]['toJSON']) === \"function\")\n                        ? this[field]['toJSON']()\n                        : this[\"_\".concat(field)];\n                }\n            }\n            return obj;\n        };\n        Schema.prototype.discardAllChanges = function () {\n            this.$changes.discardAll();\n        };\n        Schema.prototype.getByIndex = function (index) {\n            return this[this._definition.fieldsByIndex[index]];\n        };\n        Schema.prototype.deleteByIndex = function (index) {\n            this[this._definition.fieldsByIndex[index]] = undefined;\n        };\n        Schema.prototype.tryEncodeTypeId = function (bytes, type, targetType) {\n            if (type._typeid !== targetType._typeid) {\n                uint8$1(bytes, TYPE_ID);\n                number$1(bytes, targetType._typeid);\n            }\n        };\n        Schema.prototype.getSchemaType = function (bytes, it, defaultType) {\n            var type;\n            if (bytes[it.offset] === TYPE_ID) {\n                it.offset++;\n                type = this.constructor._context.get(number(bytes, it));\n            }\n            return type || defaultType;\n        };\n        Schema.prototype.createTypeInstance = function (type) {\n            var instance = new type();\n            // assign root on $changes\n            instance.$changes.root = this.$changes.root;\n            return instance;\n        };\n        Schema.prototype._triggerChanges = function (changes) {\n            var _a, _b, _c, _d, _e, _f, _g, _h, _j;\n            var uniqueRefIds = new Set();\n            var $refs = this.$changes.root.refs;\n            var _loop_2 = function (i) {\n                var change = changes[i];\n                var refId = change.refId;\n                var ref = $refs.get(refId);\n                var $callbacks = ref['$callbacks'];\n                //\n                // trigger onRemove on child structure.\n                //\n                if ((change.op & exports.OPERATION.DELETE) === exports.OPERATION.DELETE &&\n                    change.previousValue instanceof Schema) {\n                    (_b = (_a = change.previousValue['$callbacks']) === null || _a === void 0 ? void 0 : _a[exports.OPERATION.DELETE]) === null || _b === void 0 ? void 0 : _b.forEach(function (callback) { return callback(); });\n                }\n                // no callbacks defined, skip this structure!\n                if (!$callbacks) {\n                    return \"continue\";\n                }\n                if (ref instanceof Schema) {\n                    if (!uniqueRefIds.has(refId)) {\n                        try {\n                            // trigger onChange\n                            (_c = $callbacks === null || $callbacks === void 0 ? void 0 : $callbacks[exports.OPERATION.REPLACE]) === null || _c === void 0 ? void 0 : _c.forEach(function (callback) {\n                                return callback();\n                            });\n                        }\n                        catch (e) {\n                            Schema.onError(e);\n                        }\n                    }\n                    try {\n                        if ($callbacks.hasOwnProperty(change.field)) {\n                            (_d = $callbacks[change.field]) === null || _d === void 0 ? void 0 : _d.forEach(function (callback) {\n                                return callback(change.value, change.previousValue);\n                            });\n                        }\n                    }\n                    catch (e) {\n                        Schema.onError(e);\n                    }\n                }\n                else {\n                    // is a collection of items\n                    if (change.op === exports.OPERATION.ADD && change.previousValue === undefined) {\n                        // triger onAdd\n                        (_e = $callbacks[exports.OPERATION.ADD]) === null || _e === void 0 ? void 0 : _e.forEach(function (callback) { var _a; return callback(change.value, (_a = change.dynamicIndex) !== null && _a !== void 0 ? _a : change.field); });\n                    }\n                    else if (change.op === exports.OPERATION.DELETE) {\n                        //\n                        // FIXME: `previousValue` should always be available.\n                        // ADD + DELETE operations are still encoding DELETE operation.\n                        //\n                        if (change.previousValue !== undefined) {\n                            // triger onRemove\n                            (_f = $callbacks[exports.OPERATION.DELETE]) === null || _f === void 0 ? void 0 : _f.forEach(function (callback) { var _a; return callback(change.previousValue, (_a = change.dynamicIndex) !== null && _a !== void 0 ? _a : change.field); });\n                        }\n                    }\n                    else if (change.op === exports.OPERATION.DELETE_AND_ADD) {\n                        // triger onRemove\n                        if (change.previousValue !== undefined) {\n                            (_g = $callbacks[exports.OPERATION.DELETE]) === null || _g === void 0 ? void 0 : _g.forEach(function (callback) { var _a; return callback(change.previousValue, (_a = change.dynamicIndex) !== null && _a !== void 0 ? _a : change.field); });\n                        }\n                        // triger onAdd\n                        (_h = $callbacks[exports.OPERATION.ADD]) === null || _h === void 0 ? void 0 : _h.forEach(function (callback) { var _a; return callback(change.value, (_a = change.dynamicIndex) !== null && _a !== void 0 ? _a : change.field); });\n                    }\n                    // trigger onChange\n                    if (change.value !== change.previousValue) {\n                        (_j = $callbacks[exports.OPERATION.REPLACE]) === null || _j === void 0 ? void 0 : _j.forEach(function (callback) { var _a; return callback(change.value, (_a = change.dynamicIndex) !== null && _a !== void 0 ? _a : change.field); });\n                    }\n                }\n                uniqueRefIds.add(refId);\n            };\n            for (var i = 0; i < changes.length; i++) {\n                _loop_2(i);\n            }\n        };\n        Schema._definition = SchemaDefinition.create();\n        return Schema;\n    }());\n\n    function dumpChanges(schema) {\n        var changeTrees = [schema['$changes']];\n        var numChangeTrees = 1;\n        var dump = {};\n        var currentStructure = dump;\n        var _loop_1 = function (i) {\n            var changeTree = changeTrees[i];\n            changeTree.changes.forEach(function (change) {\n                var ref = changeTree.ref;\n                var fieldIndex = change.index;\n                var field = (ref['_definition'])\n                    ? ref['_definition'].fieldsByIndex[fieldIndex]\n                    : ref['$indexes'].get(fieldIndex);\n                currentStructure[field] = changeTree.getValue(fieldIndex);\n            });\n        };\n        for (var i = 0; i < numChangeTrees; i++) {\n            _loop_1(i);\n        }\n        return dump;\n    }\n\n    var reflectionContext = { context: new Context() };\n    /**\n     * Reflection\n     */\n    var ReflectionField = /** @class */ (function (_super) {\n        __extends(ReflectionField, _super);\n        function ReflectionField() {\n            return _super !== null && _super.apply(this, arguments) || this;\n        }\n        __decorate([\n            type(\"string\", reflectionContext)\n        ], ReflectionField.prototype, \"name\", void 0);\n        __decorate([\n            type(\"string\", reflectionContext)\n        ], ReflectionField.prototype, \"type\", void 0);\n        __decorate([\n            type(\"number\", reflectionContext)\n        ], ReflectionField.prototype, \"referencedType\", void 0);\n        return ReflectionField;\n    }(Schema));\n    var ReflectionType = /** @class */ (function (_super) {\n        __extends(ReflectionType, _super);\n        function ReflectionType() {\n            var _this = _super !== null && _super.apply(this, arguments) || this;\n            _this.fields = new ArraySchema();\n            return _this;\n        }\n        __decorate([\n            type(\"number\", reflectionContext)\n        ], ReflectionType.prototype, \"id\", void 0);\n        __decorate([\n            type([ReflectionField], reflectionContext)\n        ], ReflectionType.prototype, \"fields\", void 0);\n        return ReflectionType;\n    }(Schema));\n    var Reflection = /** @class */ (function (_super) {\n        __extends(Reflection, _super);\n        function Reflection() {\n            var _this = _super !== null && _super.apply(this, arguments) || this;\n            _this.types = new ArraySchema();\n            return _this;\n        }\n        Reflection.encode = function (instance) {\n            var _a;\n            var rootSchemaType = instance.constructor;\n            var reflection = new Reflection();\n            reflection.rootType = rootSchemaType._typeid;\n            var buildType = function (currentType, schema) {\n                for (var fieldName in schema) {\n                    var field = new ReflectionField();\n                    field.name = fieldName;\n                    var fieldType = void 0;\n                    if (typeof (schema[fieldName]) === \"string\") {\n                        fieldType = schema[fieldName];\n                    }\n                    else {\n                        var type_1 = schema[fieldName];\n                        var childTypeSchema = void 0;\n                        //\n                        // TODO: refactor below.\n                        //\n                        if (Schema.is(type_1)) {\n                            fieldType = \"ref\";\n                            childTypeSchema = schema[fieldName];\n                        }\n                        else {\n                            fieldType = Object.keys(type_1)[0];\n                            if (typeof (type_1[fieldType]) === \"string\") {\n                                fieldType += \":\" + type_1[fieldType]; // array:string\n                            }\n                            else {\n                                childTypeSchema = type_1[fieldType];\n                            }\n                        }\n                        field.referencedType = (childTypeSchema)\n                            ? childTypeSchema._typeid\n                            : -1;\n                    }\n                    field.type = fieldType;\n                    currentType.fields.push(field);\n                }\n                reflection.types.push(currentType);\n            };\n            var types = (_a = rootSchemaType._context) === null || _a === void 0 ? void 0 : _a.types;\n            for (var typeid in types) {\n                var type_2 = new ReflectionType();\n                type_2.id = Number(typeid);\n                buildType(type_2, types[typeid]._definition.schema);\n            }\n            return reflection.encodeAll();\n        };\n        Reflection.decode = function (bytes, it) {\n            var context = new Context();\n            var reflection = new Reflection();\n            reflection.decode(bytes, it);\n            var schemaTypes = reflection.types.reduce(function (types, reflectionType) {\n                var schema = /** @class */ (function (_super) {\n                    __extends(_, _super);\n                    function _() {\n                        return _super !== null && _super.apply(this, arguments) || this;\n                    }\n                    return _;\n                }(Schema));\n                var typeid = reflectionType.id;\n                types[typeid] = schema;\n                context.add(schema, typeid);\n                return types;\n            }, {});\n            reflection.types.forEach(function (reflectionType) {\n                var schemaType = schemaTypes[reflectionType.id];\n                reflectionType.fields.forEach(function (field) {\n                    var _a;\n                    if (field.referencedType !== undefined) {\n                        var fieldType = field.type;\n                        var refType = schemaTypes[field.referencedType];\n                        // map or array of primitive type (-1)\n                        if (!refType) {\n                            var typeInfo = field.type.split(\":\");\n                            fieldType = typeInfo[0];\n                            refType = typeInfo[1];\n                        }\n                        if (fieldType === \"ref\") {\n                            type(refType, { context: context })(schemaType.prototype, field.name);\n                        }\n                        else {\n                            type((_a = {}, _a[fieldType] = refType, _a), { context: context })(schemaType.prototype, field.name);\n                        }\n                    }\n                    else {\n                        type(field.type, { context: context })(schemaType.prototype, field.name);\n                    }\n                });\n            });\n            var rootType = schemaTypes[reflection.rootType];\n            var rootInstance = new rootType();\n            /**\n             * auto-initialize referenced types on root type\n             * to allow registering listeners immediatelly on client-side\n             */\n            for (var fieldName in rootType._definition.schema) {\n                var fieldType = rootType._definition.schema[fieldName];\n                if (typeof (fieldType) !== \"string\") {\n                    rootInstance[fieldName] = (typeof (fieldType) === \"function\")\n                        ? new fieldType() // is a schema reference\n                        : new (getType(Object.keys(fieldType)[0])).constructor(); // is a \"collection\"\n                }\n            }\n            return rootInstance;\n        };\n        __decorate([\n            type([ReflectionType], reflectionContext)\n        ], Reflection.prototype, \"types\", void 0);\n        __decorate([\n            type(\"number\", reflectionContext)\n        ], Reflection.prototype, \"rootType\", void 0);\n        return Reflection;\n    }(Schema));\n\n    registerType(\"map\", { constructor: MapSchema });\n    registerType(\"array\", { constructor: ArraySchema });\n    registerType(\"set\", { constructor: SetSchema });\n    registerType(\"collection\", { constructor: CollectionSchema, });\n\n    exports.ArraySchema = ArraySchema;\n    exports.CollectionSchema = CollectionSchema;\n    exports.Context = Context;\n    exports.MapSchema = MapSchema;\n    exports.Reflection = Reflection;\n    exports.ReflectionField = ReflectionField;\n    exports.ReflectionType = ReflectionType;\n    exports.Schema = Schema;\n    exports.SchemaDefinition = SchemaDefinition;\n    exports.SetSchema = SetSchema;\n    exports.decode = decode;\n    exports.defineTypes = defineTypes;\n    exports.deprecated = deprecated;\n    exports.dumpChanges = dumpChanges;\n    exports.encode = encode;\n    exports.filter = filter;\n    exports.filterChildren = filterChildren;\n    exports.hasFilter = hasFilter;\n    exports.registerType = registerType;\n    exports.type = type;\n\n}));\n", "import * as msgpack from './msgpack';\n\nimport { Connection } from './Connection';\nimport { Protocol, utf8Length, utf8Read } from './Protocol';\nimport { getSerializer, Serializer } from './serializer/Serializer';\n\n// The unused imports here are important for better `.d.ts` file generation\n// (Later merged with `dts-bundle-generator`)\nimport { createNanoEvents } from './core/nanoevents';\nimport { createSignal } from './core/signal';\n\nimport { Context, decode, encode, Schema } from '@colyseus/schema';\nimport { SchemaConstructor, SchemaSerializer } from './serializer/SchemaSerializer';\nimport { CloseCode } from './errors/ServerError';\n\nexport interface RoomAvailable<Metadata = any> {\n    name: string;\n    roomId: string;\n    clients: number;\n    maxClients: number;\n    metadata?: Metadata;\n}\n\nexport class Room<State= any> {\n    public roomId: string;\n    public sessionId: string;\n    public reconnectionToken: string;\n\n    public name: string;\n    public connection: Connection;\n\n    // Public signals\n    public onStateChange = createSignal<(state: State) => void>();\n    public onError = createSignal<(code: number, message?: string) => void>();\n    public onLeave = createSignal<(code: number, reason?: string) => void>();\n    protected onJoin = createSignal();\n\n    public serializerId: string;\n    public serializer: Serializer<State>;\n\n    protected hasJoined: boolean = false;\n\n    // TODO: remove me on 1.0.0\n    protected rootSchema: SchemaConstructor<State>;\n\n    protected onMessageHandlers = createNanoEvents();\n\n    constructor(name: string, rootSchema?: SchemaConstructor<State>) {\n        this.roomId = null;\n        this.name = name;\n\n        if (rootSchema) {\n            this.serializer = new (getSerializer(\"schema\"));\n            this.rootSchema = rootSchema;\n            (this.serializer as SchemaSerializer).state = new rootSchema();\n        }\n\n        this.onError((code, message) => console.warn?.(`colyseus.js - onError => (${code}) ${message}`));\n        this.onLeave(() => this.removeAllListeners());\n    }\n\n    // TODO: deprecate me on version 1.0\n    get id() { return this.roomId; }\n\n    public connect(\n        endpoint: string,\n        devModeCloseCallback?: () => void,\n        room: Room = this, // when reconnecting on devMode, re-use previous room intance for handling events.\n        headers?: any,\n    ) {\n        const connection = new Connection();\n        room.connection = connection;\n\n        connection.events.onmessage = Room.prototype.onMessageCallback.bind(room);\n        connection.events.onclose = function (e: CloseEvent) {\n            if (!room.hasJoined) {\n                console.warn?.(`Room connection was closed unexpectedly (${e.code}): ${e.reason}`);\n                room.onError.invoke(e.code, e.reason);\n                return;\n            }\n            if (e.code === CloseCode.DEVMODE_RESTART && devModeCloseCallback) {\n                devModeCloseCallback();\n            } else {\n                room.onLeave.invoke(e.code, e.reason);\n                room.destroy();\n            }\n        };\n        connection.events.onerror = function (e: CloseEvent) {\n            console.warn?.(`Room, onError (${e.code}): ${e.reason}`);\n            room.onError.invoke(e.code, e.reason);\n        };\n\n        connection.connect(endpoint, headers);\n    }\n\n    public leave(consented: boolean = true): Promise<number> {\n        return new Promise((resolve) => {\n            this.onLeave((code) => resolve(code));\n\n            if (this.connection) {\n                if (consented) {\n                    this.connection.send([Protocol.LEAVE_ROOM]);\n\n                } else {\n                    this.connection.close();\n                }\n\n            } else {\n                this.onLeave.invoke(CloseCode.CONSENTED);\n            }\n        });\n    }\n\n    public onMessage<T = any>(\n        type: \"*\",\n        callback: (type: string | number | Schema, message: T) => void\n    )\n    public onMessage<T extends (typeof Schema & (new (...args: any[]) => any))>(\n        type: T,\n        callback: (message: InstanceType<T>) => void\n    )\n    public onMessage<T = any>(\n        type: string | number,\n        callback: (message: T) => void\n    )\n    public onMessage(\n        type: '*' | string | number | typeof Schema,\n        callback: (...args: any[]) => void\n    ) {\n        return this.onMessageHandlers.on(this.getMessageHandlerKey(type), callback);\n    }\n\n    public send<T = any>(type: string | number, message?: T): void {\n        const initialBytes: number[] = [Protocol.ROOM_DATA];\n\n        if (typeof(type) === \"string\") {\n            encode.string(initialBytes, type);\n\n        } else {\n            encode.number(initialBytes, type);\n        }\n\n        let arr: Uint8Array;\n\n        if (message !== undefined) {\n            const encoded = msgpack.encode(message);\n            arr = new Uint8Array(initialBytes.length + encoded.byteLength);\n            arr.set(new Uint8Array(initialBytes), 0);\n            arr.set(new Uint8Array(encoded), initialBytes.length);\n\n        } else {\n            arr = new Uint8Array(initialBytes);\n        }\n\n        this.connection.send(arr.buffer);\n    }\n\n    public sendBytes(type: string | number, bytes: number[] | ArrayBufferLike) {\n        const initialBytes: number[] = [Protocol.ROOM_DATA_BYTES];\n\n        if (typeof(type) === \"string\") {\n            encode.string(initialBytes, type);\n\n        } else {\n            encode.number(initialBytes, type);\n        }\n\n        let arr: Uint8Array;\n        arr = new Uint8Array(initialBytes.length + ((bytes as ArrayBufferLike).byteLength || (bytes as number[]).length));\n        arr.set(new Uint8Array(initialBytes), 0);\n        arr.set(new Uint8Array(bytes), initialBytes.length);\n\n        this.connection.send(arr.buffer);\n    }\n\n    public get state (): State {\n        return this.serializer.getState();\n    }\n\n    public removeAllListeners() {\n        this.onJoin.clear();\n        this.onStateChange.clear();\n        this.onError.clear();\n        this.onLeave.clear();\n        this.onMessageHandlers.events = {};\n    }\n\n    protected onMessageCallback(event: MessageEvent) {\n        const bytes = Array.from(new Uint8Array(event.data))\n        const code = bytes[0];\n\n        if (code === Protocol.JOIN_ROOM) {\n            let offset = 1;\n\n            const reconnectionToken = utf8Read(bytes, offset);\n            offset += utf8Length(reconnectionToken);\n\n            this.serializerId = utf8Read(bytes, offset);\n            offset += utf8Length(this.serializerId);\n\n            // Instantiate serializer if not locally available.\n            if (!this.serializer) {\n                const serializer = getSerializer(this.serializerId)\n                this.serializer = new serializer();\n            }\n\n            if (bytes.length > offset && this.serializer.handshake) {\n                this.serializer.handshake(bytes, { offset });\n            }\n\n            this.reconnectionToken = `${this.roomId}:${reconnectionToken}`;\n\n            this.hasJoined = true;\n            this.onJoin.invoke();\n\n            // acknowledge successfull JOIN_ROOM\n            this.connection.send([Protocol.JOIN_ROOM]);\n\n        } else if (code === Protocol.ERROR) {\n            const it: decode.Iterator = { offset: 1 };\n\n            const code = decode.number(bytes, it);\n            const message = decode.string(bytes, it);\n\n            this.onError.invoke(code, message);\n\n        } else if (code === Protocol.LEAVE_ROOM) {\n            this.leave();\n\n        } else if (code === Protocol.ROOM_DATA_SCHEMA) {\n            const it = { offset: 1 };\n\n            const context: Context = (this.serializer.getState() as any).constructor._context;\n            const type = context.get(decode.number(bytes, it));\n\n            const message: Schema = new (type as any)();\n            message.decode(bytes, it);\n\n            this.dispatchMessage(type, message);\n\n        } else if (code === Protocol.ROOM_STATE) {\n            bytes.shift(); // drop `code` byte\n            this.setState(bytes);\n\n        } else if (code === Protocol.ROOM_STATE_PATCH) {\n            bytes.shift(); // drop `code` byte\n            this.patch(bytes);\n\n        } else if (code === Protocol.ROOM_DATA) {\n            const it: decode.Iterator = { offset: 1 };\n\n            const type = (decode.stringCheck(bytes, it))\n                ? decode.string(bytes, it)\n                : decode.number(bytes, it);\n\n            const message = (bytes.length > it.offset)\n                ? msgpack.decode(event.data, it.offset)\n                : undefined;\n\n            this.dispatchMessage(type, message);\n\n        } else if (code === Protocol.ROOM_DATA_BYTES) {\n            const it: decode.Iterator = { offset: 1 };\n\n            const type = (decode.stringCheck(bytes, it))\n                ? decode.string(bytes, it)\n                : decode.number(bytes, it);\n\n            this.dispatchMessage(type, new Uint8Array(bytes.slice(it.offset)));\n        }\n    }\n\n    protected setState(encodedState: number[]): void {\n        this.serializer.setState(encodedState);\n        this.onStateChange.invoke(this.serializer.getState());\n    }\n\n    protected patch(binaryPatch: number[]) {\n        this.serializer.patch(binaryPatch);\n        this.onStateChange.invoke(this.serializer.getState());\n    }\n\n    private dispatchMessage(type: string | number | typeof Schema, message: any) {\n        const messageType = this.getMessageHandlerKey(type);\n\n        if (this.onMessageHandlers.events[messageType]) {\n            this.onMessageHandlers.emit(messageType, message);\n\n        } else if (this.onMessageHandlers.events['*']) {\n            this.onMessageHandlers.emit('*', type, message);\n\n        } else {\n            console.warn?.(`colyseus.js: onMessage() not registered for type '${type}'.`);\n        }\n    }\n\n    private destroy () {\n        if (this.serializer) {\n            this.serializer.teardown();\n        }\n    }\n\n    private getMessageHandlerKey(type: string | number | typeof Schema): string {\n        switch (typeof(type)) {\n            // typeof Schema\n            case \"function\": return `$${(type as typeof Schema)._typeid}`;\n\n            // string\n            case \"string\": return type;\n\n            // number\n            case \"number\": return `i${type}`;\n\n            default: throw new Error(\"invalid message type.\");\n        }\n    }\n\n}\n", "function apply(src, tar) {\n\ttar.headers = src.headers || {};\n\ttar.statusMessage = src.statusText;\n\ttar.statusCode = src.status;\n\ttar.data = src.response;\n}\n\nexport function send(method, uri, opts) {\n\treturn new Promise(function (res, rej) {\n\t\topts = opts || {};\n\t\tvar req = new XMLHttpRequest;\n\t\tvar k, tmp, arr, str=opts.body;\n\t\tvar headers = opts.headers || {};\n\n\t\t// IE compatible\n\t\tif (opts.timeout) req.timeout = opts.timeout;\n\t\treq.ontimeout = req.onerror = function (err) {\n\t\t\terr.timeout = err.type == 'timeout';\n\t\t\trej(err);\n\t\t}\n\n\t\treq.open(method, uri.href || uri);\n\n\t\treq.onload = function () {\n\t\t\tarr = req.getAllResponseHeaders().trim().split(/[\\r\\n]+/);\n\t\t\tapply(req, req); //=> req.headers\n\n\t\t\twhile (tmp = arr.shift()) {\n\t\t\t\ttmp = tmp.split(': ');\n\t\t\t\treq.headers[tmp.shift().toLowerCase()] = tmp.join(': ');\n\t\t\t}\n\n\t\t\ttmp = req.headers['content-type'];\n\t\t\tif (tmp && !!~tmp.indexOf('application/json')) {\n\t\t\t\ttry {\n\t\t\t\t\treq.data = JSON.parse(req.data, opts.reviver);\n\t\t\t\t} catch (err) {\n\t\t\t\t\tapply(req, err);\n\t\t\t\t\treturn rej(err);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t(req.status >= 400 ? rej : res)(req);\n\t\t};\n\n\t\tif (typeof FormData < 'u' && str instanceof FormData) {\n\t\t\t// str = opts.body\n\t\t} else if (str && typeof str == 'object') {\n\t\t\theaders['content-type'] = 'application/json';\n\t\t\tstr = JSON.stringify(str);\n\t\t}\n\n\t\treq.withCredentials = !!opts.withCredentials;\n\n\t\tfor (k in headers) {\n\t\t\treq.setRequestHeader(k, headers[k]);\n\t\t}\n\n\t\treq.send(str);\n\t});\n}\n\nexport var get = /*#__PURE__*/ send.bind(send, 'GET');\nexport var post = /*#__PURE__*/ send.bind(send, 'POST');\nexport var patch = /*#__PURE__*/ send.bind(send, 'PATCH');\nexport var del = /*#__PURE__*/ send.bind(send, 'DELETE');\nexport var put = /*#__PURE__*/ send.bind(send, 'PUT');\n", "import { Client } from \"./Client\";\nimport { ServerError } from \"./errors/ServerError\";\nimport * as httpie from \"httpie\";\n\nexport class HTTP {\n    public authToken: string;\n\n    constructor(\n        protected client: Client,\n        public headers: { [id: string]: string } = {},\n    ) {}\n\n    public get<T = any>(path: string, options: Partial<httpie.Options> = {}): Promise<httpie.Response<T>> {\n        return this.request(\"get\", path, options);\n    }\n\n    public post<T = any>(path: string, options: Partial<httpie.Options> = {}): Promise<httpie.Response<T>> {\n        return this.request(\"post\", path, options);\n    }\n\n    public del<T = any>(path: string, options: Partial<httpie.Options> = {}): Promise<httpie.Response<T>> {\n        return this.request(\"del\", path, options);\n    }\n\n    public put<T = any>(path: string, options: Partial<httpie.Options> = {}): Promise<httpie.Response<T>> {\n        return this.request(\"put\", path, options);\n    }\n\n    protected request(method: \"get\" | \"post\" | \"put\" | \"del\", path: string, options: Partial<httpie.Options> = {}): Promise<httpie.Response> {\n        return httpie[method](this.client['getHttpEndpoint'](path), this.getOptions(options)).catch((e: any) => {\n            const status = e.statusCode; //  || -1\n            const message = e.data?.error || e.statusMessage || e.message; //  || \"offline\"\n\n            if (!status && !message) {\n                throw e;\n            }\n\n            throw new ServerError(status, message);\n        });\n    }\n\n    protected getOptions(options: Partial<httpie.Options>) {\n        // merge default custom headers with user headers\n        options.headers = Object.assign({}, this.headers, options.headers);\n\n        if (this.authToken) {\n            options.headers['Authorization'] = `Bearer ${this.authToken}`;\n        }\n\n        if (typeof (cc) !== 'undefined' && cc.sys && cc.sys.isNative) {\n            //\n            // Workaround for Cocos Creator on Native platform\n            // \"Cannot set property withCredentials of #<XMLHttpRequest> which has only a getter\"\n            //\n        } else {\n            // always include credentials\n            options.withCredentials = true;\n        }\n\n        return options;\n    }\n}\n", "/// <reference path=\"../typings/cocos-creator.d.ts\" />\n\n/**\n * We do not assign 'storage' to window.localStorage immediatelly for React\n * Native compatibility. window.localStorage is not present when this module is\n * loaded.\n */\n\nlet storage: any;\n\nfunction getStorage(): Storage {\n    if (!storage)  {\n        try {\n            storage = (typeof (cc) !== 'undefined' && cc.sys && cc.sys.localStorage)\n                ? cc.sys.localStorage  // compatibility with cocos creator\n                : window.localStorage; // RN does have window object at this point, but localStorage is not defined\n\n        } catch (e) {\n            // ignore error\n        }\n    }\n\n    if (!storage) {\n        // mock localStorage if not available (Node.js or RN environment)\n        storage = {\n            cache: {},\n            setItem: function (key, value) { this.cache[key] = value; },\n            getItem: function (key) { this.cache[key]; },\n            removeItem: function (key) { delete this.cache[key]; },\n        };\n    }\n\n    return storage;\n}\n\nexport function setItem(key: string, value: string) {\n    getStorage().setItem(key, value);\n}\n\nexport function removeItem(key: string) {\n    getStorage().removeItem(key);\n}\n\nexport function getItem(key: string, callback: Function) {\n    const value: any = getStorage().getItem(key);\n\n    if (\n        typeof (Promise) === 'undefined' || // old browsers\n        !(value instanceof Promise)\n    ) {\n        // browser has synchronous return\n        callback(value);\n\n    } else {\n        // react-native is asynchronous\n        value.then((id) => callback(id));\n    }\n}\n", "import { HTTP } from \"./HTTP\";\nimport { getItem, removeItem, setItem } from \"./Storage\";\nimport { createNanoEvents } from './core/nanoevents';\n\nexport interface AuthSettings {\n    path: string;\n    key: string;\n}\n\nexport interface PopupSettings {\n    prefix: string;\n    width: number;\n    height: number;\n}\n\nexport interface AuthData {\n    user: any;\n    token: string;\n}\n\nexport class Auth {\n    settings: AuthSettings = {\n        path: \"/auth\",\n        key: \"colyseus-auth-token\",\n    };\n\n    #_initialized = false;\n    #_initializationPromise: Promise<void>;\n    #_signInWindow = undefined;\n    #_events = createNanoEvents();\n\n    constructor(protected http: HTTP) {\n        getItem(this.settings.key, (token) => this.token = token);\n    }\n\n    public set token(token: string) {\n        this.http.authToken = token;\n    }\n\n    public get token(): string {\n        return this.http.authToken;\n    }\n\n    public onChange(callback: (response: AuthData) => void) {\n        const unbindChange = this.#_events.on(\"change\", callback);\n        if (!this.#_initialized) {\n            this.#_initializationPromise = new Promise<void>((resolve, reject) => {\n                this.getUserData().then((userData) => {\n                    this.emitChange({ ...userData, token: this.token });\n\n                }).catch((e) => {\n                    // user is not logged in, or service is down\n                    this.emitChange({ user: null, token: undefined });\n\n                }).finally(() => {\n                    resolve();\n                });\n            });\n        }\n        this.#_initialized = true;\n        return unbindChange;\n    }\n\n    public async getUserData() {\n        if (this.token) {\n            return (await this.http.get(`${this.settings.path}/userdata`)).data;\n        } else {\n            throw new Error(\"missing auth.token\");\n        }\n    }\n\n    public async registerWithEmailAndPassword(email: string, password: string, options?: any) {\n        const data = (await this.http.post(`${this.settings.path}/register`, {\n            body: { email, password, options, },\n        })).data;\n\n        this.emitChange(data);\n\n        return data;\n    }\n\n    public async signInWithEmailAndPassword(email: string, password: string) {\n        const data = (await this.http.post(`${this.settings.path}/login`, {\n            body: { email, password, },\n        })).data;\n\n        this.emitChange(data);\n\n        return data;\n    }\n\n    public async signInAnonymously(options?: any) {\n        const data = (await this.http.post(`${this.settings.path}/anonymous`, {\n            body: { options, }\n        })).data;\n\n        this.emitChange(data);\n\n        return data;\n    }\n\n    public async sendPasswordResetEmail(email: string) {\n        return (await this.http.post(`${this.settings.path}/forgot-password`, {\n            body: { email, }\n        })).data;\n    }\n\n    public async signInWithProvider(providerName: string, settings: Partial<PopupSettings> = {}) {\n        return new Promise((resolve, reject) => {\n            const w = settings.width || 480;\n            const h = settings.height || 768;\n\n            // forward existing token for upgrading\n            const upgradingToken = this.token ? `?token=${this.token}` : \"\";\n\n            // Capitalize first letter of providerName\n            const title = `Login with ${(providerName[0].toUpperCase() + providerName.substring(1))}`;\n            const url = this.http['client']['getHttpEndpoint'](`${(settings.prefix || `${this.settings.path}/provider`)}/${providerName}${upgradingToken}`);\n\n            const left = (screen.width / 2) - (w / 2);\n            const top = (screen.height / 2) - (h / 2);\n\n            this.#_signInWindow = window.open(url, title, 'toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=no, resizable=no, copyhistory=no, width=' + w + ', height=' + h + ', top=' + top + ', left=' + left);\n\n            const onMessage = (event: MessageEvent) => {\n                // TODO: it is a good idea to check if event.origin can be trusted!\n                // if (event.origin.indexOf(window.location.hostname) === -1) { return; }\n\n                // require 'user' and 'token' inside received data.\n                if (event.data.user === undefined && event.data.token === undefined) { return; }\n\n                clearInterval(rejectionChecker);\n                this.#_signInWindow.close();\n                this.#_signInWindow = undefined;\n\n                window.removeEventListener(\"message\", onMessage);\n\n                if (event.data.error !== undefined) {\n                    reject(event.data.error);\n\n                } else {\n                    resolve(event.data);\n                    this.emitChange(event.data);\n                }\n            }\n\n            const rejectionChecker = setInterval(() => {\n                if (!this.#_signInWindow || this.#_signInWindow.closed) {\n                    this.#_signInWindow = undefined;\n                    reject(\"cancelled\");\n                    window.removeEventListener(\"message\", onMessage);\n                }\n            }, 200);\n\n            window.addEventListener(\"message\", onMessage);\n        });\n    }\n\n    public async signOut() {\n        this.emitChange({ user: null, token: null });\n    }\n\n    private emitChange(authData: Partial<AuthData>) {\n        if (authData.token !== undefined) {\n            this.token = authData.token;\n\n            if (authData.token === null) {\n                removeItem(this.settings.key);\n\n            } else {\n                // store key in localStorage\n                setItem(this.settings.key, authData.token);\n            }\n        }\n\n        this.#_events.emit(\"change\", authData);\n    }\n\n}\n", "/**\n * Discord Embedded App SDK\n * https://github.com/colyseus/colyseus/issues/707\n *\n * All URLs must go through the local proxy from\n * https://<app_id>.discordsays.com/.proxy/<mapped_url>/...\n *\n * URL Mapping Examples:\n *\n * 1. Using Colyseus Cloud:\n *   - /colyseus/{subdomain} -> {subdomain}.colyseus.cloud\n *\n *   Example:\n *     const client = new Client(\"https://xxxx.colyseus.cloud\");\n *\n * -------------------------------------------------------------\n *\n * 2. Using `cloudflared` tunnel:\n *   - /colyseus/ -> <your-cloudflared-url>.trycloudflare.com\n *\n *   Example:\n *     const client = new Client(\"https://<your-cloudflared-url>.trycloudflare.com\");\n *\n * -------------------------------------------------------------\n *\n * 3. Providing a manual /.proxy/your-mapping:\n *   - /your-mapping/ -> your-endpoint.com\n *\n *   Example:\n *     const client = new Client(\"/.proxy/your-mapping\");\n *\n */\nexport function discordURLBuilder (url: URL): string {\n    const localHostname = window?.location?.hostname || \"localhost\";\n\n    const remoteHostnameSplitted = url.hostname.split('.');\n    const subdomain = (\n        !url.hostname.includes(\"trycloudflare.com\") && // ignore cloudflared subdomains\n        !url.hostname.includes(\"discordsays.com\") &&  // ignore discordsays.com subdomains\n        remoteHostnameSplitted.length > 2\n    )\n        ? `/${remoteHostnameSplitted[0]}`\n        : '';\n\n    return (url.pathname.startsWith(\"/.proxy\"))\n        ? `${url.protocol}//${localHostname}${subdomain}${url.pathname}${url.search}`\n        : `${url.protocol}//${localHostname}/.proxy/colyseus${subdomain}${url.pathname}${url.search}`;\n}\n", "import { ServerError } from './errors/ServerError';\nimport { Room, RoomAvailable } from './Room';\nimport { SchemaConstructor } from './serializer/SchemaSerializer';\nimport { HTTP } from \"./HTTP\";\nimport { Auth } from './Auth';\nimport { discordURLBuilder } from './3rd_party/discord';\n\nexport type JoinOptions = any;\n\nexport class MatchMakeError extends Error {\n    code: number;\n    constructor(message: string, code: number) {\n        super(message);\n        this.code = code;\n        Object.setPrototypeOf(this, MatchMakeError.prototype);\n    }\n}\n\n// - React Native does not provide `window.location`\n// - Cocos Creator (Native) does not provide `window.location.hostname`\nconst DEFAULT_ENDPOINT = (typeof (window) !== \"undefined\" &&  typeof (window?.location?.hostname) !== \"undefined\")\n    ? `${window.location.protocol.replace(\"http\", \"ws\")}//${window.location.hostname}${(window.location.port && `:${window.location.port}`)}`\n    : \"ws://127.0.0.1:2567\";\n\nexport interface EndpointSettings {\n    hostname: string,\n    secure: boolean,\n    port?: number,\n    pathname?: string,\n}\n\nexport interface ClientOptions {\n    headers?: { [id: string]: string };\n    urlBuilder?: (url: URL) => string;\n}\n\nexport class Client {\n    public http: HTTP;\n    public auth: Auth;\n\n    protected settings: EndpointSettings;\n    protected urlBuilder: (url: URL) => string;\n\n    constructor(\n        settings: string | EndpointSettings = DEFAULT_ENDPOINT,\n        options?: ClientOptions,\n    ) {\n        if (typeof (settings) === \"string\") {\n\n            //\n            // endpoint by url\n            //\n            const url = (settings.startsWith(\"/\"))\n                ? new URL(settings, DEFAULT_ENDPOINT)\n                : new URL(settings);\n\n            const secure = (url.protocol === \"https:\" || url.protocol === \"wss:\");\n            const port = Number(url.port || (secure ? 443 : 80));\n\n            this.settings = {\n                hostname: url.hostname,\n                pathname: url.pathname,\n                port,\n                secure\n            };\n\n        } else {\n            //\n            // endpoint by settings\n            //\n            if (settings.port === undefined) {\n                settings.port = (settings.secure) ? 443 : 80;\n            }\n            if (settings.pathname === undefined) {\n                settings.pathname = \"\";\n            }\n            this.settings = settings;\n        }\n\n        // make sure pathname does not end with \"/\"\n        if (this.settings.pathname.endsWith(\"/\")) {\n            this.settings.pathname = this.settings.pathname.slice(0, -1);\n        }\n\n        this.http = new HTTP(this, options?.headers || {});\n        this.auth = new Auth(this.http);\n\n        this.urlBuilder = options?.urlBuilder;\n\n        //\n        // Discord Embedded SDK requires a custom URL builder\n        //\n        if (\n            !this.urlBuilder &&\n            typeof (window) !== \"undefined\" &&\n            window?.location?.hostname?.includes(\"discordsays.com\")\n        ) {\n            this.urlBuilder = discordURLBuilder;\n            console.log(\"Colyseus SDK: Discord Embedded SDK detected. Using custom URL builder.\");\n        }\n    }\n\n    public async joinOrCreate<T>(roomName: string, options: JoinOptions = {}, rootSchema?: SchemaConstructor<T>) {\n        return await this.createMatchMakeRequest<T>('joinOrCreate', roomName, options, rootSchema);\n    }\n\n    public async create<T>(roomName: string, options: JoinOptions = {}, rootSchema?: SchemaConstructor<T>) {\n        return await this.createMatchMakeRequest<T>('create', roomName, options, rootSchema);\n    }\n\n    public async join<T>(roomName: string, options: JoinOptions = {}, rootSchema?: SchemaConstructor<T>) {\n        return await this.createMatchMakeRequest<T>('join', roomName, options, rootSchema);\n    }\n\n    public async joinById<T>(roomId: string, options: JoinOptions = {}, rootSchema?: SchemaConstructor<T>) {\n        return await this.createMatchMakeRequest<T>('joinById', roomId, options, rootSchema);\n    }\n\n    /**\n     * Re-establish connection with a room this client was previously connected to.\n     *\n     * @param reconnectionToken The `room.reconnectionToken` from previously connected room.\n     * @param rootSchema (optional) Concrete root schema definition\n     * @returns Promise<Room>\n     */\n    public async reconnect<T>(reconnectionToken: string, rootSchema?: SchemaConstructor<T>) {\n        if (typeof (reconnectionToken) === \"string\" && typeof (rootSchema) === \"string\") {\n            throw new Error(\"DEPRECATED: .reconnect() now only accepts 'reconnectionToken' as argument.\\nYou can get this token from previously connected `room.reconnectionToken`\");\n        }\n        const [roomId, token] = reconnectionToken.split(\":\");\n\t\tif (!roomId || !token) {\n\t\t\tthrow new Error(\"Invalid reconnection token format.\\nThe format should be roomId:reconnectionToken\");\n\t\t}\n        return await this.createMatchMakeRequest<T>('reconnect', roomId, { reconnectionToken: token }, rootSchema);\n    }\n\n    public async getAvailableRooms<Metadata = any>(roomName: string = \"\"): Promise<RoomAvailable<Metadata>[]> {\n        return (\n            await this.http.get(`matchmake/${roomName}`, {\n                headers: {\n                    'Accept': 'application/json'\n                }\n            })\n        ).data;\n    }\n\n    public async consumeSeatReservation<T>(\n        response: any,\n        rootSchema?: SchemaConstructor<T>,\n        reuseRoomInstance?: Room // used in devMode\n    ): Promise<Room<T>> {\n        const room = this.createRoom<T>(response.room.name, rootSchema);\n        room.roomId = response.room.roomId;\n        room.sessionId = response.sessionId;\n\n        const options: any = { sessionId: room.sessionId };\n\n        // forward \"reconnection token\" in case of reconnection.\n        if (response.reconnectionToken) {\n            options.reconnectionToken = response.reconnectionToken;\n        }\n\n        const targetRoom = reuseRoomInstance || room;\n        room.connect(this.buildEndpoint(response.room, options), response.devMode && (async () => {\n            console.info(`[Colyseus devMode]: ${String.fromCodePoint(0x1F504)} Re-establishing connection with room id '${room.roomId}'...`); // 🔄\n\n            let retryCount = 0;\n            let retryMaxRetries = 8;\n\n            const retryReconnection = async () => {\n                retryCount++;\n\n                try {\n                    await this.consumeSeatReservation(response, rootSchema, targetRoom);\n                    console.info(`[Colyseus devMode]: ${String.fromCodePoint(0x2705)} Successfully re-established connection with room '${room.roomId}'`); // ✅\n\n                } catch (e) {\n                    if (retryCount < retryMaxRetries) {\n                        console.info(`[Colyseus devMode]: ${String.fromCodePoint(0x1F504)} retrying... (${retryCount} out of ${retryMaxRetries})`); // 🔄\n                        setTimeout(retryReconnection, 2000);\n\n                    } else {\n                        console.info(`[Colyseus devMode]: ${String.fromCodePoint(0x274C)} Failed to reconnect. Is your server running? Please check server logs.`); // ❌\n                    }\n                }\n            };\n\n            setTimeout(retryReconnection, 2000);\n        }), targetRoom, this.http.headers);\n\n        return new Promise((resolve, reject) => {\n            const onError = (code, message) => reject(new ServerError(code, message));\n            targetRoom.onError.once(onError);\n\n            targetRoom['onJoin'].once(() => {\n                targetRoom.onError.remove(onError);\n                resolve(targetRoom);\n            });\n        });\n    }\n\n    protected async createMatchMakeRequest<T>(\n        method: string,\n        roomName: string,\n        options: JoinOptions = {},\n        rootSchema?: SchemaConstructor<T>,\n        reuseRoomInstance?: Room,\n    ) {\n        const response = (\n            await this.http.post(`matchmake/${method}/${roomName}`, {\n                headers: {\n                    'Accept': 'application/json',\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(options)\n            })\n        ).data;\n\n        // FIXME: HTTP class is already handling this as ServerError.\n        if (response.error) {\n            throw new MatchMakeError(response.error, response.code);\n        }\n\n        // forward reconnection token during \"reconnect\" methods.\n        if (method === \"reconnect\") {\n            response.reconnectionToken = options.reconnectionToken;\n        }\n\n        return await this.consumeSeatReservation<T>(response, rootSchema, reuseRoomInstance);\n    }\n\n    protected createRoom<T>(roomName: string, rootSchema?: SchemaConstructor<T>) {\n        return new Room<T>(roomName, rootSchema);\n    }\n\n    protected buildEndpoint(room: any, options: any = {}) {\n        const params = [];\n\n        // append provided options\n        for (const name in options) {\n            if (!options.hasOwnProperty(name)) {\n                continue;\n            }\n            params.push(`${name}=${options[name]}`);\n        }\n\n        let endpoint = (this.settings.secure)\n            ? \"wss://\"\n            : \"ws://\"\n\n        if (room.publicAddress) {\n            endpoint += `${room.publicAddress}`;\n\n        } else {\n            endpoint += `${this.settings.hostname}${this.getEndpointPort()}${this.settings.pathname}`;\n        }\n\n        const endpointURL = `${endpoint}/${room.processId}/${room.roomId}?${params.join('&')}`;\n        return (this.urlBuilder)\n            ? this.urlBuilder(new URL(endpointURL))\n            : endpointURL;\n    }\n\n    protected getHttpEndpoint(segments: string = '') {\n        const path = segments.startsWith(\"/\") ? segments : `/${segments}`;\n        const endpointURL = `${(this.settings.secure) ? \"https\" : \"http\"}://${this.settings.hostname}${this.getEndpointPort()}${this.settings.pathname}${path}`;\n        return (this.urlBuilder)\n            ? this.urlBuilder(new URL(endpointURL))\n            : endpointURL;\n    }\n\n    protected getEndpointPort() {\n        return (this.settings.port !== 80 && this.settings.port !== 443)\n            ? `:${this.settings.port}`\n            : \"\";\n    }\n}\n", "import { Serializer } from \"./Serializer\";\nimport { Schema, Reflection, Iterator } from \"@colyseus/schema\";\n\nexport type SchemaConstructor<T = Schema> = new (...args: any[]) => T;\n\nexport class SchemaSerializer<T extends Schema = any> implements Serializer<T> {\n    state: T;\n\n    setState(rawState: any) {\n        return this.state.decode(rawState);\n    }\n\n    getState() {\n        return this.state;\n    }\n\n    patch(patches) {\n        return this.state.decode(patches);\n    }\n\n    teardown() {\n        this.state?.['$changes']?.root.clearRefs();\n    }\n\n    handshake(bytes: number[], it?: Iterator) {\n        if (this.state) {\n            // TODO: validate client/server definitinos\n            const reflection = new Reflection();\n            reflection.decode(bytes, it);\n\n        } else {\n            // initialize reflected state from server\n            this.state = Reflection.decode(bytes, it) as any;\n        }\n    }\n}\n", "import { Serializer } from \"./Serializer\";\n\nexport class NoneSerializer<T = any> implements Serializer<T> {\n    setState(rawState: any): void {}\n    getState() { return null; }\n    patch(patches) {}\n    teardown() { }\n    handshake(bytes: number[]) {}\n}\n", "import './legacy';\n\nexport { Client, JoinOptions, MatchMakeError, type EndpointSettings, type ClientOptions } from './Client';\nexport { Protocol, ErrorCode } from './Protocol';\nexport { Room, RoomAvailable } from './Room';\nexport { Auth, type AuthSettings, type PopupSettings } from \"./Auth\";\n\n/*\n * Serializers\n */\n\nimport { SchemaSerializer } from \"./serializer/SchemaSerializer\";\nimport { NoneSerializer } from \"./serializer/NoneSerializer\";\nimport { registerSerializer } from './serializer/Serializer';\n\nexport { registerSerializer, SchemaSerializer };\nregisterSerializer('schema', SchemaSerializer);\nregisterSerializer('none', NoneSerializer);\n"], "mappings": ";;;;;;;;AAAA;;AAOA,QAAI,CAAC,YAAY,QAAQ;AACrB,kBAAY,SAAS,CAAC,MAAgC;AAClD,eAAO,MAAM,QAAQ,OAAQ,MAAO,YAAY,EAAE,kBAAkB;MACxE;;AAKJ,QACI,OAAQ,eAAgB,eACxB,OAAQ,WAAY,aACtB;AAEE,aAAO,YAAY,IAAI;;;;;;;;;;;ACpB3B,QAAY;AAAZ,KAAA,SAAYA,YAAS;AACjB,MAAAA,WAAAA,WAAA,WAAA,IAAA,GAAA,IAAA;AACA,MAAAA,WAAAA,WAAA,iBAAA,IAAA,IAAA,IAAA;IACJ,GAHY,YAAA,QAAA,cAAA,QAAA,YAAS,CAAA,EAAA;AAKrB,QAAa,cAAb,cAAiC,MAAK;MAGpC,YAAY,MAAc,SAAe;AACvC,cAAM,OAAO;AAEb,aAAK,OAAO;AACZ,aAAK,OAAO;MACd;;AARF,YAAA,cAAA;;;;;;;;;;AC+BA,aAAS,QAAQ,QAAQ,QAAM;AAC3B,WAAK,UAAU;AACf,UAAI,kBAAkB,aAAa;AAC/B,aAAK,UAAU;AACf,aAAK,QAAQ,IAAI,SAAS,KAAK,OAAO;iBAC/B,YAAY,OAAO,MAAM,GAAG;AACnC,aAAK,UAAU,OAAO;AACtB,aAAK,QAAQ,IAAI,SAAS,KAAK,SAAS,OAAO,YAAY,OAAO,UAAU;aACzE;AACH,cAAM,IAAI,MAAM,kBAAkB;;IAE1C;AAEA,aAAS,SAAS,MAAM,QAAQ,QAAM;AAClC,UAAI,SAAS,IAAI,MAAM;AACvB,eAAS,IAAI,QAAQ,MAAM,SAAS,QAAQ,IAAI,KAAK,KAAK;AACtD,YAAI,OAAO,KAAK,SAAS,CAAC;AAC1B,aAAK,OAAO,SAAU,GAAM;AACxB,oBAAU,OAAO,aAAa,IAAI;AAClC;;AAEJ,aAAK,OAAO,SAAU,KAAM;AACxB,oBAAU,OAAO,cACX,OAAO,OAAS,IACjB,KAAK,SAAS,EAAE,CAAC,IAAI,EAAK;AAE/B;;AAEJ,aAAK,OAAO,SAAU,KAAM;AACxB,oBAAU,OAAO,cACX,OAAO,OAAS,MAChB,KAAK,SAAS,EAAE,CAAC,IAAI,OAAS,KAC9B,KAAK,SAAS,EAAE,CAAC,IAAI,OAAS,CAAE;AAEtC;;AAEJ,aAAK,OAAO,SAAU,KAAM;AACxB,iBAAQ,OAAO,MAAS,MAClB,KAAK,SAAS,EAAE,CAAC,IAAI,OAAS,MAC9B,KAAK,SAAS,EAAE,CAAC,IAAI,OAAS,KAC9B,KAAK,SAAS,EAAE,CAAC,IAAI,OAAS;AACpC,cAAI,OAAO,OAAU;AACjB,mBAAO;AACP,sBAAU,OAAO,cAAc,QAAQ,MAAM,QAAS,MAAM,QAAS,KAAM;iBACxE;AACH,sBAAU,OAAO,aAAa,GAAG;;AAErC;;AAEJ,cAAM,IAAI,MAAM,kBAAkB,KAAK,SAAS,EAAE,CAAC;;AAEvD,aAAO;IACX;AAEA,YAAQ,UAAU,SAAS,SAAU,QAAM;AACvC,UAAI,QAAQ,IAAI,MAAM,MAAM;AAC5B,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,cAAM,CAAC,IAAI,KAAK,OAAM;;AAE1B,aAAO;IACX;AAEA,YAAQ,UAAU,OAAO,SAAU,QAAM;AACrC,UAAI,MAAM,IAAI,QAAQ,CAAA;AACtB,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,cAAM,KAAK,OAAM;AACjB,cAAM,GAAG,IAAI,KAAK,OAAM;;AAE5B,aAAO;IACX;AAEA,YAAQ,UAAU,OAAO,SAAU,QAAM;AACrC,UAAI,QAAQ,SAAS,KAAK,OAAO,KAAK,SAAS,MAAM;AACrD,WAAK,WAAW;AAChB,aAAO;IACX;AAEA,YAAQ,UAAU,OAAO,SAAU,QAAM;AACrC,UAAI,QAAQ,KAAK,QAAQ,MAAM,KAAK,SAAS,KAAK,UAAU,MAAM;AAClE,WAAK,WAAW;AAChB,aAAO;IACX;AAEA,YAAQ,UAAU,SAAS,WAAA;AACvB,UAAI,SAAS,KAAK,MAAM,SAAS,KAAK,SAAS;AAC/C,UAAI,OAAO,SAAS,GAAG,OAAO,GAAG,KAAK,GAAG,KAAK;AAE9C,UAAI,SAAS,KAAM;AAEf,YAAI,SAAS,KAAM;AACf,iBAAO;;AAGX,YAAI,SAAS,KAAM;AACf,iBAAO,KAAK,KAAK,SAAS,EAAI;;AAGlC,YAAI,SAAS,KAAM;AACf,iBAAO,KAAK,OAAO,SAAS,EAAI;;AAGpC,eAAO,KAAK,KAAK,SAAS,EAAI;;AAIlC,UAAI,SAAS,KAAM;AACf,gBAAQ,MAAO,SAAS,KAAK;;AAGjC,cAAQ,QAAQ;QAEZ,KAAK;AACD,iBAAO;QAEX,KAAK;AACD,iBAAO;QAEX,KAAK;AACD,iBAAO;QAGX,KAAK;AACD,mBAAS,KAAK,MAAM,SAAS,KAAK,OAAO;AACzC,eAAK,WAAW;AAChB,iBAAO,KAAK,KAAK,MAAM;QAC3B,KAAK;AACD,mBAAS,KAAK,MAAM,UAAU,KAAK,OAAO;AAC1C,eAAK,WAAW;AAChB,iBAAO,KAAK,KAAK,MAAM;QAC3B,KAAK;AACD,mBAAS,KAAK,MAAM,UAAU,KAAK,OAAO;AAC1C,eAAK,WAAW;AAChB,iBAAO,KAAK,KAAK,MAAM;QAG3B,KAAK;AACD,mBAAS,KAAK,MAAM,SAAS,KAAK,OAAO;AACzC,iBAAO,KAAK,MAAM,QAAQ,KAAK,UAAU,CAAC;AAC1C,eAAK,WAAW;AAChB,cAAI,SAAS,IAAI;AAEb,gBAAI,KAAK,KAAK,MAAM,UAAU,KAAK,OAAO;AAC1C,iBAAK,KAAK,MAAM,SAAS,KAAK,UAAU,CAAC;AACzC,iBAAK,KAAK,MAAM,UAAU,KAAK,UAAU,CAAC;AAC1C,iBAAK,WAAW;AAChB,mBAAO,IAAI,MAAM,KAAK,aAAc,MAAM,MAAM,KAAK,GAAG;;AAE5D,iBAAO,CAAC,MAAM,KAAK,KAAK,MAAM,CAAC;QACnC,KAAK;AACD,mBAAS,KAAK,MAAM,UAAU,KAAK,OAAO;AAC1C,iBAAO,KAAK,MAAM,QAAQ,KAAK,UAAU,CAAC;AAC1C,eAAK,WAAW;AAChB,iBAAO,CAAC,MAAM,KAAK,KAAK,MAAM,CAAC;QACnC,KAAK;AACD,mBAAS,KAAK,MAAM,UAAU,KAAK,OAAO;AAC1C,iBAAO,KAAK,MAAM,QAAQ,KAAK,UAAU,CAAC;AAC1C,eAAK,WAAW;AAChB,iBAAO,CAAC,MAAM,KAAK,KAAK,MAAM,CAAC;QAGnC,KAAK;AACD,kBAAQ,KAAK,MAAM,WAAW,KAAK,OAAO;AAC1C,eAAK,WAAW;AAChB,iBAAO;QACX,KAAK;AACD,kBAAQ,KAAK,MAAM,WAAW,KAAK,OAAO;AAC1C,eAAK,WAAW;AAChB,iBAAO;QAGX,KAAK;AACD,kBAAQ,KAAK,MAAM,SAAS,KAAK,OAAO;AACxC,eAAK,WAAW;AAChB,iBAAO;QACX,KAAK;AACD,kBAAQ,KAAK,MAAM,UAAU,KAAK,OAAO;AACzC,eAAK,WAAW;AAChB,iBAAO;QACX,KAAK;AACD,kBAAQ,KAAK,MAAM,UAAU,KAAK,OAAO;AACzC,eAAK,WAAW;AAChB,iBAAO;QACX,KAAK;AACD,eAAK,KAAK,MAAM,UAAU,KAAK,OAAO,IAAI,KAAK,IAAI,GAAG,EAAE;AACxD,eAAK,KAAK,MAAM,UAAU,KAAK,UAAU,CAAC;AAC1C,eAAK,WAAW;AAChB,iBAAO,KAAK;QAGhB,KAAK;AACD,kBAAQ,KAAK,MAAM,QAAQ,KAAK,OAAO;AACvC,eAAK,WAAW;AAChB,iBAAO;QACX,KAAK;AACD,kBAAQ,KAAK,MAAM,SAAS,KAAK,OAAO;AACxC,eAAK,WAAW;AAChB,iBAAO;QACX,KAAK;AACD,kBAAQ,KAAK,MAAM,SAAS,KAAK,OAAO;AACxC,eAAK,WAAW;AAChB,iBAAO;QACX,KAAK;AACD,eAAK,KAAK,MAAM,SAAS,KAAK,OAAO,IAAI,KAAK,IAAI,GAAG,EAAE;AACvD,eAAK,KAAK,MAAM,UAAU,KAAK,UAAU,CAAC;AAC1C,eAAK,WAAW;AAChB,iBAAO,KAAK;QAGhB,KAAK;AACD,iBAAO,KAAK,MAAM,QAAQ,KAAK,OAAO;AACtC,eAAK,WAAW;AAChB,cAAI,SAAS,GAAM;AAEf,iBAAK,WAAW;AAChB,mBAAO;;AAEX,iBAAO,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC;QAC9B,KAAK;AACD,iBAAO,KAAK,MAAM,QAAQ,KAAK,OAAO;AACtC,eAAK,WAAW;AAChB,iBAAO,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC;QAC9B,KAAK;AACD,iBAAO,KAAK,MAAM,QAAQ,KAAK,OAAO;AACtC,eAAK,WAAW;AAChB,cAAI,SAAS,IAAI;AAEb,oBAAQ,KAAK,MAAM,UAAU,KAAK,OAAO;AACzC,iBAAK,WAAW;AAChB,mBAAO,IAAI,KAAK,QAAQ,GAAG;;AAE/B,iBAAO,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC;QAC9B,KAAK;AACD,iBAAO,KAAK,MAAM,QAAQ,KAAK,OAAO;AACtC,eAAK,WAAW;AAChB,cAAI,SAAS,GAAM;AAEf,iBAAK,KAAK,MAAM,SAAS,KAAK,OAAO,IAAI,KAAK,IAAI,GAAG,EAAE;AACvD,iBAAK,KAAK,MAAM,UAAU,KAAK,UAAU,CAAC;AAC1C,iBAAK,WAAW;AAChB,mBAAO,IAAI,KAAK,KAAK,EAAE;;AAE3B,cAAI,SAAS,IAAI;AAEb,iBAAK,KAAK,MAAM,UAAU,KAAK,OAAO;AACtC,iBAAK,KAAK,MAAM,UAAU,KAAK,UAAU,CAAC;AAC1C,iBAAK,WAAW;AAChB,gBAAI,KAAK,KAAK,KAAO,aAAc;AACnC,mBAAO,IAAI,KAAK,IAAI,OAAO,OAAO,KAAK,GAAG;;AAE9C,iBAAO,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC;QAC9B,KAAK;AACD,iBAAO,KAAK,MAAM,QAAQ,KAAK,OAAO;AACtC,eAAK,WAAW;AAChB,iBAAO,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC;QAG/B,KAAK;AACD,mBAAS,KAAK,MAAM,SAAS,KAAK,OAAO;AACzC,eAAK,WAAW;AAChB,iBAAO,KAAK,KAAK,MAAM;QAC3B,KAAK;AACD,mBAAS,KAAK,MAAM,UAAU,KAAK,OAAO;AAC1C,eAAK,WAAW;AAChB,iBAAO,KAAK,KAAK,MAAM;QAC3B,KAAK;AACD,mBAAS,KAAK,MAAM,UAAU,KAAK,OAAO;AAC1C,eAAK,WAAW;AAChB,iBAAO,KAAK,KAAK,MAAM;QAG3B,KAAK;AACD,mBAAS,KAAK,MAAM,UAAU,KAAK,OAAO;AAC1C,eAAK,WAAW;AAChB,iBAAO,KAAK,OAAO,MAAM;QAC7B,KAAK;AACD,mBAAS,KAAK,MAAM,UAAU,KAAK,OAAO;AAC1C,eAAK,WAAW;AAChB,iBAAO,KAAK,OAAO,MAAM;QAG7B,KAAK;AACD,mBAAS,KAAK,MAAM,UAAU,KAAK,OAAO;AAC1C,eAAK,WAAW;AAChB,iBAAO,KAAK,KAAK,MAAM;QAC3B,KAAK;AACD,mBAAS,KAAK,MAAM,UAAU,KAAK,OAAO;AAC1C,eAAK,WAAW;AAChB,iBAAO,KAAK,KAAK,MAAM;;AAG/B,YAAM,IAAI,MAAM,iBAAiB;IACrC;AAEA,aAAS,OAAO,QAAQ,SAAS,GAAC;AAC9B,UAAI,UAAU,IAAI,QAAQ,QAAQ,MAAM;AACxC,UAAI,QAAQ,QAAQ,OAAM;AAC1B,UAAI,QAAQ,YAAY,OAAO,YAAY;AACvC,cAAM,IAAI,MAAO,OAAO,aAAa,QAAQ,UAAW,iBAAiB;;AAE7E,aAAO;IACX;AA2UiB,YAAA,SAAA;AArUjB,QAAI,sBAAsB,aAAc;AACxC,QAAI,sBAAsB,cAAc;AAExC,aAAS,UAAU,MAAM,QAAQ,KAAG;AAChC,UAAI,IAAI;AACR,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAI,GAAG,KAAK;AACxC,YAAI,IAAI,WAAW,CAAC;AACpB,YAAI,IAAI,KAAM;AACV,eAAK,SAAS,UAAU,CAAC;mBAEpB,IAAI,MAAO;AAChB,eAAK,SAAS,UAAU,MAAQ,KAAK,CAAE;AACvC,eAAK,SAAS,UAAU,MAAQ,IAAI,EAAK;mBAEpC,IAAI,SAAU,KAAK,OAAQ;AAChC,eAAK,SAAS,UAAU,MAAQ,KAAK,EAAG;AACxC,eAAK,SAAS,UAAU,MAAQ,KAAK,IAAK,EAAI;AAC9C,eAAK,SAAS,UAAU,MAAQ,IAAI,EAAK;eAExC;AACD;AACA,cAAI,UAAa,IAAI,SAAU,KAAO,IAAI,WAAW,CAAC,IAAI;AAC1D,eAAK,SAAS,UAAU,MAAQ,KAAK,EAAG;AACxC,eAAK,SAAS,UAAU,MAAQ,KAAK,KAAM,EAAI;AAC/C,eAAK,SAAS,UAAU,MAAQ,KAAK,IAAK,EAAI;AAC9C,eAAK,SAAS,UAAU,MAAQ,IAAI,EAAK;;;IAGrD;AAEA,aAAS,WAAW,KAAG;AACnB,UAAI,IAAI,GAAG,SAAS;AACpB,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAI,GAAG,KAAK;AACxC,YAAI,IAAI,WAAW,CAAC;AACpB,YAAI,IAAI,KAAM;AACV,oBAAU;mBAEL,IAAI,MAAO;AAChB,oBAAU;mBAEL,IAAI,SAAU,KAAK,OAAQ;AAChC,oBAAU;eAET;AACD;AACA,oBAAU;;;AAGlB,aAAO;IACX;AAEA,aAAS,QAAQ,OAAO,QAAQ,OAAK;AACjC,UAAI,OAAO,OAAO,OAAO,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG,KAAK,GAAG,SAAS,GAAG,OAAO;AAE1E,UAAI,SAAS,UAAU;AACnB,iBAAS,WAAW,KAAK;AAGzB,YAAI,SAAS,IAAM;AACf,gBAAM,KAAK,SAAS,GAAI;AACxB,iBAAO;mBAGF,SAAS,KAAO;AACrB,gBAAM,KAAK,KAAM,MAAM;AACvB,iBAAO;mBAGF,SAAS,OAAS;AACvB,gBAAM,KAAK,KAAM,UAAU,GAAG,MAAM;AACpC,iBAAO;mBAGF,SAAS,YAAa;AAC3B,gBAAM,KAAK,KAAM,UAAU,IAAI,UAAU,IAAI,UAAU,GAAG,MAAM;AAChE,iBAAO;eACJ;AACH,gBAAM,IAAI,MAAM,iBAAiB;;AAErC,eAAO,KAAK,EAAE,MAAM,OAAO,SAAS,QAAQ,SAAS,MAAM,OAAM,CAAE;AACnE,eAAO,OAAO;;AAElB,UAAI,SAAS,UAAU;AAInB,YAAI,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC,SAAS,KAAK,GAAG;AACjD,gBAAM,KAAK,GAAI;AACf,iBAAO,KAAK,EAAE,QAAQ,OAAO,SAAS,GAAG,SAAS,MAAM,OAAM,CAAE;AAChE,iBAAO;;AAGX,YAAI,SAAS,GAAG;AAEZ,cAAI,QAAQ,KAAM;AACd,kBAAM,KAAK,KAAK;AAChB,mBAAO;;AAGX,cAAI,QAAQ,KAAO;AACf,kBAAM,KAAK,KAAM,KAAK;AACtB,mBAAO;;AAGX,cAAI,QAAQ,OAAS;AACjB,kBAAM,KAAK,KAAM,SAAS,GAAG,KAAK;AAClC,mBAAO;;AAGX,cAAI,QAAQ,YAAa;AACrB,kBAAM,KAAK,KAAM,SAAS,IAAI,SAAS,IAAI,SAAS,GAAG,KAAK;AAC5D,mBAAO;;AAGX,eAAM,QAAQ,KAAK,IAAI,GAAG,EAAE,KAAM;AAClC,eAAK,UAAU;AACf,gBAAM,KAAK,KAAM,MAAM,IAAI,MAAM,IAAI,MAAM,GAAG,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,GAAG,EAAE;AACjF,iBAAO;eACJ;AAEH,cAAI,SAAS,KAAO;AAChB,kBAAM,KAAK,KAAK;AAChB,mBAAO;;AAGX,cAAI,SAAS,MAAO;AAChB,kBAAM,KAAK,KAAM,KAAK;AACtB,mBAAO;;AAGX,cAAI,SAAS,QAAS;AAClB,kBAAM,KAAK,KAAM,SAAS,GAAG,KAAK;AAClC,mBAAO;;AAGX,cAAI,SAAS,aAAa;AACtB,kBAAM,KAAK,KAAM,SAAS,IAAI,SAAS,IAAI,SAAS,GAAG,KAAK;AAC5D,mBAAO;;AAGX,eAAK,KAAK,MAAM,QAAQ,KAAK,IAAI,GAAG,EAAE,CAAC;AACvC,eAAK,UAAU;AACf,gBAAM,KAAK,KAAM,MAAM,IAAI,MAAM,IAAI,MAAM,GAAG,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,GAAG,EAAE;AACjF,iBAAO;;;AAGf,UAAI,SAAS,UAAU;AAEnB,YAAI,UAAU,MAAM;AAChB,gBAAM,KAAK,GAAI;AACf,iBAAO;;AAGX,YAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,mBAAS,MAAM;AAGf,cAAI,SAAS,IAAM;AACf,kBAAM,KAAK,SAAS,GAAI;AACxB,mBAAO;qBAGF,SAAS,OAAS;AACvB,kBAAM,KAAK,KAAM,UAAU,GAAG,MAAM;AACpC,mBAAO;qBAGF,SAAS,YAAa;AAC3B,kBAAM,KAAK,KAAM,UAAU,IAAI,UAAU,IAAI,UAAU,GAAG,MAAM;AAChE,mBAAO;iBACJ;AACH,kBAAM,IAAI,MAAM,iBAAiB;;AAErC,eAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AACzB,oBAAQ,QAAQ,OAAO,QAAQ,MAAM,CAAC,CAAC;;AAE3C,iBAAO;;AAGX,YAAI,iBAAiB,MAAM;AACvB,cAAI,KAAK,MAAM,QAAO;AACtB,cAAI,IAAI,KAAK,MAAM,KAAK,GAAG;AAC3B,cAAI,MAAM,KAAK,IAAI,OAAO;AAE1B,cAAI,KAAK,KAAK,MAAM,KAAK,KAAK,qBAAqB;AAC/C,gBAAI,OAAO,KAAK,KAAK,qBAAqB;AAEtC,oBAAM,KAAK,KAAM,KAAM,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,CAAC;AAClD,qBAAO;mBACJ;AAEH,mBAAK,IAAI;AACT,mBAAK,IAAI;AACT,oBAAM,KAAK,KAAM,KAAM,MAAM,IAAI,MAAM,IAAI,MAAM,GAAG,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,GAAG,EAAE;AACvF,qBAAO;;iBAER;AAEH,iBAAK,KAAK,MAAM,IAAI,UAAW;AAC/B,iBAAK,MAAM;AACX,kBAAM,KAAK,KAAM,IAAM,KAAM,MAAM,IAAI,MAAM,IAAI,MAAM,GAAG,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,GAAG,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,GAAG,EAAE;AAC9H,mBAAO;;;AAIf,YAAI,iBAAiB,aAAa;AAC9B,mBAAS,MAAM;AAGf,cAAI,SAAS,KAAO;AAChB,kBAAM,KAAK,KAAM,MAAM;AACvB,mBAAO;qBAGH,SAAS,OAAS;AAClB,kBAAM,KAAK,KAAM,UAAU,GAAG,MAAM;AACpC,mBAAO;qBAGP,SAAS,YAAa;AACtB,kBAAM,KAAK,KAAM,UAAU,IAAI,UAAU,IAAI,UAAU,GAAG,MAAM;AAChE,mBAAO;iBACJ;AACH,kBAAM,IAAI,MAAM,kBAAkB;;AAE1C,iBAAO,KAAK,EAAE,MAAM,OAAO,SAAS,QAAQ,SAAS,MAAM,OAAM,CAAE;AACnE,iBAAO,OAAO;;AAGlB,YAAI,OAAO,MAAM,WAAW,YAAY;AACpC,iBAAO,QAAQ,OAAO,QAAQ,MAAM,OAAM,CAAE;;AAGhD,YAAI,OAAO,CAAA,GAAI,MAAM;AAErB,YAAI,UAAU,OAAO,KAAK,KAAK;AAC/B,aAAK,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,KAAK;AACxC,gBAAM,QAAQ,CAAC;AACf,cAAI,MAAM,GAAG,MAAM,UAAa,OAAO,MAAM,GAAG,MAAM,YAAY;AAC9D,iBAAK,KAAK,GAAG;;;AAGrB,iBAAS,KAAK;AAGd,YAAI,SAAS,IAAM;AACf,gBAAM,KAAK,SAAS,GAAI;AACxB,iBAAO;mBAGF,SAAS,OAAS;AACvB,gBAAM,KAAK,KAAM,UAAU,GAAG,MAAM;AACpC,iBAAO;mBAGF,SAAS,YAAa;AAC3B,gBAAM,KAAK,KAAM,UAAU,IAAI,UAAU,IAAI,UAAU,GAAG,MAAM;AAChE,iBAAO;eACJ;AACH,gBAAM,IAAI,MAAM,kBAAkB;;AAGtC,aAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AACzB,gBAAM,KAAK,CAAC;AACZ,kBAAQ,QAAQ,OAAO,QAAQ,GAAG;AAClC,kBAAQ,QAAQ,OAAO,QAAQ,MAAM,GAAG,CAAC;;AAE7C,eAAO;;AAGX,UAAI,SAAS,WAAW;AACpB,cAAM,KAAK,QAAQ,MAAO,GAAI;AAC9B,eAAO;;AAEX,UAAI,SAAS,aAAa;AACtB,cAAM,KAAK,GAAI;AACf,eAAO;;AAGX,UAAI,OAAO,MAAM,WAAW,YAAY;AACpC,eAAO,QAAQ,OAAO,QAAQ,MAAM,OAAM,CAAE;;AAEhD,YAAM,IAAI,MAAM,kBAAkB;IACtC;AAEA,aAAS,OAAO,OAAK;AACjB,UAAI,QAAQ,CAAA;AACZ,UAAI,SAAS,CAAA;AACb,UAAI,OAAO,QAAQ,OAAO,QAAQ,KAAK;AACvC,UAAI,MAAM,IAAI,YAAY,IAAI;AAC9B,UAAI,OAAO,IAAI,SAAS,GAAG;AAE3B,UAAI,aAAa;AACjB,UAAI,eAAe;AACnB,UAAI,aAAa;AACjB,UAAI,OAAO,SAAS,GAAG;AACnB,qBAAa,OAAO,CAAC,EAAE;;AAG3B,UAAI,OAAO,cAAc,GAAG,SAAS;AACrC,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC1C,aAAK,SAAS,eAAe,GAAG,MAAM,CAAC,CAAC;AACxC,YAAI,IAAI,MAAM,YAAY;AAAE;;AAC5B,gBAAQ,OAAO,UAAU;AACzB,sBAAc,MAAM;AACpB,iBAAS,eAAe;AACxB,YAAI,MAAM,MAAM;AACZ,cAAI,MAAM,IAAI,WAAW,MAAM,IAAI;AACnC,mBAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AAClC,iBAAK,SAAS,SAAS,GAAG,IAAI,CAAC,CAAC;;mBAE7B,MAAM,MAAM;AACnB,oBAAU,MAAM,QAAQ,MAAM,IAAI;mBAC3B,MAAM,WAAW,QAAW;AACnC,eAAK,WAAW,QAAQ,MAAM,MAAM;;AAExC;AACA,wBAAgB;AAChB,YAAI,OAAO,UAAU,GAAG;AACpB,uBAAa,OAAO,UAAU,EAAE;;;AAGxC,aAAO;IACX;AAES,YAAA,SAAA;;;;;AC3pBT;AAAA;AAAA;AAEA,WAAO,UAAU,WAAY;AAC3B,YAAM,IAAI;AAAA,QACR;AAAA,MAEF;AAAA,IACF;AAAA;AAAA;;;;;;;;;;;ACPA,QAAA,OAAA,gBAAA,iBAAA;AAGA,QAAM,YAAY,WAAW,aAAa,KAAA;AAE1C,QAAa,qBAAb,MAA+B;MAI3B,YAAmB,QAA0B;AAA1B,aAAA,SAAA;MAA6B;MAEzC,KAAK,MAAiC;AACzC,YAAI,gBAAgB,aAAa;AAC7B,eAAK,GAAG,KAAK,IAAI;mBAEV,MAAM,QAAQ,IAAI,GAAG;AAC5B,eAAK,GAAG,KAAM,IAAI,WAAW,IAAI,EAAG,MAAM;;MAElD;;;;;MAMO,QAAQ,KAAa,SAAa;AACrC,YAAI;AAEA,eAAK,KAAK,IAAI,UAAU,KAAK,EAAE,SAAS,WAAW,KAAK,UAAS,CAAE;iBAE9D,GAAG;AAER,eAAK,KAAK,IAAI,UAAU,KAAK,KAAK,SAAS;;AAG/C,aAAK,GAAG,aAAa;AACrB,aAAK,GAAG,SAAS,KAAK,OAAO;AAC7B,aAAK,GAAG,YAAY,KAAK,OAAO;AAChC,aAAK,GAAG,UAAU,KAAK,OAAO;AAC9B,aAAK,GAAG,UAAU,KAAK,OAAO;MAClC;MAEO,MAAM,MAAe,QAAe;AACvC,aAAK,GAAG,MAAM,MAAM,MAAM;MAC9B;MAEA,IAAI,SAAM;AACN,eAAO,KAAK,GAAG,eAAe,UAAU;MAC5C;;AA1CJ,YAAA,qBAAA;;;;;;;;;;ACJA,QAAA,uBAAA;AAEA,QAAa,aAAb,MAAuB;MAInB,cAAA;AAFA,aAAA,SAA6B,CAAA;AAGzB,aAAK,YAAY,IAAI,qBAAA,mBAAmB,KAAK,MAAM;MACvD;MAEA,KAAK,MAAiC;AAClC,aAAK,UAAU,KAAK,IAAI;MAC5B;MAGA,QAAQ,KAAa,SAAY;AAC7B,aAAK,UAAU,QAAQ,KAAK,OAAO;MACvC;MAEA,MAAM,MAAe,QAAe;AAChC,aAAK,UAAU,MAAM,MAAM,MAAM;MACrC;MAEA,IAAI,SAAM;AACN,eAAO,KAAK,UAAU;MAC1B;;AAvBJ,YAAA,aAAA;;;;;;;;;;ACDA,QAAY;AAAZ,KAAA,SAAYC,WAAQ;AAEhB,MAAAA,UAAAA,UAAA,WAAA,IAAA,CAAA,IAAA;AACA,MAAAA,UAAAA,UAAA,WAAA,IAAA,EAAA,IAAA;AACA,MAAAA,UAAAA,UAAA,OAAA,IAAA,EAAA,IAAA;AACA,MAAAA,UAAAA,UAAA,YAAA,IAAA,EAAA,IAAA;AACA,MAAAA,UAAAA,UAAA,WAAA,IAAA,EAAA,IAAA;AACA,MAAAA,UAAAA,UAAA,YAAA,IAAA,EAAA,IAAA;AACA,MAAAA,UAAAA,UAAA,kBAAA,IAAA,EAAA,IAAA;AACA,MAAAA,UAAAA,UAAA,kBAAA,IAAA,EAAA,IAAA;AACA,MAAAA,UAAAA,UAAA,iBAAA,IAAA,EAAA,IAAA;IACJ,GAXY,WAAA,QAAA,aAAA,QAAA,WAAQ,CAAA,EAAA;AAapB,QAAY;AAAZ,KAAA,SAAYC,YAAS;AACjB,MAAAA,WAAAA,WAAA,sBAAA,IAAA,IAAA,IAAA;AACA,MAAAA,WAAAA,WAAA,4BAAA,IAAA,IAAA,IAAA;AACA,MAAAA,WAAAA,WAAA,2BAAA,IAAA,IAAA,IAAA;AACA,MAAAA,WAAAA,WAAA,qBAAA,IAAA,IAAA,IAAA;AACA,MAAAA,WAAAA,WAAA,mBAAA,IAAA,IAAA,IAAA;AAEA,MAAAA,WAAAA,WAAA,aAAA,IAAA,IAAA,IAAA;AACA,MAAAA,WAAAA,WAAA,mBAAA,IAAA,IAAA,IAAA;IACJ,GATY,YAAA,QAAA,cAAA,QAAA,YAAS,CAAA,EAAA;AAWrB,aAAgB,SAAS,MAAgB,QAAc;AACrD,YAAM,SAAS,KAAK,QAAQ;AAE5B,UAAI,SAAS,IAAI,MAAM;AACvB,eAAS,IAAI,QAAQ,MAAM,SAAS,QAAQ,IAAI,KAAK,KAAK;AACxD,YAAI,OAAO,KAAK,CAAC;AACjB,aAAK,OAAO,SAAU,GAAM;AAC1B,oBAAU,OAAO,aAAa,IAAI;AAClC;;AAEF,aAAK,OAAO,SAAU,KAAM;AAC1B,oBAAU,OAAO,cACb,OAAO,OAAS,IACjB,KAAK,EAAE,CAAC,IAAI,EAAK;AAEpB;;AAEF,aAAK,OAAO,SAAU,KAAM;AAC1B,oBAAU,OAAO,cACb,OAAO,OAAS,MAChB,KAAK,EAAE,CAAC,IAAI,OAAS,KACrB,KAAK,EAAE,CAAC,IAAI,OAAS,CAAE;AAE3B;;AAEF,aAAK,OAAO,SAAU,KAAM;AAC1B,iBAAQ,OAAO,MAAS,MACpB,KAAK,EAAE,CAAC,IAAI,OAAS,MACrB,KAAK,EAAE,CAAC,IAAI,OAAS,KACrB,KAAK,EAAE,CAAC,IAAI,OAAS;AACzB,cAAI,OAAO,OAAU;AACnB,mBAAO;AACP,sBAAU,OAAO,cAAc,QAAQ,MAAM,QAAS,MAAM,QAAS,KAAM;iBACtE;AACL,sBAAU,OAAO,aAAa,GAAG;;AAEnC;;AAEF,cAAM,IAAI,MAAM,kBAAkB,KAAK,SAAS,EAAE,CAAC;;AAErD,aAAO;IACT;AAzCA,YAAA,WAAA;AA4CA,aAAgB,WAAW,MAAc,IAAE;AACzC,UAAI,IAAI;AACR,UAAI,SAAS;AACb,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAI,GAAG,KAAK;AAC1C,YAAI,IAAI,WAAW,CAAC;AACpB,YAAI,IAAI,KAAM;AACZ,oBAAU;mBACD,IAAI,MAAO;AACpB,oBAAU;mBACD,IAAI,SAAU,KAAK,OAAQ;AACpC,oBAAU;eACL;AACL;AACA,oBAAU;;;AAGd,aAAO,SAAS;IAClB;AAjBA,YAAA,aAAA;;;;;;;;;;AC5DA,QAAM,cAAqC,CAAA;AAE3C,aAAgB,mBAAoB,IAAY,YAAe;AAC3D,kBAAY,EAAE,IAAI;IACtB;AAFA,YAAA,qBAAA;AAIA,aAAgB,cAAe,IAAU;AACrC,YAAM,aAAa,YAAY,EAAE;AACjC,UAAI,CAAC,YAAY;AAAE,cAAM,IAAI,MAAM,yBAAyB,EAAE;;AAC9D,aAAO;IACX;AAJA,YAAA,gBAAA;;;;;;;;;;ACOO,QAAM,mBAAmB,OAAO;MACnC,KAAK,UAAkB,MAAW;AAC9B,YAAI,YAAY,KAAK,OAAO,KAAK,KAAK,CAAA;AACtC,iBAAS,IAAI,GAAG,SAAS,UAAU,QAAQ,IAAI,QAAQ,KAAK;AACxD,oBAAU,CAAC,EAAE,GAAG,IAAI;;MAE5B;MACA,QAAQ,CAAA;MACR,GAAG,OAAe,IAA4B;;AAC1C,UAAA,KAAA,KAAK,OAAO,KAAK,OAAC,QAAA,OAAA,SAAA,SAAA,GAAE,KAAK,EAAE,OAAM,KAAK,OAAO,KAAK,IAAI,CAAC,EAAE;AACzD,eAAO,MAAK;;AACR,eAAK,OAAO,KAAK,KAAIC,MAAA,KAAK,OAAO,KAAK,OAAC,QAAAA,QAAA,SAAA,SAAAA,IAAE,OAAO,OAAK,OAAO,CAAC;QACjE;MACJ;;AAbS,YAAA,mBAAgB;;;;;;;;;;AClB7B,QAAa,eAAb,MAAyB;MAAzB,cAAA;AACE,aAAA,WAAqC,CAAA;MAwBvC;MAtBE,SAAS,IAAuB,OAAgB,OAAK;AACnD,aAAK,SAAS,KAAK,EAAE;AACrB,eAAO;MACT;MAEA,UAAU,MAA2C;AACnD,aAAK,SAAS,QAAQ,CAAC,YAAY,QAAQ,MAAM,MAAM,IAAI,CAAC;MAC9D;MAEA,eAAe,MAA2C;AACxD,eAAO,QAAQ,IAAI,KAAK,SAAS,IAAI,CAAC,YAAY,QAAQ,MAAM,MAAM,IAAI,CAAC,CAAC;MAC9E;MAEA,OAAQ,IAAqB;AAC3B,cAAM,QAAQ,KAAK,SAAS,QAAQ,EAAE;AACtC,aAAK,SAAS,KAAK,IAAI,KAAK,SAAS,KAAK,SAAS,SAAS,CAAC;AAC7D,aAAK,SAAS,IAAG;MACnB;MAEA,QAAK;AACH,aAAK,WAAW,CAAA;MAClB;;AAxBF,YAAA,eAAA;AA2BA,aAAgB,eAAY;AAU1B,YAAM,UAAU,IAAI,aAAY;AAEhC,eAAS,SAAoB,IAAqB;AAChD,eAAO,QAAQ,SAAS,IAAI,SAAS,IAAI;MAC3C;AAAC;AAED,eAAS,OAAO,CAAC,OAAyB;AACxC,cAAM,WAAgB,YAAa,MAAW;AAC5C,aAAG,MAAM,MAAM,IAAI;AACnB,kBAAQ,OAAO,QAAQ;QACzB;AACA,gBAAQ,SAAS,QAAQ;MAC3B;AACA,eAAS,SAAS,CAAC,OAA0B,QAAQ,OAAO,EAAE;AAC9D,eAAS,SAAS,IAAI,SAAgD,QAAQ,OAAO,GAAG,IAAI;AAC5F,eAAS,cAAc,IAAI,SAAgD,QAAQ,YAAY,GAAG,IAAI;AACtG,eAAS,QAAQ,MAAM,QAAQ,MAAK;AAEpC,aAAO;IACT;AA7BA,YAAA,eAAA;;;;;AChCA;AAAA;AAAA,KAAC,SAAU,QAAQ,SAAS;AACxB,aAAO,YAAY,YAAY,OAAO,WAAW,cAAc,QAAQ,OAAO,IAC9E,OAAO,WAAW,cAAc,OAAO,MAAM,OAAO,CAAC,SAAS,GAAG,OAAO,KACvE,SAAS,OAAO,eAAe,cAAc,aAAa,UAAU,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAC;AAAA,IACzG,GAAG,SAAO,SAAUC,UAAS;AAAE;AAkB3B,UAAI,gBAAgB,SAAS,GAAG,GAAG;AAC/B,wBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUC,IAAGC,IAAG;AAAE,UAAAD,GAAE,YAAYC;AAAA,QAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,mBAAS,KAAKA;AAAG,gBAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC;AAAG,cAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,QAAG;AACpG,eAAO,cAAc,GAAG,CAAC;AAAA,MAC7B;AAEA,eAAS,UAAU,GAAG,GAAG;AACrB,YAAI,OAAO,MAAM,cAAc,MAAM;AACjC,gBAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,sBAAc,GAAG,CAAC;AAClB,iBAAS,KAAK;AAAE,eAAK,cAAc;AAAA,QAAG;AACtC,UAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AAAA,MACtF;AAEA,eAAS,WAAW,YAAY,QAAQ,KAAK,MAAM;AAC/C,YAAI,IAAI,UAAU,QAAQ,IAAI,IAAI,IAAI,SAAS,SAAS,OAAO,OAAO,OAAO,yBAAyB,QAAQ,GAAG,IAAI,MAAM;AAC3H,YAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,aAAa;AAAY,cAAI,QAAQ,SAAS,YAAY,QAAQ,KAAK,IAAI;AAAA;AACxH,mBAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG;AAAK,gBAAI,IAAI,WAAW,CAAC;AAAG,mBAAK,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,IAAI,EAAE,QAAQ,KAAK,CAAC,IAAI,EAAE,QAAQ,GAAG,MAAM;AAChJ,eAAO,IAAI,KAAK,KAAK,OAAO,eAAe,QAAQ,KAAK,CAAC,GAAG;AAAA,MAChE;AAEA,eAAS,cAAc,IAAI,MAAM,MAAM;AACnC,YAAI,QAAQ,UAAU,WAAW;AAAG,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,IAAI,GAAG,KAAK;AACjF,gBAAI,MAAM,EAAE,KAAK,OAAO;AACpB,kBAAI,CAAC;AAAI,qBAAK,MAAM,UAAU,MAAM,KAAK,MAAM,GAAG,CAAC;AACnD,iBAAG,CAAC,IAAI,KAAK,CAAC;AAAA,YAClB;AAAA,UACJ;AACA,eAAO,GAAG,OAAO,MAAM,MAAM,UAAU,MAAM,KAAK,IAAI,CAAC;AAAA,MAC3D;AAEA,aAAO,oBAAoB,aAAa,kBAAkB,SAAU,OAAO,YAAY,SAAS;AAC5F,YAAI,IAAI,IAAI,MAAM,OAAO;AACzB,eAAO,EAAE,OAAO,mBAAmB,EAAE,QAAQ,OAAO,EAAE,aAAa,YAAY;AAAA,MACnF;AAGA,UAAI,sBAAsB;AAC1B,UAAI,UAAU;AAId,MAAAF,SAAQ,YAAY;AACpB,OAAC,SAAU,WAAW;AAElB,kBAAU,UAAU,KAAK,IAAI,GAAG,IAAI;AAEpC,kBAAU,UAAU,SAAS,IAAI,CAAC,IAAI;AAEtC,kBAAU,UAAU,QAAQ,IAAI,EAAE,IAAI;AAEtC,kBAAU,UAAU,gBAAgB,IAAI,GAAG,IAAI;AAG/C,kBAAU,UAAU,OAAO,IAAI,CAAC,IAAI;AAEpC,kBAAU,UAAU,OAAO,IAAI,EAAE,IAAI;AAAA,MACzC,GAAGA,SAAQ,cAAcA,SAAQ,YAAY,CAAC,EAAE;AAkBhD,UAAI;AAAA;AAAA,QAA4B,WAAY;AACxC,mBAASG,YAAW,KAAK,QAAQ,MAAM;AACnC,iBAAK,UAAU;AACf,iBAAK,UAAU,oBAAI,IAAI;AACvB,iBAAK,aAAa,oBAAI,IAAI;AAE1B,iBAAK,SAAS,CAAC;AACf,iBAAK,yBAAyB;AAC9B,iBAAK,MAAM;AACX,iBAAK,UAAU,QAAQ,IAAI;AAAA,UAC/B;AACA,UAAAA,YAAW,UAAU,YAAY,SAAU,QAAQ,MAAM,aAAa;AAClE,gBAAI,QAAQ;AACZ,gBAAI,CAAC,KAAK,SAAS;AACf,mBAAK,UAAW,KAAK,eAAe,SAC9B,KAAK,IAAI,aAAa,EAAE,UACxB,CAAC;AAAA,YACX;AACA,iBAAK,SAAS;AACd,iBAAK,cAAc;AAEnB,gBAAI,CAAC,MAAM;AACP;AAAA,YACJ;AACA,iBAAK,OAAO;AAIZ,gBAAI,KAAK,eAAe,QAAQ;AAC5B,kBAAI,aAAa,KAAK,IAAI,aAAa;AACvC,uBAAS,SAAS,WAAW,QAAQ;AACjC,oBAAI,QAAQ,KAAK,IAAI,KAAK;AAC1B,oBAAI,SAAS,MAAM,UAAU,GAAG;AAC5B,sBAAI,gBAAgB,WAAW,QAAQ,KAAK;AAC5C,wBAAM,UAAU,EAAE,UAAU,KAAK,KAAK,MAAM,aAAa;AAAA,gBAC7D;AAAA,cACJ;AAAA,YACJ,WACS,OAAQ,KAAK,QAAS,UAAU;AACrC,mBAAK,IAAI,QAAQ,SAAUC,QAAO,KAAK;AACnC,oBAAIA,kBAAiB,QAAQ;AACzB,sBAAI,cAAcA,OAAM,UAAU;AAClC,sBAAI,gBAAgB,MAAM,IAAI,UAAU,EAAE,QAAQ,GAAG;AACrD,8BAAY,UAAU,MAAM,KAAK,MAAM,MAAM,aAAa;AAAA,gBAC9D;AAAA,cACJ,CAAC;AAAA,YACL;AAAA,UACJ;AACA,UAAAD,YAAW,UAAU,YAAY,SAAU,IAAI;AAC3C,iBAAK,QAAQ,IAAI,EAAE,KAAK,wBAAwB,EAAE;AAAA,UACtD;AACA,UAAAA,YAAW,UAAU,SAAS,SAAU,WAAW,WAAW;AAC1D,gBAAI,cAAc,QAAQ;AAAE,0BAAYH,SAAQ,UAAU;AAAA,YAAK;AAC/D,gBAAI,QAAS,OAAQ,cAAe,WAC9B,YACA,KAAK,QAAQ,SAAS;AAC5B,iBAAK,iBAAiB,OAAO,SAAS;AACtC,gBAAI,iBAAiB,KAAK,QAAQ,IAAI,KAAK;AAC3C,gBAAI,CAAC,kBACD,eAAe,OAAOA,SAAQ,UAAU,UACxC,eAAe,OAAOA,SAAQ,UAAU,OAC1C;AACE,mBAAK,QAAQ,IAAI,OAAO;AAAA,gBACpB,IAAK,CAAC,iBACA,YACC,eAAe,OAAOA,SAAQ,UAAU,SACrCA,SAAQ,UAAU,iBAClB;AAAA;AAAA,gBAEV;AAAA,cACJ,CAAC;AAAA,YACL;AACA,iBAAK,WAAW,IAAI,KAAK;AACzB,iBAAK,UAAU;AACf,iBAAK,aAAa;AAAA,UACtB;AACA,UAAAG,YAAW,UAAU,QAAQ,SAAU,WAAW;AAC9C,gBAAI,QAAS,OAAQ,cAAe,WAC9B,YACA,KAAK,QAAQ,SAAS;AAC5B,iBAAK,iBAAiB,OAAO,SAAS;AACtC,gBAAI,CAAC,KAAK,QAAQ,IAAI,KAAK,GAAG;AAC1B,mBAAK,QAAQ,IAAI,OAAO,EAAE,IAAIH,SAAQ,UAAU,OAAO,MAAa,CAAC;AAAA,YACzE;AACA,iBAAK,WAAW,IAAI,KAAK;AAEzB,iBAAK,aAAa;AAAA,UACtB;AACA,UAAAG,YAAW,UAAU,eAAe,WAAY;AAC5C,gBAAI,KAAK,QAAQ;AACb,mBAAK,OAAO,UAAU,EAAE,MAAM,KAAK,WAAW;AAAA,YAClD;AAAA,UACJ;AACA,UAAAA,YAAW,UAAU,UAAU,SAAU,OAAO;AAC5C,gBAAI,KAAK,IAAI,aAAa,GAAG;AACzB,kBAAI,aAAa,KAAK,IAAI,aAAa;AACvC,qBAAO,WAAW,OAAO,WAAW,cAAc,KAAK,CAAC;AAAA,YAC5D,OACK;AACD,kBAAI,aAAa,KAAK,OAAO,aAAa;AAC1C,kBAAI,aAAa,WAAW,OAAO,WAAW,cAAc,KAAK,WAAW,CAAC;AAO7E,qBAAO,OAAO,OAAO,UAAU,EAAE,CAAC;AAAA,YACtC;AAAA,UACJ;AACA,UAAAA,YAAW,UAAU,oBAAoB,WAAY;AACjD,gBAAI,eAAe,KAAK,OAAO,aAAa,EAAE;AAC9C,mBAAO,gBAAgB,aAAa,KAAK,WAAW;AAAA,UACxD;AAIA,UAAAA,YAAW,UAAU,WAAW,SAAU,OAAO;AAC7C,mBAAO,KAAK,IAAI,YAAY,EAAE,KAAK;AAAA,UACvC;AACA,UAAAA,YAAW,UAAU,SAAS,SAAU,WAAW;AAC/C,gBAAI,QAAS,OAAQ,cAAe,WAC9B,YACA,KAAK,QAAQ,SAAS;AAC5B,gBAAI,UAAU,QAAW;AACrB,sBAAQ,KAAK,oBAAoB,OAAO,KAAK,IAAI,YAAY,MAAM,yCAAyC,EAAE,OAAO,WAAW,IAAI,EAAE,OAAO,OAAO,GAAG,CAAC;AACxJ;AAAA,YACJ;AACA,gBAAI,gBAAgB,KAAK,SAAS,KAAK;AAEvC,iBAAK,QAAQ,IAAI,OAAO,EAAE,IAAIH,SAAQ,UAAU,QAAQ,MAAa,CAAC;AACtE,iBAAK,WAAW,OAAO,KAAK;AAE5B,mBAAO,KAAK,OAAO,KAAK;AAExB,gBAAI,iBAAiB,cAAc,UAAU,GAAG;AAC5C,4BAAc,UAAU,EAAE,SAAS;AAAA,YACvC;AACA,iBAAK,UAAU;AACf,iBAAK,aAAa;AAAA,UACtB;AACA,UAAAG,YAAW,UAAU,UAAU,SAAU,SAAS,YAAY;AAC1D,gBAAI,QAAQ;AACZ,gBAAI,YAAY,QAAQ;AAAE,wBAAU;AAAA,YAAO;AAC3C,gBAAI,eAAe,QAAQ;AAAE,2BAAa;AAAA,YAAO;AAQjD,gBAAI,EAAE,KAAK,eAAe,SAAS;AAC/B,mBAAK,QAAQ,QAAQ,SAAU,QAAQ;AACnC,oBAAI,OAAO,OAAOH,SAAQ,UAAU,QAAQ;AACxC,sBAAI,QAAQ,MAAM,IAAI,UAAU,EAAE,OAAO,KAAK;AAC9C,yBAAO,MAAM,QAAQ,KAAK;AAAA,gBAC9B;AAAA,cACJ,CAAC;AAAA,YACL;AACA,iBAAK,QAAQ,MAAM;AACnB,iBAAK,UAAU;AACf,gBAAI,YAAY;AACZ,mBAAK,WAAW,MAAM;AAAA,YAC1B;AAEA,iBAAK,yBAAyB;AAAA,UAClC;AAIA,UAAAG,YAAW,UAAU,aAAa,WAAY;AAC1C,gBAAI,QAAQ;AACZ,iBAAK,QAAQ,QAAQ,SAAU,QAAQ;AACnC,kBAAI,QAAQ,MAAM,SAAS,OAAO,KAAK;AACvC,kBAAI,SAAS,MAAM,UAAU,GAAG;AAC5B,sBAAM,UAAU,EAAE,WAAW;AAAA,cACjC;AAAA,YACJ,CAAC;AACD,iBAAK,QAAQ;AAAA,UACjB;AAEA,UAAAA,YAAW,UAAU,QAAQ,SAAU,OAAO,aAAa;AACvD,iBAAK,OAAO,KAAK,IAAI;AAAA,UACzB;AACA,UAAAA,YAAW,UAAU,QAAQ,WAAY;AACrC,mBAAO,IAAIA,YAAW,KAAK,KAAK,KAAK,QAAQ,KAAK,IAAI;AAAA,UAC1D;AACA,UAAAA,YAAW,UAAU,cAAc,WAAY;AAE3C,gBAAI,KAAK,UAAU,QAAW;AAC1B;AAAA,YACJ;AACA,iBAAK,QAAQ,KAAK,KAAK,gBAAgB;AAAA,UAC3C;AACA,UAAAA,YAAW,UAAU,mBAAmB,SAAU,OAAO,WAAW;AAChE,gBAAI,UAAU,QAAW;AACrB,oBAAM,IAAI,MAAM,wCAAyC,OAAO,WAAW,GAAI,CAAC;AAAA,YACpF;AAAA,UACJ;AACA,iBAAOA;AAAA,QACX,EAAE;AAAA;AAEF,eAAS,YAAY,YAAY,IAAI,UAAU,UAAU;AAErD,YAAI,CAAC,WAAW,EAAE,GAAG;AACjB,qBAAW,EAAE,IAAI,CAAC;AAAA,QACtB;AACA,mBAAW,EAAE,EAAE,KAAK,QAAQ;AAM5B,qBAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,QAAQ,SAAU,MAAM,KAAK;AAAE,iBAAO,SAAS,MAAM,GAAG;AAAA,QAAG,CAAC;AACzH,eAAO,WAAY;AAAE,iBAAO,UAAU,WAAW,EAAE,GAAG,WAAW,EAAE,EAAE,QAAQ,QAAQ,CAAC;AAAA,QAAG;AAAA,MAC7F;AACA,eAAS,gBAAgB,SAAS;AAC9B,YAAI,QAAQ;AACZ,YAAI,gBAAiB,OAAQ,KAAK,SAAS,QAAQ,MAAO;AAC1D,aAAK,OAAO,QAAQ,SAAU,MAAM,KAAK;AACrC,kBAAQ,KAAK;AAAA,YACT,OAAO,MAAM,SAAS;AAAA,YACtB,IAAIH,SAAQ,UAAU;AAAA,YACtB,OAAO;AAAA,YACP,OAAO;AAAA,YACP,eAAe;AAAA,UACnB,CAAC;AACD,cAAI,eAAe;AACf,kBAAM,SAAS,KAAK,UAAU,KAAK,UAAU,EAAE,KAAK;AAAA,UACxD;AAAA,QACJ,CAAC;AAAA,MACL;AACA,eAAS,UAAU,KAAK,OAAO;AAE3B,YAAI,UAAU,MAAM,SAAS,IAAI,QAAQ;AACrC,iBAAO;AAAA,QACX;AACA,YAAI,MAAM,IAAI,SAAS;AACvB,iBAAS,IAAI,OAAO,IAAI,KAAK,KAAK;AAC9B,cAAI,CAAC,IAAI,IAAI,IAAI,CAAC;AAAA,QACtB;AACA,YAAI,SAAS;AACb,eAAO;AAAA,MACX;AAEA,UAAI,eAAe,SAAU,GAAG,GAAG;AAC/B,YAAI,IAAI,EAAE,SAAS;AACnB,YAAI,IAAI,EAAE,SAAS;AACnB,YAAI,IAAI;AACJ,iBAAO;AAAA,iBACF,IAAI;AACT,iBAAO;AAAA;AAEP,iBAAO;AAAA,MACf;AACA,eAAS,cAAc,OAAO;AAC1B,cAAM,QAAQ,IAAI;AAOlB,gBAAQ,IAAI,MAAM,OAAO;AAAA,UACrB,KAAK,SAAU,KAAK,MAAM;AACtB,gBAAI,OAAQ,SAAU,YAClB,CAAC,MAAM,IAAI,GACb;AACE,qBAAO,IAAI,GAAG,IAAI;AAAA,YACtB,OACK;AACD,qBAAO,IAAI,IAAI;AAAA,YACnB;AAAA,UACJ;AAAA,UACA,KAAK,SAAU,KAAK,MAAM,UAAU;AAChC,gBAAI,OAAQ,SAAU,YAClB,CAAC,MAAM,IAAI,GAAG;AACd,kBAAI,UAAU,MAAM,KAAK,IAAI,QAAQ,EAAE,KAAK,CAAC;AAC7C,kBAAI,MAAM,SAAS,QAAQ,IAAI,KAAK,IAAI;AACxC,kBAAI,aAAa,UAAa,aAAa,MAAM;AAC7C,oBAAI,SAAS,GAAG;AAAA,cACpB,OACK;AACD,oBAAI,MAAM,KAAK,QAAQ;AAAA,cAC3B;AAAA,YACJ,OACK;AACD,kBAAI,IAAI,IAAI;AAAA,YAChB;AACA,mBAAO;AAAA,UACX;AAAA,UACA,gBAAgB,SAAU,KAAK,MAAM;AACjC,gBAAI,OAAQ,SAAU,UAAU;AAC5B,kBAAI,SAAS,IAAI;AAAA,YACrB,OACK;AACD,qBAAO,IAAI,IAAI;AAAA,YACnB;AACA,mBAAO;AAAA,UACX;AAAA,UACA,KAAK,SAAU,KAAK,KAAK;AACrB,gBAAI,OAAQ,QAAS,YACjB,CAAC,MAAM,OAAO,GAAG,CAAC,GAAG;AACrB,qBAAO,IAAI,QAAQ,EAAE,IAAI,OAAO,GAAG,CAAC;AAAA,YACxC;AACA,mBAAO,QAAQ,IAAI,KAAK,GAAG;AAAA,UAC/B;AAAA,QACJ,CAAC;AACD,eAAO;AAAA,MACX;AACA,UAAI;AAAA;AAAA,QAA6B,WAAY;AACzC,mBAASK,eAAc;AACnB,gBAAI,QAAQ,CAAC;AACb,qBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,oBAAM,EAAE,IAAI,UAAU,EAAE;AAAA,YAC5B;AACA,iBAAK,WAAW,IAAI,WAAW,IAAI;AACnC,iBAAK,SAAS,oBAAI,IAAI;AACtB,iBAAK,WAAW,oBAAI,IAAI;AACxB,iBAAK,SAAS;AACd,iBAAK,KAAK,MAAM,MAAM,KAAK;AAAA,UAC/B;AACA,UAAAA,aAAY,UAAU,QAAQ,SAAU,UAAU,YAAY;AAC1D,gBAAI,eAAe,QAAQ;AAAE,2BAAa;AAAA,YAAM;AAChD,mBAAO,YAAa,KAAK,eAAe,KAAK,aAAa,CAAC,IAAKL,SAAQ,UAAU,KAAK,UAAW,aAC5F,KAAK,SACL,MAAS;AAAA,UACnB;AACA,UAAAK,aAAY,UAAU,WAAW,SAAU,UAAU;AAAE,mBAAO,YAAY,KAAK,eAAe,KAAK,aAAa,CAAC,IAAIL,SAAQ,UAAU,QAAQ,QAAQ;AAAA,UAAG;AAC1J,UAAAK,aAAY,UAAU,WAAW,SAAU,UAAU;AAAE,mBAAO,YAAY,KAAK,eAAe,KAAK,aAAa,CAAC,IAAIL,SAAQ,UAAU,SAAS,QAAQ;AAAA,UAAG;AAC3J,UAAAK,aAAY,KAAK,SAAUC,OAAM;AAC7B;AAAA;AAAA,cAEA,MAAM,QAAQA,KAAI;AAAA,cAEbA,MAAK,OAAO,MAAM;AAAA;AAAA,UAC3B;AACA,iBAAO,eAAeD,aAAY,WAAW,UAAU;AAAA,YACnD,KAAK,WAAY;AACb,qBAAO,KAAK,OAAO;AAAA,YACvB;AAAA,YACA,KAAK,SAAU,OAAO;AAClB,kBAAI,UAAU,GAAG;AACb,qBAAK,MAAM;AAAA,cACf,OACK;AACD,qBAAK,OAAO,OAAO,KAAK,SAAS,KAAK;AAAA,cAC1C;AAAA,YACJ;AAAA,YACA,YAAY;AAAA,YACZ,cAAc;AAAA,UAClB,CAAC;AACD,UAAAA,aAAY,UAAU,OAAO,WAAY;AACrC,gBAAI,QAAQ;AACZ,gBAAI,SAAS,CAAC;AACd,qBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,qBAAO,EAAE,IAAI,UAAU,EAAE;AAAA,YAC7B;AACA,gBAAI;AACJ,mBAAO,QAAQ,SAAU,OAAO;AAE5B,0BAAY,MAAM;AAClB,oBAAM,MAAM,WAAW,KAAK;AAAA,YAChC,CAAC;AACD,mBAAO;AAAA,UACX;AAIA,UAAAA,aAAY,UAAU,MAAM,WAAY;AACpC,gBAAI,MAAM,MAAM,KAAK,KAAK,SAAS,OAAO,CAAC,EAAE,IAAI;AACjD,gBAAI,QAAQ,QAAW;AACnB,qBAAO;AAAA,YACX;AACA,iBAAK,SAAS,OAAO,GAAG;AACxB,iBAAK,SAAS,OAAO,GAAG;AACxB,gBAAI,QAAQ,KAAK,OAAO,IAAI,GAAG;AAC/B,iBAAK,OAAO,OAAO,GAAG;AACtB,mBAAO;AAAA,UACX;AACA,UAAAA,aAAY,UAAU,KAAK,SAAU,OAAO;AAIxC,oBAAQ,KAAK,MAAM,KAAK,KAAK;AAE7B,gBAAI,QAAQ;AACR,uBAAS,KAAK;AAElB,gBAAI,QAAQ,KAAK,SAAS,KAAK;AAC3B,qBAAO;AACX,gBAAI,MAAM,MAAM,KAAK,KAAK,OAAO,KAAK,CAAC,EAAE,KAAK;AAC9C,mBAAO,KAAK,OAAO,IAAI,GAAG;AAAA,UAC9B;AACA,UAAAA,aAAY,UAAU,QAAQ,SAAU,OAAO,OAAO;AAClD,gBAAI,IAAI;AACR,gBAAI,UAAU,UAAa,UAAU,MAAM;AACvC,sBAAQ,MAAM,gFAAgF;AAC9F;AAAA,YACJ;AAEA,gBAAI,KAAK,OAAO,IAAI,KAAK,MAAM,OAAO;AAClC;AAAA,YACJ;AACA,gBAAI,MAAM,UAAU,MAAM,QAAW;AACjC,oBAAM,UAAU,EAAE,UAAU,MAAM,KAAK,SAAS,MAAM,KAAK;AAAA,YAC/D;AACA,gBAAI,aAAa,MAAM,KAAK,KAAK,SAAS,QAAQ,KAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,QAAQ,OAAO,SAAS,KAAKL,SAAQ,UAAU;AACzJ,iBAAK,SAAS,QAAQ,KAAK,IAAI;AAC/B,iBAAK,SAAS,IAAI,OAAO,KAAK;AAC9B,iBAAK,OAAO,IAAI,OAAO,KAAK;AAC5B,iBAAK,SAAS,OAAO,OAAO,SAAS;AAAA,UACzC;AACA,UAAAK,aAAY,UAAU,WAAW,SAAU,OAAO;AAC9C,gBAAI,MAAM,MAAM,KAAK,KAAK,OAAO,KAAK,CAAC,EAAE,KAAK;AAC9C,gBAAI,QAAQ,QAAW;AACnB,qBAAO;AAAA,YACX;AACA,mBAAO,KAAK,UAAU,GAAG;AAAA,UAC7B;AACA,UAAAA,aAAY,UAAU,YAAY,SAAU,OAAO;AAE/C,iBAAK,SAAS,OAAO,KAAK;AAC1B,iBAAK,SAAS,OAAO,KAAK;AAC1B,mBAAO,KAAK,OAAO,OAAO,KAAK;AAAA,UACnC;AACA,UAAAA,aAAY,UAAU,QAAQ,SAAU,SAAS;AAE7C,iBAAK,SAAS,QAAQ,MAAM,IAAI;AAChC,iBAAK,SAAS,UAAU,CAAC;AAEzB,iBAAK,SAAS,MAAM;AAMpB,gBAAI,SAAS;AACT,8BAAgB,KAAK,MAAM,OAAO;AAAA,YACtC;AAEA,iBAAK,OAAO,MAAM;AAClB,iBAAK,SAAS,UAAU,EAAE,OAAO,GAAG,IAAIL,SAAQ,UAAU,MAAM,CAAC;AAEjE,iBAAK,SAAS,aAAa;AAAA,UAC/B;AAMA,UAAAK,aAAY,UAAU,SAAS,WAAY;AACvC,gBAAI;AACJ,gBAAI,QAAQ,CAAC;AACb,qBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,oBAAM,EAAE,IAAI,UAAU,EAAE;AAAA,YAC5B;AACA,mBAAO,KAAKA,aAAY,KAAK,MAAMA,cAAa,cAAc,CAAC,MAAM,IAAI,KAAK,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC,GAAG,OAAO,MAAM,IAAI,KAAK,GAAG,KAAK,CAAC,GAAG;AAAA,UACtJ;AAKA,UAAAA,aAAY,UAAU,OAAO,SAAU,WAAW;AAC9C,mBAAO,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC,EAAE,KAAK,SAAS;AAAA,UAC1D;AAKA,UAAAA,aAAY,UAAU,UAAU,WAAY;AACxC,gBAAI,QAAQ;AACZ,gBAAI,UAAU,MAAM,KAAK,KAAK,OAAO,KAAK,CAAC;AAC3C,gBAAI,gBAAgB,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC,EAAE,QAAQ;AAC7D,0BAAc,QAAQ,SAAU,MAAM,GAAG;AACrC,oBAAM,MAAM,QAAQ,CAAC,GAAG,IAAI;AAAA,YAChC,CAAC;AACD,mBAAO;AAAA,UACX;AAIA,UAAAA,aAAY,UAAU,QAAQ,WAAY;AACtC,gBAAI,UAAU,MAAM,KAAK,KAAK,OAAO,KAAK,CAAC;AAC3C,gBAAI,UAAU,QAAQ,MAAM;AAC5B,gBAAI,YAAY,QAAW;AACvB,qBAAO;AAAA,YACX;AACA,gBAAI,QAAQ,KAAK,OAAO,IAAI,OAAO;AACnC,iBAAK,UAAU,OAAO;AACtB,mBAAO;AAAA,UACX;AAMA,UAAAA,aAAY,UAAU,QAAQ,SAAU,OAAO,KAAK;AAChD,gBAAI,SAAS,IAAIA,aAAY;AAC7B,mBAAO,KAAK,MAAM,QAAQ,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC,EAAE,MAAM,OAAO,GAAG,CAAC;AAC5E,mBAAO;AAAA,UACX;AAUA,UAAAA,aAAY,UAAU,OAAO,SAAU,WAAW;AAC9C,gBAAI,QAAQ;AACZ,gBAAI,cAAc,QAAQ;AAAE,0BAAY;AAAA,YAAc;AACtD,gBAAI,UAAU,MAAM,KAAK,KAAK,OAAO,KAAK,CAAC;AAC3C,gBAAI,cAAc,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC,EAAE,KAAK,SAAS;AACjE,wBAAY,QAAQ,SAAU,MAAM,GAAG;AACnC,oBAAM,MAAM,QAAQ,CAAC,GAAG,IAAI;AAAA,YAChC,CAAC;AACD,mBAAO;AAAA,UACX;AAOA,UAAAA,aAAY,UAAU,SAAS,SAAU,OAAO,aAAa;AACzD,gBAAI,gBAAgB,QAAQ;AAAE,4BAAc,KAAK,SAAS;AAAA,YAAO;AACjE,gBAAI,QAAQ,CAAC;AACb,qBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,oBAAM,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,YAChC;AACA,gBAAI,UAAU,MAAM,KAAK,KAAK,OAAO,KAAK,CAAC;AAC3C,gBAAI,eAAe,CAAC;AACpB,qBAAS,IAAI,OAAO,IAAI,QAAQ,aAAa,KAAK;AAC9C,2BAAa,KAAK,KAAK,OAAO,IAAI,QAAQ,CAAC,CAAC,CAAC;AAC7C,mBAAK,UAAU,QAAQ,CAAC,CAAC;AAAA,YAC7B;AACA,qBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,mBAAK,MAAM,QAAQ,GAAG,MAAM,CAAC,CAAC;AAAA,YAClC;AACA,mBAAO;AAAA,UACX;AAKA,UAAAA,aAAY,UAAU,UAAU,WAAY;AACxC,gBAAI,QAAQ;AACZ,gBAAI,QAAQ,CAAC;AACb,qBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,oBAAM,EAAE,IAAI,UAAU,EAAE;AAAA,YAC5B;AACA,gBAAI,SAAS,KAAK;AAClB,gBAAI,cAAc,MAAM;AAExB,gBAAI,iBAAiB,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC;AACpD,kBAAM,QAAQ,SAAU,MAAM,GAAG;AAC7B,oBAAM,MAAM,GAAG,IAAI;AAAA,YACvB,CAAC;AACD,2BAAe,QAAQ,SAAU,eAAe,GAAG;AAC/C,oBAAM,MAAM,cAAc,GAAG,aAAa;AAAA,YAC9C,CAAC;AACD,mBAAO,SAAS;AAAA,UACpB;AAMA,UAAAA,aAAY,UAAU,UAAU,SAAU,eAAe,WAAW;AAChE,mBAAO,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC,EAAE,QAAQ,eAAe,SAAS;AAAA,UAC5E;AAMA,UAAAA,aAAY,UAAU,cAAc,SAAU,eAAe,WAAW;AACpE,gBAAI,cAAc,QAAQ;AAAE,0BAAY,KAAK,SAAS;AAAA,YAAG;AACzD,mBAAO,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC,EAAE,YAAY,eAAe,SAAS;AAAA,UAChF;AACA,UAAAA,aAAY,UAAU,QAAQ,SAAU,YAAY,SAAS;AACzD,mBAAO,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC,EAAE,MAAM,YAAY,OAAO;AAAA,UACrE;AASA,UAAAA,aAAY,UAAU,OAAO,SAAU,YAAY,SAAS;AACxD,mBAAO,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC,EAAE,KAAK,YAAY,OAAO;AAAA,UACpE;AAMA,UAAAA,aAAY,UAAU,UAAU,SAAU,YAAY,SAAS;AAC3D,kBAAM,KAAK,KAAK,OAAO,OAAO,CAAC,EAAE,QAAQ,YAAY,OAAO;AAAA,UAChE;AAMA,UAAAA,aAAY,UAAU,MAAM,SAAU,YAAY,SAAS;AACvD,mBAAO,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC,EAAE,IAAI,YAAY,OAAO;AAAA,UACnE;AACA,UAAAA,aAAY,UAAU,SAAS,SAAU,YAAY,SAAS;AAC1D,mBAAO,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC,EAAE,OAAO,YAAY,OAAO;AAAA,UACtE;AAMA,UAAAA,aAAY,UAAU,SAAS,SAAU,YAAY,cAAc;AAC/D,mBAAO,MAAM,UAAU,OAAO,MAAM,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC,GAAG,SAAS;AAAA,UACnF;AAMA,UAAAA,aAAY,UAAU,cAAc,SAAU,YAAY,cAAc;AACpE,mBAAO,MAAM,UAAU,YAAY,MAAM,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC,GAAG,SAAS;AAAA,UACxF;AAUA,UAAAA,aAAY,UAAU,OAAO,SAAU,WAAW,SAAS;AACvD,mBAAO,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC,EAAE,KAAK,WAAW,OAAO;AAAA,UACnE;AAUA,UAAAA,aAAY,UAAU,YAAY,SAAU,WAAW,SAAS;AAC5D,mBAAO,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC,EAAE,UAAU,WAAW,OAAO;AAAA,UACxE;AASA,UAAAA,aAAY,UAAU,OAAO,SAAU,OAAO,OAAO,KAAK;AAItD,kBAAM,IAAI,MAAM,oCAAoC;AAAA,UACxD;AAUA,UAAAA,aAAY,UAAU,aAAa,SAAU,QAAQ,OAAO,KAAK;AAI7D,kBAAM,IAAI,MAAM,0CAA0C;AAAA,UAC9D;AAIA,UAAAA,aAAY,UAAU,WAAW,WAAY;AAAE,mBAAO,KAAK,OAAO,SAAS;AAAA,UAAG;AAI9E,UAAAA,aAAY,UAAU,iBAAiB,WAAY;AAAE,mBAAO,KAAK,OAAO,eAAe;AAAA,UAAG;AAE1F,UAAAA,aAAY,UAAU,OAAO,QAAQ,IAAI,WAAY;AACjD,mBAAO,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC,EAAE,OAAO,QAAQ,EAAE;AAAA,UAC7D;AACA,iBAAO,eAAeA,cAAa,OAAO,SAAS;AAAA,YAC/C,KAAK,WAAY;AACb,qBAAOA;AAAA,YACX;AAAA,YACA,YAAY;AAAA,YACZ,cAAc;AAAA,UAClB,CAAC;AAID,UAAAA,aAAY,UAAU,UAAU,WAAY;AAAE,mBAAO,KAAK,OAAO,QAAQ;AAAA,UAAG;AAI5E,UAAAA,aAAY,UAAU,OAAO,WAAY;AAAE,mBAAO,KAAK,OAAO,KAAK;AAAA,UAAG;AAItE,UAAAA,aAAY,UAAU,SAAS,WAAY;AAAE,mBAAO,KAAK,OAAO,OAAO;AAAA,UAAG;AAM1E,UAAAA,aAAY,UAAU,WAAW,SAAU,eAAe,WAAW;AACjE,mBAAO,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC,EAAE,SAAS,eAAe,SAAS;AAAA,UAC7E;AAeA,UAAAA,aAAY,UAAU,UAAU,SAAU,UAAU,SAAS;AAEzD,kBAAM,IAAI,MAAM,yCAAyC;AAAA,UAC7D;AAQA,UAAAA,aAAY,UAAU,OAAO,SAAU,OAAO;AAC1C,kBAAM,IAAI,MAAM,sCAAsC;AAAA,UAC1D;AACA,UAAAA,aAAY,UAAU,WAAW,WAAY;AACzC,gBAAI,MAAM,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC;AAEzC,mBAAO,IAAI,SAAS,MAAM,KAAK,SAAS;AAAA,UAC5C;AACA,UAAAA,aAAY,UAAU,gBAAgB,WAAY;AAC9C,gBAAI,MAAM,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC;AAEzC,mBAAO,IAAI,cAAc,MAAM,KAAK,SAAS;AAAA,UACjD;AAIA,UAAAA,aAAY,UAAU,OAAO,SAAU,OAAO,OAAO;AACjD,gBAAI,OAAO,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC;AAC1C,iBAAK,KAAK,IAAI;AACd,mBAAO,KAAKA,aAAY,KAAK,MAAMA,cAAa,cAAc,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG;AAAA,UAC3F;AACA,UAAAA,aAAY,UAAU,aAAa,WAAY;AAC3C,mBAAO,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC,EAAE,QAAQ;AAAA,UACpD;AACA,UAAAA,aAAY,UAAU,WAAW,SAAU,WAAW;AAClD,mBAAO,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC,EAAE,KAAK,SAAS;AAAA,UAC1D;AAEA,UAAAA,aAAY,UAAU,YAAY,SAAU,OAAO,aAAa;AAC5D,gBAAI,OAAO,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC;AAE1C,mBAAO,KAAK,UAAU,MAAM,MAAM,SAAS;AAAA,UAC/C;AACA,UAAAA,aAAY,UAAU,WAAW,SAAU,OAAO,KAAK;AACnD,iBAAK,SAAS,IAAI,OAAO,GAAG;AAAA,UAChC;AACA,UAAAA,aAAY,UAAU,WAAW,SAAU,OAAO;AAC9C,mBAAO,KAAK,SAAS,IAAI,KAAK;AAAA,UAClC;AACA,UAAAA,aAAY,UAAU,aAAa,SAAU,OAAO;AAChD,mBAAO,KAAK,OAAO,IAAI,KAAK,SAAS,IAAI,KAAK,CAAC;AAAA,UACnD;AACA,UAAAA,aAAY,UAAU,gBAAgB,SAAU,OAAO;AACnD,gBAAI,MAAM,KAAK,SAAS,IAAI,KAAK;AACjC,iBAAK,OAAO,OAAO,GAAG;AACtB,iBAAK,SAAS,OAAO,KAAK;AAAA,UAC9B;AACA,UAAAA,aAAY,UAAU,UAAU,WAAY;AACxC,mBAAO,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC;AAAA,UAC1C;AACA,UAAAA,aAAY,UAAU,SAAS,WAAY;AACvC,mBAAO,KAAK,QAAQ,EAAE,IAAI,SAAU,OAAO;AACvC,qBAAQ,OAAQ,MAAM,QAAQ,MAAO,aAC/B,MAAM,QAAQ,EAAE,IAChB;AAAA,YACV,CAAC;AAAA,UACL;AAIA,UAAAA,aAAY,UAAU,QAAQ,SAAU,YAAY;AAChD,gBAAI;AACJ,gBAAI,YAAY;AACZ,uBAAS,KAAKA,aAAY,KAAK,MAAMA,cAAa,cAAc,CAAC,MAAM,GAAG,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC,GAAG,KAAK,CAAC,GAAG;AAAA,YACzH,OACK;AACD,uBAAS,KAAKA,aAAY,KAAK,MAAMA,cAAa,cAAc,CAAC,MAAM,GAAG,KAAK,IAAI,SAAU,MAAM;AAAE,uBAAS,KAAK,UAAU,IACvH,KAAK,MAAM,IACX;AAAA,cAAO,CAAC,GAAG,KAAK,CAAC,GAAG;AAAA,YAC9B;AACA,mBAAO;AAAA,UACX;AACA,iBAAOA;AAAA,QACX,EAAE;AAAA;AAEF,eAAS,YAAY,OAAO;AACxB,cAAM,QAAQ,IAAI;AAClB,gBAAQ,IAAI,MAAM,OAAO;AAAA,UACrB,KAAK,SAAU,KAAK,MAAM;AACtB,gBAAI,OAAQ,SAAU;AAAA,YAClB,OAAQ,IAAI,IAAI,MAAO,aAAa;AACpC,qBAAO,IAAI,IAAI,IAAI;AAAA,YACvB,OACK;AACD,qBAAO,IAAI,IAAI;AAAA,YACnB;AAAA,UACJ;AAAA,UACA,KAAK,SAAU,KAAK,MAAM,UAAU;AAChC,gBAAI,OAAQ,SAAU,aACjB,KAAK,QAAQ,GAAG,MAAM,MACnB,SAAS,WACT,SAAS,cACT,SAAS,aAAa;AAC1B,kBAAI,IAAI,MAAM,QAAQ;AAAA,YAC1B,OACK;AACD,kBAAI,IAAI,IAAI;AAAA,YAChB;AACA,mBAAO;AAAA,UACX;AAAA,UACA,gBAAgB,SAAU,KAAK,MAAM;AACjC,gBAAI,OAAO,IAAI;AACf,mBAAO;AAAA,UACX;AAAA,QACJ,CAAC;AACD,eAAO;AAAA,MACX;AACA,UAAI;AAAA;AAAA,QAA2B,WAAY;AACvC,mBAASE,WAAU,eAAe;AAC9B,gBAAI,QAAQ;AACZ,iBAAK,WAAW,IAAI,WAAW,IAAI;AACnC,iBAAK,SAAS,oBAAI,IAAI;AACtB,iBAAK,WAAW,oBAAI,IAAI;AACxB,iBAAK,SAAS;AACd,gBAAI,eAAe;AACf,kBAAI,yBAAyB,OACzB,yBAAyBA,YAAW;AACpC,8BAAc,QAAQ,SAAU,GAAGC,IAAG;AAAE,yBAAO,MAAM,IAAIA,IAAG,CAAC;AAAA,gBAAG,CAAC;AAAA,cACrE,OACK;AACD,yBAAS,KAAK,eAAe;AACzB,uBAAK,IAAI,GAAG,cAAc,CAAC,CAAC;AAAA,gBAChC;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ;AACA,UAAAD,WAAU,UAAU,QAAQ,SAAU,UAAU,YAAY;AACxD,gBAAI,eAAe,QAAQ;AAAE,2BAAa;AAAA,YAAM;AAChD,mBAAO,YAAa,KAAK,eAAe,KAAK,aAAa,CAAC,IAAKP,SAAQ,UAAU,KAAK,UAAW,aAC5F,KAAK,SACL,MAAS;AAAA,UACnB;AACA,UAAAO,WAAU,UAAU,WAAW,SAAU,UAAU;AAAE,mBAAO,YAAY,KAAK,eAAe,KAAK,aAAa,CAAC,IAAIP,SAAQ,UAAU,QAAQ,QAAQ;AAAA,UAAG;AACxJ,UAAAO,WAAU,UAAU,WAAW,SAAU,UAAU;AAAE,mBAAO,YAAY,KAAK,eAAe,KAAK,aAAa,CAAC,IAAIP,SAAQ,UAAU,SAAS,QAAQ;AAAA,UAAG;AACzJ,UAAAO,WAAU,KAAK,SAAUD,OAAM;AAC3B,mBAAOA,MAAK,KAAK,MAAM;AAAA,UAC3B;AAEA,UAAAC,WAAU,UAAU,OAAO,QAAQ,IAAI,WAAY;AAAE,mBAAO,KAAK,OAAO,OAAO,QAAQ,EAAE;AAAA,UAAG;AAC5F,iBAAO,eAAeA,WAAU,WAAW,OAAO,aAAa;AAAA,YAC3D,KAAK,WAAY;AAAE,qBAAO,KAAK,OAAO,OAAO,WAAW;AAAA,YAAG;AAAA,YAC3D,YAAY;AAAA,YACZ,cAAc;AAAA,UAClB,CAAC;AACD,iBAAO,eAAeA,YAAW,OAAO,SAAS;AAAA,YAC7C,KAAK,WAAY;AACb,qBAAOA;AAAA,YACX;AAAA,YACA,YAAY;AAAA,YACZ,cAAc;AAAA,UAClB,CAAC;AACD,UAAAA,WAAU,UAAU,MAAM,SAAU,KAAK,OAAO;AAC5C,gBAAI,UAAU,UAAa,UAAU,MAAM;AACvC,oBAAM,IAAI,MAAM,kBAAkB,OAAO,KAAK,KAAK,EAAE,OAAO,OAAO,mBAAmB,EAAE,OAAO,OAAO,aAAa,EAAE,OAAO,KAAK,IAAI,CAAC;AAAA,YAC1I;AAGA,kBAAM,IAAI,SAAS;AAEnB,gBAAI,WAAW,OAAQ,KAAK,SAAS,QAAQ,GAAG,MAAO;AACvD,gBAAI,QAAS,WACP,KAAK,SAAS,QAAQ,GAAG,IACzB,KAAK;AACX,gBAAI,YAAa,WACXP,SAAQ,UAAU,UAClBA,SAAQ,UAAU;AACxB,gBAAI,QAAS,MAAM,UAAU,MAAO;AACpC,gBAAI,OAAO;AACP,oBAAM,UAAU,EAAE,UAAU,MAAM,KAAK,SAAS,MAAM,KAAK;AAAA,YAC/D;AAKA,gBAAI,CAAC,UAAU;AACX,mBAAK,SAAS,QAAQ,GAAG,IAAI;AAC7B,mBAAK,SAAS,IAAI,OAAO,GAAG;AAAA,YAChC,WACS,CAAC,SACN,KAAK,OAAO,IAAI,GAAG,MAAM,OAAO;AAEhC;AAAA,YACJ,WACS;AAAA,YACL,KAAK,OAAO,IAAI,GAAG,MAAM,OAAO;AAChC,0BAAYA,SAAQ,UAAU;AAAA,YAClC;AACA,iBAAK,OAAO,IAAI,KAAK,KAAK;AAC1B,iBAAK,SAAS,OAAO,KAAK,SAAS;AACnC,mBAAO;AAAA,UACX;AACA,UAAAO,WAAU,UAAU,MAAM,SAAU,KAAK;AACrC,mBAAO,KAAK,OAAO,IAAI,GAAG;AAAA,UAC9B;AACA,UAAAA,WAAU,UAAU,SAAS,SAAU,KAAK;AASxC,iBAAK,SAAS,OAAO,IAAI,SAAS,CAAC;AACnC,mBAAO,KAAK,OAAO,OAAO,GAAG;AAAA,UACjC;AACA,UAAAA,WAAU,UAAU,QAAQ,SAAU,SAAS;AAE3C,iBAAK,SAAS,QAAQ,MAAM,IAAI;AAChC,iBAAK,SAAS,UAAU,CAAC;AAEzB,iBAAK,SAAS,MAAM;AAMpB,gBAAI,SAAS;AACT,8BAAgB,KAAK,MAAM,OAAO;AAAA,YACtC;AAEA,iBAAK,OAAO,MAAM;AAClB,iBAAK,SAAS,UAAU,EAAE,OAAO,GAAG,IAAIP,SAAQ,UAAU,MAAM,CAAC;AAEjE,iBAAK,SAAS,aAAa;AAAA,UAC/B;AACA,UAAAO,WAAU,UAAU,MAAM,SAAU,KAAK;AACrC,mBAAO,KAAK,OAAO,IAAI,GAAG;AAAA,UAC9B;AACA,UAAAA,WAAU,UAAU,UAAU,SAAU,YAAY;AAChD,iBAAK,OAAO,QAAQ,UAAU;AAAA,UAClC;AACA,UAAAA,WAAU,UAAU,UAAU,WAAY;AACtC,mBAAO,KAAK,OAAO,QAAQ;AAAA,UAC/B;AACA,UAAAA,WAAU,UAAU,OAAO,WAAY;AACnC,mBAAO,KAAK,OAAO,KAAK;AAAA,UAC5B;AACA,UAAAA,WAAU,UAAU,SAAS,WAAY;AACrC,mBAAO,KAAK,OAAO,OAAO;AAAA,UAC9B;AACA,iBAAO,eAAeA,WAAU,WAAW,QAAQ;AAAA,YAC/C,KAAK,WAAY;AACb,qBAAO,KAAK,OAAO;AAAA,YACvB;AAAA,YACA,YAAY;AAAA,YACZ,cAAc;AAAA,UAClB,CAAC;AACD,UAAAA,WAAU,UAAU,WAAW,SAAU,OAAO,KAAK;AACjD,iBAAK,SAAS,IAAI,OAAO,GAAG;AAAA,UAChC;AACA,UAAAA,WAAU,UAAU,WAAW,SAAU,OAAO;AAC5C,mBAAO,KAAK,SAAS,IAAI,KAAK;AAAA,UAClC;AACA,UAAAA,WAAU,UAAU,aAAa,SAAU,OAAO;AAC9C,mBAAO,KAAK,OAAO,IAAI,KAAK,SAAS,IAAI,KAAK,CAAC;AAAA,UACnD;AACA,UAAAA,WAAU,UAAU,gBAAgB,SAAU,OAAO;AACjD,gBAAI,MAAM,KAAK,SAAS,IAAI,KAAK;AACjC,iBAAK,OAAO,OAAO,GAAG;AACtB,iBAAK,SAAS,OAAO,KAAK;AAAA,UAC9B;AACA,UAAAA,WAAU,UAAU,SAAS,WAAY;AACrC,gBAAI,MAAM,CAAC;AACX,iBAAK,QAAQ,SAAU,OAAO,KAAK;AAC/B,kBAAI,GAAG,IAAK,OAAQ,MAAM,QAAQ,MAAO,aACnC,MAAM,QAAQ,EAAE,IAChB;AAAA,YACV,CAAC;AACD,mBAAO;AAAA,UACX;AAIA,UAAAA,WAAU,UAAU,QAAQ,SAAU,YAAY;AAC9C,gBAAI;AACJ,gBAAI,YAAY;AAEZ,uBAAS,OAAO,OAAO,IAAIA,WAAU,GAAG,IAAI;AAAA,YAChD,OACK;AAED,uBAAS,IAAIA,WAAU;AACvB,mBAAK,QAAQ,SAAU,OAAO,KAAK;AAC/B,oBAAI,MAAM,UAAU,GAAG;AACnB,yBAAO,IAAI,KAAK,MAAM,OAAO,EAAE,CAAC;AAAA,gBACpC,OACK;AACD,yBAAO,IAAI,KAAK,KAAK;AAAA,gBACzB;AAAA,cACJ,CAAC;AAAA,YACL;AACA,mBAAO;AAAA,UACX;AACA,iBAAOA;AAAA,QACX,EAAE;AAAA;AAEF,UAAI,kBAAkB,CAAC;AACvB,eAAS,aAAa,YAAY,YAAY;AAC1C,wBAAgB,UAAU,IAAI;AAAA,MAClC;AACA,eAAS,QAAQ,YAAY;AACzB,eAAO,gBAAgB,UAAU;AAAA,MACrC;AAEA,UAAI;AAAA;AAAA,QAAkC,WAAY;AAC9C,mBAASE,oBAAmB;AAIxB,iBAAK,UAAU,CAAC;AAChB,iBAAK,gBAAgB,CAAC;AACtB,iBAAK,aAAa,CAAC;AACnB,iBAAK,cAAc,CAAC;AAAA,UACxB;AACA,UAAAA,kBAAiB,SAAS,SAAU,QAAQ;AACxC,gBAAI,aAAa,IAAIA,kBAAiB;AAEtC,uBAAW,SAAS,OAAO,OAAO,CAAC,GAAG,UAAU,OAAO,UAAU,CAAC,CAAC;AACnE,uBAAW,UAAU,OAAO,OAAO,CAAC,GAAG,UAAU,OAAO,WAAW,CAAC,CAAC;AACrE,uBAAW,gBAAgB,OAAO,OAAO,CAAC,GAAG,UAAU,OAAO,iBAAiB,CAAC,CAAC;AACjF,uBAAW,cAAc,OAAO,OAAO,CAAC,GAAG,UAAU,OAAO,eAAe,CAAC,CAAC;AAC7E,uBAAW,aAAa,OAAO,OAAO,CAAC,GAAG,UAAU,OAAO,cAAc,CAAC,CAAC;AAC3E,mBAAO;AAAA,UACX;AACA,UAAAA,kBAAiB,UAAU,WAAW,SAAU,OAAOH,OAAM;AACzD,gBAAI,QAAQ,KAAK,kBAAkB;AACnC,iBAAK,cAAc,KAAK,IAAI;AAC5B,iBAAK,QAAQ,KAAK,IAAI;AACtB,iBAAK,OAAO,KAAK,IAAK,MAAM,QAAQA,KAAI,IAClC,EAAE,OAAOA,MAAK,CAAC,EAAE,IACjBA;AAAA,UACV;AACA,UAAAG,kBAAiB,UAAU,WAAW,SAAU,OAAO;AACnD,mBAAO,KAAK,QAAQ,KAAK,MAAM;AAAA,UACnC;AACA,UAAAA,kBAAiB,UAAU,YAAY,SAAU,OAAO,IAAI;AACxD,gBAAI,CAAC,KAAK,SAAS;AACf,mBAAK,UAAU,CAAC;AAChB,mBAAK,qBAAqB,CAAC;AAAA,YAC/B;AACA,iBAAK,QAAQ,KAAK,QAAQ,KAAK,CAAC,IAAI;AACpC,iBAAK,mBAAmB,KAAK,KAAK,QAAQ,KAAK,CAAC;AAChD,mBAAO;AAAA,UACX;AACA,UAAAA,kBAAiB,UAAU,oBAAoB,SAAU,OAAO,IAAI;AAChE,gBAAI,QAAQ,KAAK,QAAQ,KAAK;AAC9B,gBAAIH,QAAO,KAAK,OAAO,KAAK;AAC5B,gBAAI,QAAQ,OAAO,KAAKA,KAAI,EAAE,CAAC,CAAC,GAAG;AAC/B,kBAAI,CAAC,KAAK,cAAc;AACpB,qBAAK,eAAe,CAAC;AAAA,cACzB;AACA,mBAAK,aAAa,KAAK,IAAI;AAC3B,qBAAO;AAAA,YACX,OACK;AACD,sBAAQ,KAAK,2BAA2B,OAAO,OAAO,yCAAyC,CAAC;AAAA,YACpG;AAAA,UACJ;AACA,UAAAG,kBAAiB,UAAU,oBAAoB,SAAU,OAAO;AAC5D,mBAAO,KAAK,gBAAgB,KAAK,aAAa,KAAK,QAAQ,KAAK,CAAC;AAAA,UACrE;AACA,UAAAA,kBAAiB,UAAU,oBAAoB,WAAY;AACvD,mBAAO,OAAO,KAAK,KAAK,UAAU,CAAC,CAAC,EAAE;AAAA,UAC1C;AACA,iBAAOA;AAAA,QACX,EAAE;AAAA;AACF,eAAS,UAAU,OAAO;AACtB,eAAO,MAAM,YAAY,MAAM,SAAS;AAAA,MAC5C;AACA,UAAI;AAAA;AAAA,QAAyB,WAAY;AACrC,mBAASC,WAAU;AACf,iBAAK,QAAQ,CAAC;AACd,iBAAK,UAAU,oBAAI,IAAI;AACvB,iBAAK,aAAa;AAAA,UACtB;AACA,UAAAA,SAAQ,UAAU,MAAM,SAAU,QAAQ;AACtC,mBAAO,KAAK,QAAQ,IAAI,MAAM;AAAA,UAClC;AACA,UAAAA,SAAQ,UAAU,MAAM,SAAU,QAAQ;AACtC,mBAAO,KAAK,MAAM,MAAM;AAAA,UAC5B;AACA,UAAAA,SAAQ,UAAU,MAAM,SAAU,QAAQ,QAAQ;AAC9C,gBAAI,WAAW,QAAQ;AAAE,uBAAS,KAAK,QAAQ;AAAA,YAAM;AAGrD,mBAAO,cAAc,iBAAiB,OAAO,OAAO,WAAW;AAC/D,mBAAO,UAAU;AACjB,iBAAK,MAAM,MAAM,IAAI;AACrB,iBAAK,QAAQ,IAAI,QAAQ,MAAM;AAAA,UACnC;AACA,UAAAA,SAAQ,SAAS,SAAU,SAAS;AAChC,gBAAI,YAAY,QAAQ;AAAE,wBAAU,CAAC;AAAA,YAAG;AACxC,mBAAO,SAAU,YAAY;AACzB,kBAAI,CAAC,QAAQ,SAAS;AAClB,wBAAQ,UAAU,IAAIA,SAAQ;AAAA,cAClC;AACA,qBAAO,KAAK,YAAY,OAAO;AAAA,YACnC;AAAA,UACJ;AACA,iBAAOA;AAAA,QACX,EAAE;AAAA;AACF,UAAI,gBAAgB,IAAI,QAAQ;AAiBhC,eAAS,KAAKJ,OAAM,SAAS;AACzB,YAAI,YAAY,QAAQ;AAAE,oBAAU,CAAC;AAAA,QAAG;AACxC,eAAO,SAAU,QAAQ,OAAO;AAC5B,cAAI,UAAU,QAAQ,WAAW;AACjC,cAAI,cAAc,OAAO;AACzB,sBAAY,WAAW;AACvB,cAAI,CAACA,OAAM;AACP,kBAAM,IAAI,MAAM,GAAG,OAAO,YAAY,MAAM,oCAAqC,EAAE,OAAO,OAAO,qEAAsE,CAAC;AAAA,UAC5K;AAIA,cAAI,CAAC,QAAQ,IAAI,WAAW,GAAG;AAC3B,oBAAQ,IAAI,WAAW;AAAA,UAC3B;AACA,cAAI,aAAa,YAAY;AAC7B,qBAAW,SAAS,OAAOA,KAAI;AAI/B,cAAI,WAAW,YAAY,KAAK,GAAG;AAC/B,gBAAI,WAAW,WAAW,KAAK,GAAG;AAE9B;AAAA,YACJ,OACK;AAGD,kBAAI;AACA,sBAAM,IAAI,MAAM,gCAAgC,OAAO,OAAO,mBAAmB,EAAE,OAAO,YAAY,MAAM,8BAA8B,CAAC;AAAA,cAC/I,SACO,GAAG;AACN,oBAAI,mBAAmB,EAAE,MAAM,MAAM,IAAI,EAAE,CAAC,EAAE,KAAK;AACnD,sBAAM,IAAI,MAAM,GAAG,OAAO,EAAE,SAAS,GAAG,EAAE,OAAO,gBAAgB,CAAC;AAAA,cACtE;AAAA,YACJ;AAAA,UACJ;AACA,cAAI,UAAU,YAAY,GAAGA,KAAI;AACjC,cAAI,QAAQ,CAAC,WAAW,UAAU,GAAGA,KAAI;AAIzC,cAAI,OAAQA,UAAU,YAAY,CAAC,OAAO,GAAGA,KAAI,GAAG;AAChD,gBAAI,YAAY,OAAO,OAAOA,KAAI,EAAE,CAAC;AACrC,gBAAI,OAAQ,cAAe,YAAY,CAAC,QAAQ,IAAI,SAAS,GAAG;AAC5D,sBAAQ,IAAI,SAAS;AAAA,YACzB;AAAA,UACJ;AACA,cAAI,QAAQ,QAAQ;AAEhB,uBAAW,YAAY,KAAK,IAAI;AAAA,cAC5B,YAAY;AAAA,cACZ,cAAc;AAAA,cACd,UAAU;AAAA,YACd;AACA;AAAA,UACJ;AACA,cAAI,cAAc,IAAI,OAAO,KAAK;AAClC,qBAAW,YAAY,WAAW,IAAI;AAAA,YAClC,YAAY;AAAA,YACZ,cAAc;AAAA,YACd,UAAU;AAAA,UACd;AACA,qBAAW,YAAY,KAAK,IAAI;AAAA,YAC5B,KAAK,WAAY;AACb,qBAAO,KAAK,WAAW;AAAA,YAC3B;AAAA,YACA,KAAK,SAAU,OAAO;AAKlB,kBAAI,UAAU,KAAK,WAAW,GAAG;AAC7B;AAAA,cACJ;AACA,kBAAI,UAAU,UACV,UAAU,MAAM;AAEhB,oBAAI,WAAW,EAAE,iBAAiB,cAAc;AAC5C,0BAAQ,KAAK,YAAY,KAAK,MAAM,aAAa,cAAc,CAAC,MAAM,GAAG,OAAO,KAAK,CAAC,GAAG;AAAA,gBAC7F;AAEA,oBAAI,SAAS,EAAE,iBAAiB,YAAY;AACxC,0BAAQ,IAAI,UAAU,KAAK;AAAA,gBAC/B;AAEA,oBAAI,MAAM,QAAQ,MAAM,QAAW;AAC/B,sBAAI,OAAO;AACP,4BAAQ,YAAY,KAAK;AAAA,kBAC7B,WACS,SAAS;AACd,4BAAQ,cAAc,KAAK;AAAA,kBAC/B;AAAA,gBACJ;AAEA,qBAAK,SAAS,OAAO,KAAK;AAK1B,oBAAI,MAAM,UAAU,GAAG;AACnB,wBAAM,UAAU,EAAE,UAAU,MAAM,KAAK,SAAS,MAAM,KAAK,YAAY,QAAQ,KAAK,CAAC;AAAA,gBACzF;AAAA,cACJ,WACS,KAAK,WAAW,MAAM,QAAW;AAItC,qBAAK,SAAS,OAAO,KAAK;AAAA,cAC9B;AACA,mBAAK,WAAW,IAAI;AAAA,YACxB;AAAA,YACA,YAAY;AAAA,YACZ,cAAc;AAAA,UAClB;AAAA,QACJ;AAAA,MACJ;AAIA,eAAS,OAAO,IAAI;AAChB,eAAO,SAAU,QAAQ,OAAO;AAC5B,cAAI,cAAc,OAAO;AACzB,cAAI,aAAa,YAAY;AAC7B,cAAI,WAAW,UAAU,OAAO,EAAE,GAAG;AACjC,wBAAY,SAAS,aAAa;AAAA,UACtC;AAAA,QACJ;AAAA,MACJ;AACA,eAAS,eAAe,IAAI;AACxB,eAAO,SAAU,QAAQ,OAAO;AAC5B,cAAI,cAAc,OAAO;AACzB,cAAI,aAAa,YAAY;AAC7B,cAAI,WAAW,kBAAkB,OAAO,EAAE,GAAG;AACzC,wBAAY,SAAS,aAAa;AAAA,UACtC;AAAA,QACJ;AAAA,MACJ;AAKA,eAAS,WAAW,QAAQ;AACxB,YAAI,WAAW,QAAQ;AAAE,mBAAS;AAAA,QAAM;AACxC,eAAO,SAAU,QAAQ,OAAO;AAC5B,cAAI,cAAc,OAAO;AACzB,cAAI,aAAa,YAAY;AAC7B,qBAAW,WAAW,KAAK,IAAI;AAC/B,cAAI,QAAQ;AACR,uBAAW,YAAY,KAAK,IAAI;AAAA,cAC5B,KAAK,WAAY;AAAE,sBAAM,IAAI,MAAM,GAAG,OAAO,OAAO,iBAAiB,CAAC;AAAA,cAAG;AAAA,cACzE,KAAK,SAAU,OAAO;AAAA,cAAE;AAAA,cACxB,YAAY;AAAA,cACZ,cAAc;AAAA,YAClB;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA,eAAS,YAAY,QAAQ,QAAQ,SAAS;AAC1C,YAAI,YAAY,QAAQ;AAAE,oBAAU,CAAC;AAAA,QAAG;AACxC,YAAI,CAAC,QAAQ,SAAS;AAClB,kBAAQ,UAAU,OAAO,YAAY,QAAQ,WAAW;AAAA,QAC5D;AACA,iBAAS,SAAS,QAAQ;AACtB,eAAK,OAAO,KAAK,GAAG,OAAO,EAAE,OAAO,WAAW,KAAK;AAAA,QACxD;AACA,eAAO;AAAA,MACX;AA4BA,eAAS,WAAW,KAAK;AACrB,YAAI,IAAI,GAAG,SAAS;AACpB,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAI,GAAG,KAAK;AACxC,cAAI,IAAI,WAAW,CAAC;AACpB,cAAI,IAAI,KAAM;AACV,sBAAU;AAAA,UACd,WACS,IAAI,MAAO;AAChB,sBAAU;AAAA,UACd,WACS,IAAI,SAAU,KAAK,OAAQ;AAChC,sBAAU;AAAA,UACd,OACK;AACD;AACA,sBAAU;AAAA,UACd;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AACA,eAAS,UAAU,MAAM,QAAQ,KAAK;AAClC,YAAI,IAAI;AACR,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAI,GAAG,KAAK;AACxC,cAAI,IAAI,WAAW,CAAC;AACpB,cAAI,IAAI,KAAM;AACV,iBAAK,QAAQ,IAAI;AAAA,UACrB,WACS,IAAI,MAAO;AAChB,iBAAK,QAAQ,IAAI,MAAQ,KAAK;AAC9B,iBAAK,QAAQ,IAAI,MAAQ,IAAI;AAAA,UACjC,WACS,IAAI,SAAU,KAAK,OAAQ;AAChC,iBAAK,QAAQ,IAAI,MAAQ,KAAK;AAC9B,iBAAK,QAAQ,IAAI,MAAQ,KAAK,IAAI;AAClC,iBAAK,QAAQ,IAAI,MAAQ,IAAI;AAAA,UACjC,OACK;AACD;AACA,gBAAI,UAAa,IAAI,SAAU,KAAO,IAAI,WAAW,CAAC,IAAI;AAC1D,iBAAK,QAAQ,IAAI,MAAQ,KAAK;AAC9B,iBAAK,QAAQ,IAAI,MAAQ,KAAK,KAAK;AACnC,iBAAK,QAAQ,IAAI,MAAQ,KAAK,IAAI;AAClC,iBAAK,QAAQ,IAAI,MAAQ,IAAI;AAAA,UACjC;AAAA,QACJ;AAAA,MACJ;AACA,eAAS,OAAO,OAAO,OAAO;AAC1B,cAAM,KAAK,QAAQ,GAAG;AAAA,MAC1B;AACA,eAAS,QAAQ,OAAO,OAAO;AAC3B,cAAM,KAAK,QAAQ,GAAG;AAAA,MAC1B;AACA,eAAS,QAAQ,OAAO,OAAO;AAC3B,cAAM,KAAK,QAAQ,GAAG;AACtB,cAAM,KAAM,SAAS,IAAK,GAAG;AAAA,MACjC;AACA,eAAS,SAAS,OAAO,OAAO;AAC5B,cAAM,KAAK,QAAQ,GAAG;AACtB,cAAM,KAAM,SAAS,IAAK,GAAG;AAAA,MACjC;AACA,eAAS,QAAQ,OAAO,OAAO;AAC3B,cAAM,KAAK,QAAQ,GAAG;AACtB,cAAM,KAAM,SAAS,IAAK,GAAG;AAC7B,cAAM,KAAM,SAAS,KAAM,GAAG;AAC9B,cAAM,KAAM,SAAS,KAAM,GAAG;AAAA,MAClC;AACA,eAAS,SAAS,OAAO,OAAO;AAC5B,YAAI,KAAK,SAAS;AAClB,YAAI,KAAK,SAAS;AAClB,YAAI,KAAK,SAAS;AAClB,YAAI,KAAK;AACT,cAAM,KAAK,KAAK,GAAG;AACnB,cAAM,KAAK,KAAK,GAAG;AACnB,cAAM,KAAK,KAAK,GAAG;AACnB,cAAM,KAAK,KAAK,GAAG;AAAA,MACvB;AACA,eAAS,QAAQ,OAAO,OAAO;AAC3B,YAAI,OAAO,KAAK,MAAM,QAAQ,KAAK,IAAI,GAAG,EAAE,CAAC;AAC7C,YAAI,MAAM,UAAU;AACpB,iBAAS,OAAO,GAAG;AACnB,iBAAS,OAAO,IAAI;AAAA,MACxB;AACA,eAAS,SAAS,OAAO,OAAO;AAC5B,YAAI,OAAQ,QAAQ,KAAK,IAAI,GAAG,EAAE,KAAM;AACxC,YAAI,MAAM,UAAU;AACpB,iBAAS,OAAO,GAAG;AACnB,iBAAS,OAAO,IAAI;AAAA,MACxB;AACA,eAAS,UAAU,OAAO,OAAO;AAC7B,qBAAa,OAAO,KAAK;AAAA,MAC7B;AACA,eAAS,UAAU,OAAO,OAAO;AAC7B,qBAAa,OAAO,KAAK;AAAA,MAC7B;AACA,UAAI,WAAW,IAAI,WAAW,CAAC;AAC/B,UAAI,aAAa,IAAI,aAAa,SAAS,MAAM;AACjD,UAAI,aAAa,IAAI,aAAa,SAAS,MAAM;AACjD,eAAS,aAAa,OAAO,OAAO;AAChC,mBAAW,CAAC,IAAI;AAChB,gBAAQ,OAAO,SAAS,CAAC,CAAC;AAAA,MAC9B;AACA,eAAS,aAAa,OAAO,OAAO;AAChC,mBAAW,CAAC,IAAI;AAChB,gBAAQ,OAAO,SAAS,CAAE,CAAC;AAC3B,gBAAQ,OAAO,SAAS,CAAE,CAAC;AAAA,MAC/B;AACA,eAAS,UAAU,OAAO,OAAO;AAC7B,eAAO,QAAQ,OAAO,QAAQ,IAAI,CAAC;AAAA,MACvC;AACA,eAAS,SAAS,OAAO,OAAO;AAE5B,YAAI,CAAC,OAAO;AACR,kBAAQ;AAAA,QACZ;AACA,YAAI,SAAS,WAAW,KAAK;AAC7B,YAAI,OAAO;AAEX,YAAI,SAAS,IAAM;AACf,gBAAM,KAAK,SAAS,GAAI;AACxB,iBAAO;AAAA,QACX,WAES,SAAS,KAAO;AACrB,gBAAM,KAAK,GAAI;AACf,kBAAQ,OAAO,MAAM;AACrB,iBAAO;AAAA,QACX,WAES,SAAS,OAAS;AACvB,gBAAM,KAAK,GAAI;AACf,mBAAS,OAAO,MAAM;AACtB,iBAAO;AAAA,QACX,WAES,SAAS,YAAa;AAC3B,gBAAM,KAAK,GAAI;AACf,mBAAS,OAAO,MAAM;AACtB,iBAAO;AAAA,QACX,OACK;AACD,gBAAM,IAAI,MAAM,iBAAiB;AAAA,QACrC;AACA,kBAAU,OAAO,MAAM,QAAQ,KAAK;AACpC,eAAO,OAAO;AAAA,MAClB;AACA,eAAS,SAAS,OAAO,OAAO;AAC5B,YAAI,MAAM,KAAK,GAAG;AACd,iBAAO,SAAS,OAAO,CAAC;AAAA,QAC5B,WACS,CAAC,SAAS,KAAK,GAAG;AACvB,iBAAO,SAAS,OAAQ,QAAQ,IAAK,OAAO,mBAAmB,CAAC,OAAO,gBAAgB;AAAA,QAC3F,WACS,WAAW,QAAQ,IAAI;AAC5B,gBAAM,KAAK,GAAI;AACf,uBAAa,OAAO,KAAK;AACzB,iBAAO;AAAA,QAOX;AACA,YAAI,SAAS,GAAG;AAEZ,cAAI,QAAQ,KAAM;AACd,oBAAQ,OAAO,KAAK;AACpB,mBAAO;AAAA,UACX;AAEA,cAAI,QAAQ,KAAO;AACf,kBAAM,KAAK,GAAI;AACf,oBAAQ,OAAO,KAAK;AACpB,mBAAO;AAAA,UACX;AAEA,cAAI,QAAQ,OAAS;AACjB,kBAAM,KAAK,GAAI;AACf,qBAAS,OAAO,KAAK;AACrB,mBAAO;AAAA,UACX;AAEA,cAAI,QAAQ,YAAa;AACrB,kBAAM,KAAK,GAAI;AACf,qBAAS,OAAO,KAAK;AACrB,mBAAO;AAAA,UACX;AAEA,gBAAM,KAAK,GAAI;AACf,mBAAS,OAAO,KAAK;AACrB,iBAAO;AAAA,QACX,OACK;AAED,cAAI,SAAS,KAAK;AACd,kBAAM,KAAK,MAAQ,QAAQ,EAAK;AAChC,mBAAO;AAAA,UACX;AAEA,cAAI,SAAS,MAAM;AACf,kBAAM,KAAK,GAAI;AACf,mBAAO,OAAO,KAAK;AACnB,mBAAO;AAAA,UACX;AAEA,cAAI,SAAS,QAAQ;AACjB,kBAAM,KAAK,GAAI;AACf,oBAAQ,OAAO,KAAK;AACpB,mBAAO;AAAA,UACX;AAEA,cAAI,SAAS,aAAa;AACtB,kBAAM,KAAK,GAAI;AACf,oBAAQ,OAAO,KAAK;AACpB,mBAAO;AAAA,UACX;AAEA,gBAAM,KAAK,GAAI;AACf,kBAAQ,OAAO,KAAK;AACpB,iBAAO;AAAA,QACX;AAAA,MACJ;AAEA,UAAI,SAAsB,OAAO,OAAO;AAAA,QACpC,WAAW;AAAA,QACX,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,QACT,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,OAAO;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AAwBD,eAAS,SAAS,OAAO,QAAQ,QAAQ;AACrC,YAAIK,UAAS,IAAI,MAAM;AACvB,iBAAS,IAAI,QAAQ,MAAM,SAAS,QAAQ,IAAI,KAAK,KAAK;AACtD,cAAI,OAAO,MAAM,CAAC;AAClB,eAAK,OAAO,SAAU,GAAM;AACxB,YAAAA,WAAU,OAAO,aAAa,IAAI;AAClC;AAAA,UACJ;AACA,eAAK,OAAO,SAAU,KAAM;AACxB,YAAAA,WAAU,OAAO,cAAe,OAAO,OAAS,IAC3C,MAAM,EAAE,CAAC,IAAI,EAAK;AACvB;AAAA,UACJ;AACA,eAAK,OAAO,SAAU,KAAM;AACxB,YAAAA,WAAU,OAAO,cAAe,OAAO,OAAS,MAC1C,MAAM,EAAE,CAAC,IAAI,OAAS,KACtB,MAAM,EAAE,CAAC,IAAI,OAAS,CAAE;AAC9B;AAAA,UACJ;AACA,eAAK,OAAO,SAAU,KAAM;AACxB,mBAAQ,OAAO,MAAS,MAClB,MAAM,EAAE,CAAC,IAAI,OAAS,MACtB,MAAM,EAAE,CAAC,IAAI,OAAS,KACtB,MAAM,EAAE,CAAC,IAAI,OAAS;AAC5B,gBAAI,OAAO,OAAU;AACjB,qBAAO;AACP,cAAAA,WAAU,OAAO,cAAc,QAAQ,MAAM,QAAS,MAAM,QAAS,KAAM;AAAA,YAC/E,OACK;AACD,cAAAA,WAAU,OAAO,aAAa,GAAG;AAAA,YACrC;AACA;AAAA,UACJ;AACA,kBAAQ,MAAM,kBAAkB,KAAK,SAAS,EAAE,CAAC;AAAA,QAGrD;AACA,eAAOA;AAAA,MACX;AACA,eAAS,KAAK,OAAO,IAAI;AACrB,eAAO,MAAM,OAAO,EAAE,KAAK,MAAM;AAAA,MACrC;AACA,eAAS,MAAM,OAAO,IAAI;AACtB,eAAO,MAAM,GAAG,QAAQ;AAAA,MAC5B;AACA,eAAS,MAAM,OAAO,IAAI;AACtB,eAAO,OAAO,OAAO,EAAE,KAAK,MAAM;AAAA,MACtC;AACA,eAAS,OAAO,OAAO,IAAI;AACvB,eAAO,MAAM,GAAG,QAAQ,IAAI,MAAM,GAAG,QAAQ,KAAK;AAAA,MACtD;AACA,eAAS,MAAM,OAAO,IAAI;AACtB,eAAO,MAAM,GAAG,QAAQ,IAAI,MAAM,GAAG,QAAQ,KAAK,IAAI,MAAM,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,KAAK;AAAA,MAC3G;AACA,eAAS,OAAO,OAAO,IAAI;AACvB,eAAO,MAAM,OAAO,EAAE,MAAM;AAAA,MAChC;AACA,eAAS,QAAQ,OAAO,IAAI;AACxB,eAAO,YAAY,OAAO,EAAE;AAAA,MAChC;AACA,eAAS,QAAQ,OAAO,IAAI;AACxB,eAAO,YAAY,OAAO,EAAE;AAAA,MAChC;AACA,eAAS,MAAM,OAAO,IAAI;AACtB,YAAI,MAAM,OAAO,OAAO,EAAE;AAC1B,YAAI,OAAO,MAAM,OAAO,EAAE,IAAI,KAAK,IAAI,GAAG,EAAE;AAC5C,eAAO,OAAO;AAAA,MAClB;AACA,eAAS,OAAO,OAAO,IAAI;AACvB,YAAI,MAAM,OAAO,OAAO,EAAE;AAC1B,YAAI,OAAO,OAAO,OAAO,EAAE,IAAI,KAAK,IAAI,GAAG,EAAE;AAC7C,eAAO,OAAO;AAAA,MAClB;AACA,UAAI,SAAS,IAAI,WAAW,CAAC;AAC7B,UAAI,WAAW,IAAI,aAAa,OAAO,MAAM;AAC7C,UAAI,WAAW,IAAI,aAAa,OAAO,MAAM;AAC7C,eAAS,YAAY,OAAO,IAAI;AAC5B,eAAO,CAAC,IAAI,MAAM,OAAO,EAAE;AAC3B,eAAO,SAAS,CAAC;AAAA,MACrB;AACA,eAAS,YAAY,OAAO,IAAI;AAC5B,eAAO,CAAE,IAAI,MAAM,OAAO,EAAE;AAC5B,eAAO,CAAE,IAAI,MAAM,OAAO,EAAE;AAC5B,eAAO,SAAS,CAAC;AAAA,MACrB;AACA,eAAS,QAAQ,OAAO,IAAI;AACxB,eAAO,MAAM,OAAO,EAAE,IAAI;AAAA,MAC9B;AACA,eAAS,OAAO,OAAO,IAAI;AACvB,YAAI,SAAS,MAAM,GAAG,QAAQ;AAC9B,YAAI;AACJ,YAAI,SAAS,KAAM;AAEf,mBAAS,SAAS;AAAA,QACtB,WACS,WAAW,KAAM;AACtB,mBAAS,MAAM,OAAO,EAAE;AAAA,QAC5B,WACS,WAAW,KAAM;AACtB,mBAAS,OAAO,OAAO,EAAE;AAAA,QAC7B,WACS,WAAW,KAAM;AACtB,mBAAS,OAAO,OAAO,EAAE;AAAA,QAC7B;AACA,YAAI,QAAQ,SAAS,OAAO,GAAG,QAAQ,MAAM;AAC7C,WAAG,UAAU;AACb,eAAO;AAAA,MACX;AACA,eAAS,YAAY,OAAO,IAAI;AAC5B,YAAI,SAAS,MAAM,GAAG,MAAM;AAC5B;AAAA;AAAA,UAEC,SAAS,OAAQ,SAAS;AAAA,UAEvB,WAAW;AAAA,UAEX,WAAW;AAAA,UAEX,WAAW;AAAA;AAAA,MACnB;AACA,eAAS,OAAO,OAAO,IAAI;AACvB,YAAI,SAAS,MAAM,GAAG,QAAQ;AAC9B,YAAI,SAAS,KAAM;AAEf,iBAAO;AAAA,QACX,WACS,WAAW,KAAM;AAEtB,iBAAO,YAAY,OAAO,EAAE;AAAA,QAChC,WACS,WAAW,KAAM;AAEtB,iBAAO,YAAY,OAAO,EAAE;AAAA,QAChC,WACS,WAAW,KAAM;AAEtB,iBAAO,MAAM,OAAO,EAAE;AAAA,QAC1B,WACS,WAAW,KAAM;AAEtB,iBAAO,OAAO,OAAO,EAAE;AAAA,QAC3B,WACS,WAAW,KAAM;AAEtB,iBAAO,OAAO,OAAO,EAAE;AAAA,QAC3B,WACS,WAAW,KAAM;AAEtB,iBAAO,OAAO,OAAO,EAAE;AAAA,QAC3B,WACS,WAAW,KAAM;AAEtB,iBAAO,KAAK,OAAO,EAAE;AAAA,QACzB,WACS,WAAW,KAAM;AAEtB,iBAAO,MAAM,OAAO,EAAE;AAAA,QAC1B,WACS,WAAW,KAAM;AAEtB,iBAAO,MAAM,OAAO,EAAE;AAAA,QAC1B,WACS,WAAW,KAAM;AAEtB,iBAAO,MAAM,OAAO,EAAE;AAAA,QAC1B,WACS,SAAS,KAAM;AAEpB,kBAAQ,MAAO,SAAS,KAAK;AAAA,QACjC;AAAA,MACJ;AACA,eAAS,YAAY,OAAO,IAAI;AAC5B,YAAI,SAAS,MAAM,GAAG,MAAM;AAY5B,eAAQ,SAAS,OACZ,UAAU,OAAQ,UAAU;AAAA,MACrC;AACA,eAAS,WAAW,OAAO,IAAI;AAC3B,eAAO,MAAM,GAAG,MAAM,IAAI;AAAA,MAW9B;AACA,eAAS,qBAAqB,OAAO,IAAI;AACrC;AAAA;AAAA,UAEA,MAAM,GAAG,SAAS,CAAC,MAAM;AAAA,WAEpB,MAAM,GAAG,MAAM,IAAI,OAAS,MAAM,GAAG,MAAM,KAAK,OAAQ,MAAM,GAAG,MAAM,KAAK;AAAA;AAAA,MACrF;AAEA,UAAI,SAAsB,OAAO,OAAO;AAAA,QACpC,WAAW;AAAA,QACX;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AAED,UAAI;AAAA;AAAA,QAAkC,WAAY;AAC9C,mBAASC,kBAAiB,eAAe;AACrC,gBAAI,QAAQ;AACZ,iBAAK,WAAW,IAAI,WAAW,IAAI;AACnC,iBAAK,SAAS,oBAAI,IAAI;AACtB,iBAAK,WAAW,oBAAI,IAAI;AACxB,iBAAK,SAAS;AACd,gBAAI,eAAe;AACf,4BAAc,QAAQ,SAAU,GAAG;AAAE,uBAAO,MAAM,IAAI,CAAC;AAAA,cAAG,CAAC;AAAA,YAC/D;AAAA,UACJ;AACA,UAAAA,kBAAiB,UAAU,QAAQ,SAAU,UAAU,YAAY;AAC/D,gBAAI,eAAe,QAAQ;AAAE,2BAAa;AAAA,YAAM;AAChD,mBAAO,YAAa,KAAK,eAAe,KAAK,aAAa,CAAC,IAAKZ,SAAQ,UAAU,KAAK,UAAW,aAC5F,KAAK,SACL,MAAS;AAAA,UACnB;AACA,UAAAY,kBAAiB,UAAU,WAAW,SAAU,UAAU;AAAE,mBAAO,YAAY,KAAK,eAAe,KAAK,aAAa,CAAC,IAAIZ,SAAQ,UAAU,QAAQ,QAAQ;AAAA,UAAG;AAC/J,UAAAY,kBAAiB,UAAU,WAAW,SAAU,UAAU;AAAE,mBAAO,YAAY,KAAK,eAAe,KAAK,aAAa,CAAC,IAAIZ,SAAQ,UAAU,SAAS,QAAQ;AAAA,UAAG;AAChK,UAAAY,kBAAiB,KAAK,SAAUN,OAAM;AAClC,mBAAOA,MAAK,YAAY,MAAM;AAAA,UAClC;AACA,UAAAM,kBAAiB,UAAU,MAAM,SAAU,OAAO;AAE9C,gBAAI,QAAQ,KAAK;AACjB,gBAAI,QAAS,MAAM,UAAU,MAAO;AACpC,gBAAI,OAAO;AACP,oBAAM,UAAU,EAAE,UAAU,MAAM,KAAK,SAAS,MAAM,KAAK;AAAA,YAC/D;AACA,iBAAK,SAAS,QAAQ,KAAK,IAAI;AAC/B,iBAAK,SAAS,IAAI,OAAO,KAAK;AAC9B,iBAAK,OAAO,IAAI,OAAO,KAAK;AAC5B,iBAAK,SAAS,OAAO,KAAK;AAC1B,mBAAO;AAAA,UACX;AACA,UAAAA,kBAAiB,UAAU,KAAK,SAAU,OAAO;AAC7C,gBAAI,MAAM,MAAM,KAAK,KAAK,OAAO,KAAK,CAAC,EAAE,KAAK;AAC9C,mBAAO,KAAK,OAAO,IAAI,GAAG;AAAA,UAC9B;AACA,UAAAA,kBAAiB,UAAU,UAAU,WAAY;AAC7C,mBAAO,KAAK,OAAO,QAAQ;AAAA,UAC/B;AACA,UAAAA,kBAAiB,UAAU,SAAS,SAAU,MAAM;AAChD,gBAAI,UAAU,KAAK,OAAO,QAAQ;AAClC,gBAAI;AACJ,gBAAI;AACJ,mBAAO,QAAQ,QAAQ,KAAK,GAAG;AAC3B,kBAAI,MAAM,MAAM;AACZ;AAAA,cACJ;AACA,kBAAI,SAAS,MAAM,MAAM,CAAC,GAAG;AACzB,wBAAQ,MAAM,MAAM,CAAC;AACrB;AAAA,cACJ;AAAA,YACJ;AACA,gBAAI,UAAU,QAAW;AACrB,qBAAO;AAAA,YACX;AACA,iBAAK,SAAS,OAAO,KAAK;AAC1B,iBAAK,SAAS,OAAO,KAAK;AAC1B,mBAAO,KAAK,OAAO,OAAO,KAAK;AAAA,UACnC;AACA,UAAAA,kBAAiB,UAAU,QAAQ,SAAU,SAAS;AAElD,iBAAK,SAAS,QAAQ,MAAM,IAAI;AAChC,iBAAK,SAAS,UAAU,CAAC;AAEzB,iBAAK,SAAS,MAAM;AAMpB,gBAAI,SAAS;AACT,8BAAgB,KAAK,MAAM,OAAO;AAAA,YACtC;AAEA,iBAAK,OAAO,MAAM;AAClB,iBAAK,SAAS,UAAU,EAAE,OAAO,GAAG,IAAIZ,SAAQ,UAAU,MAAM,CAAC;AAEjE,iBAAK,SAAS,aAAa;AAAA,UAC/B;AACA,UAAAY,kBAAiB,UAAU,MAAM,SAAU,OAAO;AAC9C,mBAAO,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC,EAAE,KAAK,SAAU,GAAG;AAAE,qBAAO,MAAM;AAAA,YAAO,CAAC;AAAA,UACrF;AACA,UAAAA,kBAAiB,UAAU,UAAU,SAAU,YAAY;AACvD,gBAAI,QAAQ;AACZ,iBAAK,OAAO,QAAQ,SAAU,OAAO,KAAK,GAAG;AAAE,qBAAO,WAAW,OAAO,KAAK,KAAK;AAAA,YAAG,CAAC;AAAA,UAC1F;AACA,UAAAA,kBAAiB,UAAU,SAAS,WAAY;AAC5C,mBAAO,KAAK,OAAO,OAAO;AAAA,UAC9B;AACA,iBAAO,eAAeA,kBAAiB,WAAW,QAAQ;AAAA,YACtD,KAAK,WAAY;AACb,qBAAO,KAAK,OAAO;AAAA,YACvB;AAAA,YACA,YAAY;AAAA,YACZ,cAAc;AAAA,UAClB,CAAC;AACD,UAAAA,kBAAiB,UAAU,WAAW,SAAU,OAAO,KAAK;AACxD,iBAAK,SAAS,IAAI,OAAO,GAAG;AAAA,UAChC;AACA,UAAAA,kBAAiB,UAAU,WAAW,SAAU,OAAO;AACnD,mBAAO,KAAK,SAAS,IAAI,KAAK;AAAA,UAClC;AACA,UAAAA,kBAAiB,UAAU,aAAa,SAAU,OAAO;AACrD,mBAAO,KAAK,OAAO,IAAI,KAAK,SAAS,IAAI,KAAK,CAAC;AAAA,UACnD;AACA,UAAAA,kBAAiB,UAAU,gBAAgB,SAAU,OAAO;AACxD,gBAAI,MAAM,KAAK,SAAS,IAAI,KAAK;AACjC,iBAAK,OAAO,OAAO,GAAG;AACtB,iBAAK,SAAS,OAAO,KAAK;AAAA,UAC9B;AACA,UAAAA,kBAAiB,UAAU,UAAU,WAAY;AAC7C,mBAAO,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC;AAAA,UAC1C;AACA,UAAAA,kBAAiB,UAAU,SAAS,WAAY;AAC5C,gBAAI,SAAS,CAAC;AACd,iBAAK,QAAQ,SAAU,OAAO,KAAK;AAC/B,qBAAO,KAAM,OAAQ,MAAM,QAAQ,MAAO,aACpC,MAAM,QAAQ,EAAE,IAChB,KAAK;AAAA,YACf,CAAC;AACD,mBAAO;AAAA,UACX;AAIA,UAAAA,kBAAiB,UAAU,QAAQ,SAAU,YAAY;AACrD,gBAAI;AACJ,gBAAI,YAAY;AAEZ,uBAAS,OAAO,OAAO,IAAIA,kBAAiB,GAAG,IAAI;AAAA,YACvD,OACK;AAED,uBAAS,IAAIA,kBAAiB;AAC9B,mBAAK,QAAQ,SAAU,OAAO;AAC1B,oBAAI,MAAM,UAAU,GAAG;AACnB,yBAAO,IAAI,MAAM,OAAO,EAAE,CAAC;AAAA,gBAC/B,OACK;AACD,yBAAO,IAAI,KAAK;AAAA,gBACpB;AAAA,cACJ,CAAC;AAAA,YACL;AACA,mBAAO;AAAA,UACX;AACA,iBAAOA;AAAA,QACX,EAAE;AAAA;AAEF,UAAI;AAAA;AAAA,QAA2B,WAAY;AACvC,mBAASC,WAAU,eAAe;AAC9B,gBAAI,QAAQ;AACZ,iBAAK,WAAW,IAAI,WAAW,IAAI;AACnC,iBAAK,SAAS,oBAAI,IAAI;AACtB,iBAAK,WAAW,oBAAI,IAAI;AACxB,iBAAK,SAAS;AACd,gBAAI,eAAe;AACf,4BAAc,QAAQ,SAAU,GAAG;AAAE,uBAAO,MAAM,IAAI,CAAC;AAAA,cAAG,CAAC;AAAA,YAC/D;AAAA,UACJ;AACA,UAAAA,WAAU,UAAU,QAAQ,SAAU,UAAU,YAAY;AACxD,gBAAI,eAAe,QAAQ;AAAE,2BAAa;AAAA,YAAM;AAChD,mBAAO,YAAa,KAAK,eAAe,KAAK,aAAa,CAAC,IAAKb,SAAQ,UAAU,KAAK,UAAW,aAC5F,KAAK,SACL,MAAS;AAAA,UACnB;AACA,UAAAa,WAAU,UAAU,WAAW,SAAU,UAAU;AAAE,mBAAO,YAAY,KAAK,eAAe,KAAK,aAAa,CAAC,IAAIb,SAAQ,UAAU,QAAQ,QAAQ;AAAA,UAAG;AACxJ,UAAAa,WAAU,UAAU,WAAW,SAAU,UAAU;AAAE,mBAAO,YAAY,KAAK,eAAe,KAAK,aAAa,CAAC,IAAIb,SAAQ,UAAU,SAAS,QAAQ;AAAA,UAAG;AACzJ,UAAAa,WAAU,KAAK,SAAUP,OAAM;AAC3B,mBAAOA,MAAK,KAAK,MAAM;AAAA,UAC3B;AACA,UAAAO,WAAU,UAAU,MAAM,SAAU,OAAO;AACvC,gBAAI,IAAI;AAER,gBAAI,KAAK,IAAI,KAAK,GAAG;AACjB,qBAAO;AAAA,YACX;AAEA,gBAAI,QAAQ,KAAK;AACjB,gBAAK,MAAM,UAAU,MAAO,QAAW;AACnC,oBAAM,UAAU,EAAE,UAAU,MAAM,KAAK,SAAS,MAAM,KAAK;AAAA,YAC/D;AACA,gBAAI,aAAa,MAAM,KAAK,KAAK,SAAS,QAAQ,KAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,QAAQ,OAAO,SAAS,KAAKb,SAAQ,UAAU;AACzJ,iBAAK,SAAS,QAAQ,KAAK,IAAI;AAC/B,iBAAK,SAAS,IAAI,OAAO,KAAK;AAC9B,iBAAK,OAAO,IAAI,OAAO,KAAK;AAC5B,iBAAK,SAAS,OAAO,OAAO,SAAS;AACrC,mBAAO;AAAA,UACX;AACA,UAAAa,WAAU,UAAU,UAAU,WAAY;AACtC,mBAAO,KAAK,OAAO,QAAQ;AAAA,UAC/B;AACA,UAAAA,WAAU,UAAU,SAAS,SAAU,MAAM;AACzC,gBAAI,UAAU,KAAK,OAAO,QAAQ;AAClC,gBAAI;AACJ,gBAAI;AACJ,mBAAO,QAAQ,QAAQ,KAAK,GAAG;AAC3B,kBAAI,MAAM,MAAM;AACZ;AAAA,cACJ;AACA,kBAAI,SAAS,MAAM,MAAM,CAAC,GAAG;AACzB,wBAAQ,MAAM,MAAM,CAAC;AACrB;AAAA,cACJ;AAAA,YACJ;AACA,gBAAI,UAAU,QAAW;AACrB,qBAAO;AAAA,YACX;AACA,iBAAK,SAAS,OAAO,KAAK;AAC1B,iBAAK,SAAS,OAAO,KAAK;AAC1B,mBAAO,KAAK,OAAO,OAAO,KAAK;AAAA,UACnC;AACA,UAAAA,WAAU,UAAU,QAAQ,SAAU,SAAS;AAE3C,iBAAK,SAAS,QAAQ,MAAM,IAAI;AAChC,iBAAK,SAAS,UAAU,CAAC;AAEzB,iBAAK,SAAS,MAAM;AAMpB,gBAAI,SAAS;AACT,8BAAgB,KAAK,MAAM,OAAO;AAAA,YACtC;AAEA,iBAAK,OAAO,MAAM;AAClB,iBAAK,SAAS,UAAU,EAAE,OAAO,GAAG,IAAIb,SAAQ,UAAU,MAAM,CAAC;AAEjE,iBAAK,SAAS,aAAa;AAAA,UAC/B;AACA,UAAAa,WAAU,UAAU,MAAM,SAAU,OAAO;AACvC,gBAAI,SAAS,KAAK,OAAO,OAAO;AAChC,gBAAI,MAAM;AACV,gBAAI;AACJ,mBAAO,QAAQ,OAAO,KAAK,GAAG;AAC1B,kBAAI,MAAM,MAAM;AACZ;AAAA,cACJ;AACA,kBAAI,UAAU,MAAM,OAAO;AACvB,sBAAM;AACN;AAAA,cACJ;AAAA,YACJ;AACA,mBAAO;AAAA,UACX;AACA,UAAAA,WAAU,UAAU,UAAU,SAAU,YAAY;AAChD,gBAAI,QAAQ;AACZ,iBAAK,OAAO,QAAQ,SAAU,OAAO,KAAK,GAAG;AAAE,qBAAO,WAAW,OAAO,KAAK,KAAK;AAAA,YAAG,CAAC;AAAA,UAC1F;AACA,UAAAA,WAAU,UAAU,SAAS,WAAY;AACrC,mBAAO,KAAK,OAAO,OAAO;AAAA,UAC9B;AACA,iBAAO,eAAeA,WAAU,WAAW,QAAQ;AAAA,YAC/C,KAAK,WAAY;AACb,qBAAO,KAAK,OAAO;AAAA,YACvB;AAAA,YACA,YAAY;AAAA,YACZ,cAAc;AAAA,UAClB,CAAC;AACD,UAAAA,WAAU,UAAU,WAAW,SAAU,OAAO,KAAK;AACjD,iBAAK,SAAS,IAAI,OAAO,GAAG;AAAA,UAChC;AACA,UAAAA,WAAU,UAAU,WAAW,SAAU,OAAO;AAC5C,mBAAO,KAAK,SAAS,IAAI,KAAK;AAAA,UAClC;AACA,UAAAA,WAAU,UAAU,aAAa,SAAU,OAAO;AAC9C,mBAAO,KAAK,OAAO,IAAI,KAAK,SAAS,IAAI,KAAK,CAAC;AAAA,UACnD;AACA,UAAAA,WAAU,UAAU,gBAAgB,SAAU,OAAO;AACjD,gBAAI,MAAM,KAAK,SAAS,IAAI,KAAK;AACjC,iBAAK,OAAO,OAAO,GAAG;AACtB,iBAAK,SAAS,OAAO,KAAK;AAAA,UAC9B;AACA,UAAAA,WAAU,UAAU,UAAU,WAAY;AACtC,mBAAO,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC;AAAA,UAC1C;AACA,UAAAA,WAAU,UAAU,SAAS,WAAY;AACrC,gBAAI,SAAS,CAAC;AACd,iBAAK,QAAQ,SAAU,OAAO,KAAK;AAC/B,qBAAO,KAAM,OAAQ,MAAM,QAAQ,MAAO,aACpC,MAAM,QAAQ,EAAE,IAChB,KAAK;AAAA,YACf,CAAC;AACD,mBAAO;AAAA,UACX;AAIA,UAAAA,WAAU,UAAU,QAAQ,SAAU,YAAY;AAC9C,gBAAI;AACJ,gBAAI,YAAY;AAEZ,uBAAS,OAAO,OAAO,IAAIA,WAAU,GAAG,IAAI;AAAA,YAChD,OACK;AAED,uBAAS,IAAIA,WAAU;AACvB,mBAAK,QAAQ,SAAU,OAAO;AAC1B,oBAAI,MAAM,UAAU,GAAG;AACnB,yBAAO,IAAI,MAAM,OAAO,EAAE,CAAC;AAAA,gBAC/B,OACK;AACD,yBAAO,IAAI,KAAK;AAAA,gBACpB;AAAA,cACJ,CAAC;AAAA,YACL;AACA,mBAAO;AAAA,UACX;AACA,iBAAOA;AAAA,QACX,EAAE;AAAA;AAEF,UAAI;AAAA;AAAA,QAA6B,WAAY;AACzC,mBAASC,eAAc;AACnB,iBAAK,SAAS,oBAAI,QAAQ;AAC1B,iBAAK,mBAAmB,oBAAI,QAAQ;AAAA,UACxC;AAEA,UAAAA,aAAY,UAAU,WAAW,SAAU,YAAY;AACnD,gBAAI,CAAC,KAAK,OAAO,IAAI,UAAU,GAAG;AAC9B,mBAAK,OAAO,IAAI,UAAU;AAC1B,mBAAK,iBAAiB,IAAI,YAAY,oBAAI,IAAI,CAAC;AAAA,YACnD;AAAA,UACJ;AACA,UAAAA,aAAY,MAAM,SAAU,QAAQ;AAChC,gBAAI,OAAO,iBAAiB,QAAW;AACnC,qBAAO,eAAe,IAAIA,aAAY;AAAA,YAC1C;AACA,mBAAO,OAAO;AAAA,UAClB;AACA,iBAAOA;AAAA,QACX,EAAE;AAAA;AAEF,UAAI;AAAA;AAAA,QAAkC,WAAY;AAC9C,mBAASC,oBAAmB;AAKxB,iBAAK,OAAO,oBAAI,IAAI;AACpB,iBAAK,YAAY,CAAC;AAClB,iBAAK,cAAc,oBAAI,IAAI;AAC3B,iBAAK,eAAe;AAAA,UACxB;AACA,UAAAA,kBAAiB,UAAU,kBAAkB,WAAY;AACrD,mBAAO,KAAK;AAAA,UAChB;AAEA,UAAAA,kBAAiB,UAAU,SAAS,SAAU,OAAO,KAAK,gBAAgB;AACtE,gBAAI,mBAAmB,QAAQ;AAAE,+BAAiB;AAAA,YAAM;AACxD,iBAAK,KAAK,IAAI,OAAO,GAAG;AACxB,gBAAI,gBAAgB;AAChB,mBAAK,UAAU,KAAK,KAAK,KAAK,UAAU,KAAK,KAAK,KAAK;AAAA,YAC3D;AAAA,UACJ;AAEA,UAAAA,kBAAiB,UAAU,YAAY,SAAU,OAAO;AACpD,gBAAI,WAAW,KAAK,UAAU,KAAK;AACnC,gBAAI,aAAa,QAAW;AACxB,sBAAQ,KAAK,8BAA8B,OAAO,OAAO,qBAAqB,CAAC;AAC/E;AAAA,YACJ;AACA,gBAAI,aAAa,GAAG;AAChB,sBAAQ,KAAK,8BAA8B,OAAO,OAAO,kBAAkB,CAAC;AAC5E;AAAA,YACJ;AACA,iBAAK,UAAU,KAAK,IAAI,WAAW;AACnC,iBAAK,YAAY,IAAI,KAAK;AAAA,UAC9B;AACA,UAAAA,kBAAiB,UAAU,YAAY,WAAY;AAC/C,iBAAK,KAAK,MAAM;AAChB,iBAAK,YAAY,MAAM;AACvB,iBAAK,YAAY,CAAC;AAAA,UACtB;AAEA,UAAAA,kBAAiB,UAAU,4BAA4B,WAAY;AAC/D,gBAAI,QAAQ;AACZ,iBAAK,YAAY,QAAQ,SAAU,OAAO;AAItC,kBAAI,MAAM,UAAU,KAAK,IAAI,GAAG;AAC5B;AAAA,cACJ;AACA,kBAAI,MAAM,MAAM,KAAK,IAAI,KAAK;AAI9B,kBAAI,eAAe,QAAQ;AACvB,yBAAS,aAAa,IAAI,aAAa,EAAE,QAAQ;AAC7C,sBAAI,OAAQ,IAAI,aAAa,EAAE,OAAO,SAAS,MAAO,YAClD,IAAI,SAAS,KACb,IAAI,SAAS,EAAE,UAAU,GAAG;AAC5B,0BAAM,UAAU,IAAI,SAAS,EAAE,UAAU,EAAE,KAAK;AAAA,kBACpD;AAAA,gBACJ;AAAA,cACJ,OACK;AACD,oBAAI,aAAa,IAAI,UAAU,EAAE,OAAO;AACxC,oBAAIT,QAAO,WAAW,OAAO,WAAW,cAAc,IAAI,UAAU,EAAE,WAAW,CAAC;AAClF,oBAAI,OAAQ,OAAO,OAAOA,KAAI,EAAE,CAAC,MAAO,YAAY;AAChD,wBAAM,KAAK,IAAI,OAAO,CAAC,EAClB,QAAQ,SAAU,OAAO;AAAE,2BAAO,MAAM,UAAU,MAAM,UAAU,EAAE,KAAK;AAAA,kBAAG,CAAC;AAAA,gBACtF;AAAA,cACJ;AACA,oBAAM,KAAK,OAAO,KAAK;AACvB,qBAAO,MAAM,UAAU,KAAK;AAAA,YAChC,CAAC;AAED,iBAAK,YAAY,MAAM;AAAA,UAC3B;AACA,iBAAOS;AAAA,QACX,EAAE;AAAA;AAEF,UAAI;AAAA;AAAA,QAAmC,SAAU,QAAQ;AACrD,oBAAUC,oBAAmB,MAAM;AACnC,mBAASA,qBAAoB;AACzB,mBAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,UAC/D;AACA,iBAAOA;AAAA,QACX,EAAE,KAAK;AAAA;AACP,eAAS,WAAW,OAAOV,OAAM,OAAO,OAAO;AAC3C,YAAI;AACJ,YAAI,YAAY;AAChB,gBAAQA,OAAM;AAAA,UACV,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACD,2BAAe;AACf,gBAAI,MAAM,KAAK,GAAG;AACd,sBAAQ,IAAI,6BAA+B,OAAO,MAAM,YAAY,MAAM,GAAG,EAAE,OAAO,KAAK,CAAC;AAAA,YAChG;AACA;AAAA,UACJ,KAAK;AACD,2BAAe;AACf,wBAAY;AACZ;AAAA,UACJ,KAAK;AAED;AAAA,QACR;AACA,YAAI,OAAQ,UAAW,iBAAiB,CAAC,aAAc,aAAa,UAAU,OAAQ;AAClF,cAAI,aAAa,IAAI,OAAO,KAAK,UAAU,KAAK,GAAG,GAAG,EAAE,OAAQ,SAAS,MAAM,eAAe,KAAK,OAAO,MAAM,YAAY,MAAM,GAAG,KAAM,EAAE;AAC7I,gBAAM,IAAI,kBAAkB,MAAM,OAAO,cAAc,sBAAsB,EAAE,OAAO,YAAY,mBAAmB,EAAE,OAAO,MAAM,YAAY,MAAM,GAAG,EAAE,OAAO,KAAK,CAAC;AAAA,QAC5K;AAAA,MACJ;AACA,eAAS,mBAAmB,OAAOA,OAAM,OAAO,OAAO;AACnD,YAAI,EAAE,iBAAiBA,QAAO;AAC1B,gBAAM,IAAI,kBAAkB,MAAM,OAAOA,MAAK,MAAM,uBAAuB,EAAE,OAAO,MAAM,YAAY,MAAM,oBAAoB,EAAE,OAAO,MAAM,YAAY,MAAM,GAAG,EAAE,OAAO,KAAK,CAAC;AAAA,QACvL;AAAA,MACJ;AACA,eAAS,oBAAoBA,OAAM,OAAO,OAAO,OAAO,OAAO;AAC3D,mBAAW,OAAOA,OAAM,OAAO,KAAK;AACpC,YAAI,aAAa,OAAOA,KAAI;AAC5B,YAAI,YAAY;AACZ,qBAAW,OAAO,KAAK;AAAA,QAC3B,OACK;AACD,gBAAM,IAAI,kBAAkB,MAAM,OAAOA,OAAM,sBAAsB,EAAE,OAAO,OAAO,mBAAmB,EAAE,OAAO,MAAM,YAAY,MAAM,GAAG,EAAE,OAAO,KAAK,CAAC;AAAA,QAC/J;AAAA,MACJ;AACA,eAAS,oBAAoBA,OAAM,OAAO,IAAI;AAC1C,eAAO,OAAOA,KAAI,EAAE,OAAO,EAAE;AAAA,MACjC;AAIA,UAAI;AAAA;AAAA,QAAwB,WAAY;AAEpC,mBAASW,UAAS;AACd,gBAAI,OAAO,CAAC;AACZ,qBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,mBAAK,EAAE,IAAI,UAAU,EAAE;AAAA,YAC3B;AAEA,mBAAO,iBAAiB,MAAM;AAAA,cAC1B,UAAU;AAAA,gBACN,OAAO,IAAI,WAAW,MAAM,QAAW,IAAI,iBAAiB,CAAC;AAAA,gBAC7D,YAAY;AAAA,gBACZ,UAAU;AAAA,cACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAMA,YAAY;AAAA,gBACR,OAAO;AAAA,gBACP,YAAY;AAAA,gBACZ,UAAU;AAAA,cACd;AAAA,YACJ,CAAC;AACD,gBAAI,cAAc,KAAK,YAAY;AACnC,gBAAI,aAAa;AACb,qBAAO,iBAAiB,MAAM,WAAW;AAAA,YAC7C;AAIA,gBAAI,KAAK,CAAC,GAAG;AACT,mBAAK,OAAO,KAAK,CAAC,CAAC;AAAA,YACvB;AAAA,UACJ;AACA,UAAAA,QAAO,UAAU,SAAU,GAAG;AAC1B,oBAAQ,MAAM,CAAC;AAAA,UACnB;AACA,UAAAA,QAAO,KAAK,SAAUX,OAAM;AACxB,mBAAQA,MAAK,aAAa,KACtBA,MAAK,aAAa,EAAE,WAAW;AAAA,UACvC;AACA,UAAAW,QAAO,UAAU,WAAW,SAAU,UAAU;AAC5C,mBAAO,YAAa,KAAK,eAAe,KAAK,aAAa,CAAC,IAAKjB,SAAQ,UAAU,SAAS,QAAQ;AAAA,UACvG;AACA,UAAAiB,QAAO,UAAU,WAAW,SAAU,UAAU;AAC5C,mBAAO,YAAa,KAAK,eAAe,KAAK,aAAa,CAAC,IAAKjB,SAAQ,UAAU,QAAQ,QAAQ;AAAA,UACtG;AACA,UAAAiB,QAAO,UAAU,SAAS,SAAU,OAAO;AACvC,mBAAO,OAAO,MAAM,KAAK;AACzB,mBAAO;AAAA,UACX;AACA,iBAAO,eAAeA,QAAO,WAAW,eAAe;AAAA,YACnD,KAAK,WAAY;AAAE,qBAAO,KAAK,YAAY;AAAA,YAAa;AAAA,YACxD,YAAY;AAAA,YACZ,cAAc;AAAA,UAClB,CAAC;AAOD,UAAAA,QAAO,UAAU,WAAW,SAAU,UAAU,WAAW;AACvD,iBAAK,SAAS,OAAO,UAAU,SAAS;AAAA,UAC5C;AAOA,UAAAA,QAAO,UAAU,SAAS,SAAU,MAAM,UAAU,WAAW;AAC3D,gBAAI,QAAQ;AACZ,gBAAI,cAAc,QAAQ;AAAE,0BAAY;AAAA,YAAM;AAC9C,gBAAI,CAAC,KAAK,YAAY;AAClB,mBAAK,aAAa,CAAC;AAAA,YACvB;AACA,gBAAI,CAAC,KAAK,WAAW,IAAI,GAAG;AACxB,mBAAK,WAAW,IAAI,IAAI,CAAC;AAAA,YAC7B;AACA,iBAAK,WAAW,IAAI,EAAE,KAAK,QAAQ;AACnC,gBAAI,aAAa,KAAK,IAAI,MAAM,QAAW;AACvC,uBAAS,KAAK,IAAI,GAAG,MAAS;AAAA,YAClC;AAEA,mBAAO,WAAY;AAAE,qBAAO,UAAU,MAAM,WAAW,IAAI,GAAG,MAAM,WAAW,IAAI,EAAE,QAAQ,QAAQ,CAAC;AAAA,YAAG;AAAA,UAC7G;AACA,UAAAA,QAAO,UAAU,SAAS,SAAU,OAAO,IAAI,KAAK;AAChD,gBAAI,OAAO,QAAQ;AAAE,mBAAK,EAAE,QAAQ,EAAE;AAAA,YAAG;AACzC,gBAAI,QAAQ,QAAQ;AAAE,oBAAM;AAAA,YAAM;AAClC,gBAAI,aAAa,CAAC;AAClB,gBAAI,QAAQ,KAAK,SAAS;AAC1B,gBAAI,aAAa,MAAM;AACvB,gBAAI,QAAQ;AACZ,kBAAM,KAAK,IAAI,OAAO,IAAI;AAC1B,mBAAO,GAAG,SAAS,YAAY;AAC3B,kBAAI,OAAO,MAAM,GAAG,QAAQ;AAC5B,kBAAI,QAAQ,qBAAqB;AAC7B,wBAAQ,OAAO,OAAO,EAAE;AACxB,oBAAI,UAAU,MAAM,KAAK,IAAI,KAAK;AAIlC,oBAAI,CAAC,SAAS;AACV,wBAAM,IAAI,MAAM,sBAAwB,OAAO,KAAK,CAAC;AAAA,gBACzD;AACA,sBAAM;AACN;AAAA,cACJ;AACA,kBAAI,aAAa,IAAI,UAAU;AAC/B,kBAAI,WAAY,IAAI,aAAa,MAAM;AACvC,kBAAI,YAAa,WACV,QAAQ,KAAM,IACf;AACN,kBAAI,cAAcjB,SAAQ,UAAU,OAAO;AAMvC,oBAAI,MAAM,UAAU;AACpB;AAAA,cACJ;AACA,kBAAI,aAAc,WACZ,QAAQ,aAAa,OACrB,OAAO,OAAO,EAAE;AACtB,kBAAI,YAAa,WACV,IAAI,aAAa,EAAE,cAAc,UAAU,IAC5C;AACN,kBAAIM,QAAO,WAAW,QAAQ,UAAU;AACxC,kBAAI,QAAQ;AACZ,kBAAI,gBAAgB;AACpB,kBAAI,eAAe;AACnB,kBAAI,CAAC,UAAU;AACX,gCAAgB,IAAI,YAAY,EAAE,UAAU;AAC5C,qBAAK,YAAYN,SAAQ,UAAU,SAASA,SAAQ,UAAU,KAAK;AAC/D,iCAAgB,eAAe,YACzB,OAAO,OAAO,EAAE,IAChB;AACN,sBAAI,UAAU,EAAE,YAAY,YAAY;AAAA,gBAC5C,OACK;AAED,iCAAe,IAAI,UAAU,EAAE,UAAU;AAAA,gBAC7C;AAAA,cACJ,OACK;AACD,gCAAgB,IAAI,IAAI,OAAO,SAAS,CAAC;AAAA,cAC7C;AAIA,mBAAK,YAAYA,SAAQ,UAAU,YAAYA,SAAQ,UAAU,QAAQ;AACrE,oBAAI,cAAcA,SAAQ,UAAU,gBAAgB;AAChD,sBAAI,eAAe,EAAE,UAAU;AAAA,gBACnC;AAEA,oBAAI,iBAAiB,cAAc,UAAU,GAAG;AAC5C,wBAAM,UAAU,cAAc,UAAU,EAAE,KAAK;AAAA,gBACnD;AACA,wBAAQ;AAAA,cACZ;AACA,kBAAI,cAAc,QAAW;AACzB,wBAAQ,KAAK,uCAAuC;AAKpD,oBAAI,eAAe,EAAE,QAAQ,GAAG,OAAO;AACvC,uBAAO,GAAG,SAAS,YAAY;AAC3B,sBAAI,qBAAqB,OAAO,EAAE,GAAG;AACjC,iCAAa,SAAS,GAAG,SAAS;AAClC,wBAAI,MAAM,KAAK,IAAI,OAAO,OAAO,YAAY,CAAC,GAAG;AAC7C;AAAA,oBACJ;AAAA,kBACJ;AACA,qBAAG;AAAA,gBACP;AACA;AAAA,cACJ,WACS,cAAcA,SAAQ,UAAU;AAAQ;AAAA,uBACxCiB,QAAO,GAAGX,KAAI,GAAG;AACtB,oBAAI,UAAU,OAAO,OAAO,EAAE;AAC9B,wBAAQ,MAAM,KAAK,IAAI,OAAO;AAC9B,oBAAI,cAAcN,SAAQ,UAAU,SAAS;AACzC,sBAAI,YAAY,KAAK,cAAc,OAAO,IAAIM,KAAI;AAClD,sBAAI,CAAC,OAAO;AACR,4BAAQ,KAAK,mBAAmB,SAAS;AACzC,0BAAM,SAAS,QAAQ;AACvB,wBAAI,eAAe;AACf,4BAAM,aAAa,cAAc;AAEjC,0BAAI,cAAc,UAAU,EAAE,SAC1B,YAAY,cAAc,UAAU,EAAE,OAAO;AAC7C,8BAAM,UAAU,cAAc,UAAU,EAAE,KAAK;AAAA,sBACnD;AAAA,oBACJ;AAAA,kBACJ;AACA,wBAAM,OAAO,SAAS,OAAQ,UAAU,aAAc;AAAA,gBAC1D;AAAA,cACJ,WACS,OAAQA,UAAU,UAAU;AAIjC,wBAAQ,oBAAoBA,OAAM,OAAO,EAAE;AAAA,cAC/C,OACK;AACD,oBAAI,UAAU,QAAQ,OAAO,KAAKA,KAAI,EAAE,CAAC,CAAC;AAC1C,oBAAI,UAAU,OAAO,OAAO,EAAE;AAC9B,oBAAI,WAAY,MAAM,KAAK,IAAI,OAAO,IAChC,iBAAiB,MAAM,KAAK,IAAI,OAAO,IACvC,IAAI,QAAQ,YAAY;AAC9B,wBAAQ,SAAS,MAAM,IAAI;AAC3B,sBAAM,SAAS,QAAQ;AAEvB,oBAAI,eAAe;AACf,wBAAM,YAAY,IAAI,cAAc,YAAY;AAChD,sBAAI,cAAc,UAAU,EAAE,SAC1B,YAAY,cAAc,UAAU,EAAE,OAAO;AAC7C,0BAAM,UAAU,cAAc,UAAU,EAAE,KAAK;AAI/C,wBAAI,UAAU,cAAc,QAAQ;AACpC,wBAAI,OAAO;AACX,4BAAQ,OAAO,QAAQ,KAAK,MAAM,CAAC,KAAK,MAAM;AAC1C,0BAAI,KAAK,KAAK,OAAO,MAAM,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC;AAChD,iCAAW,KAAK;AAAA,wBACZ,OAAO;AAAA,wBACP,IAAIN,SAAQ,UAAU;AAAA,wBACtB,OAAO;AAAA,wBACP,OAAO;AAAA,wBACP,eAAe;AAAA,sBACnB,CAAC;AAAA,oBACL;AAAA,kBACJ;AAAA,gBACJ;AACA,sBAAM,OAAO,SAAS,OAAQ,aAAa,aAAc;AAAA,cAC7D;AACA,kBAAI,UAAU,QACV,UAAU,QAAW;AACrB,oBAAI,MAAM,UAAU,GAAG;AACnB,wBAAM,UAAU,EAAE,UAAU,WAAW,KAAK,WAAW,MAAM,UAAU;AAAA,gBAC3E;AACA,oBAAI,eAAeiB,SAAQ;AACvB,sBAAI,SAAS,IAAI;AAAA,gBAErB,WACS,eAAe,WAAW;AAE/B,sBAAI,MAAM;AAEV,sBAAI,QAAQ,EAAE,IAAI,KAAK,KAAK;AAC5B,sBAAI,UAAU,EAAE,WAAW,IAAI,UAAU;AAAA,gBAC7C,WACS,eAAe,aAAa;AAIjC,sBAAI,MAAM,YAAY,KAAK;AAAA,gBAC/B,WACS,eAAe,kBAAkB;AACtC,sBAAI,QAAQ,IAAI,IAAI,KAAK;AACzB,sBAAI,UAAU,EAAE,YAAY,KAAK;AAAA,gBACrC,WACS,eAAe,WAAW;AAC/B,sBAAI,QAAQ,IAAI,IAAI,KAAK;AACzB,sBAAI,UAAU,OAAO;AACjB,wBAAI,UAAU,EAAE,YAAY,KAAK;AAAA,kBACrC;AAAA,gBACJ;AAAA,cACJ;AACA,kBAAI,kBAAkB,OAAO;AACzB,2BAAW,KAAK;AAAA,kBACZ;AAAA,kBACA,IAAI;AAAA,kBACJ,OAAO;AAAA,kBACP;AAAA,kBACA;AAAA,kBACA;AAAA,gBACJ,CAAC;AAAA,cACL;AAAA,YACJ;AACA,iBAAK,gBAAgB,UAAU;AAE/B,kBAAM,0BAA0B;AAChC,mBAAO;AAAA,UACX;AACA,UAAAA,QAAO,UAAU,SAAS,SAAU,WAAW,OAAO,YAAY;AAC9D,gBAAI,cAAc,QAAQ;AAAE,0BAAY;AAAA,YAAO;AAC/C,gBAAI,UAAU,QAAQ;AAAE,sBAAQ,CAAC;AAAA,YAAG;AACpC,gBAAI,eAAe,QAAQ;AAAE,2BAAa;AAAA,YAAO;AACjD,gBAAI,iBAAiB,KAAK;AAC1B,gBAAI,gBAAgB,oBAAI,QAAQ;AAChC,gBAAI,cAAc,CAAC,cAAc;AACjC,gBAAI,iBAAiB;AACrB,qBAAS,IAAI,GAAG,IAAI,gBAAgB,KAAK;AACrC,kBAAI,aAAa,YAAY,CAAC;AAC9B,kBAAI,MAAM,WAAW;AACrB,kBAAI,WAAY,eAAeA;AAE/B,yBAAW,YAAY;AAEvB,4BAAc,IAAI,UAAU;AAE5B,kBAAI,eAAe,mBACd,WAAW,WAAW,YAAY;AACnC,wBAAQ,OAAO,mBAAmB;AAClC,yBAAS,OAAO,WAAW,KAAK;AAAA,cACpC;AACA,kBAAI,UAAW,YACT,MAAM,KAAK,WAAW,UAAU,IAChC,MAAM,KAAK,WAAW,QAAQ,OAAO,CAAC;AAC5C,uBAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK;AAC9C,oBAAI,YAAa,YACX,EAAE,IAAIjB,SAAQ,UAAU,KAAK,OAAO,QAAQ,CAAC,EAAE,IAC/C,QAAQ,CAAC;AACf,oBAAI,aAAa,UAAU;AAC3B,oBAAI,QAAS,WACP,IAAI,aAAa,EAAE,iBAAiB,IAAI,aAAa,EAAE,cAAc,UAAU,IAC/E;AAEN,oBAAI,aAAa,MAAM;AAEvB,oBAAI,UAAU,OAAOA,SAAQ,UAAU,OAAO;AAC1C,sBAAI,UAAU;AAKV,4BAAQ,OAAQ,aAAa,UAAU,EAAG;AAAA,kBAC9C,OACK;AACD,4BAAQ,OAAO,UAAU,EAAE;AAE3B,wBAAI,UAAU,OAAOA,SAAQ,UAAU,OAAO;AAC1C;AAAA,oBACJ;AAEA,6BAAS,OAAO,UAAU;AAAA,kBAC9B;AAAA,gBACJ;AAIA,oBAAI,CAAC,aACA,UAAU,KAAKA,SAAQ,UAAU,QAAQA,SAAQ,UAAU,KAC9D;AACE,sBAAI,eAAe,WAAW;AAI1B,wBAAI,eAAe,WAAW,IAAI,UAAU,EAAE,IAAI,UAAU;AAC5D,6BAAS,OAAO,YAAY;AAAA,kBAChC;AAAA,gBACJ;AACA,oBAAI,UAAU,OAAOA,SAAQ,UAAU,QAAQ;AAO3C;AAAA,gBACJ;AAEA,oBAAIM,QAAO,WAAW,QAAQ,UAAU;AAExC,oBAAI,QAAQ,WAAW,SAAS,UAAU;AAE1C,oBAAI,SACA,MAAM,UAAU,KAChB,CAAC,cAAc,IAAI,MAAM,UAAU,CAAC,GAAG;AACvC,8BAAY,KAAK,MAAM,UAAU,CAAC;AAClC,wBAAM,UAAU,EAAE,YAAY;AAC9B;AAAA,gBACJ;AACA,oBAAI,UAAU,OAAON,SAAQ,UAAU,OAAO;AAC1C;AAAA,gBACJ;AACA,oBAAIiB,QAAO,GAAGX,KAAI,GAAG;AACjB,qCAAmB,OAAOA,OAAM,KAAK,KAAK;AAK1C,2BAAS,OAAO,MAAM,SAAS,KAAK;AAEpC,uBAAK,UAAU,KAAKN,SAAQ,UAAU,SAASA,SAAQ,UAAU,KAAK;AAClE,yBAAK,gBAAgB,OAAOM,OAAM,MAAM,WAAW;AAAA,kBACvD;AAAA,gBACJ,WACS,OAAQA,UAAU,UAAU;AAIjC,sCAAoBA,OAAM,OAAO,OAAO,KAAK,KAAK;AAAA,gBACtD,OACK;AAID,sBAAI,aAAa,QAAQ,OAAO,KAAKA,KAAI,EAAE,CAAC,CAAC;AAI7C,qCAAmB,IAAI,IAAI,OAAO,KAAK,CAAC,GAAG,WAAW,aAAa,KAAK,KAAK;AAK7E,2BAAS,OAAO,MAAM,SAAS,KAAK;AAAA,gBACxC;AACA,oBAAI,YAAY;AAEZ,6BAAW,MAAM,YAAY,MAAM,MAAM,UAAU,CAAC;AAAA,gBACxD;AAAA,cACJ;AACA,kBAAI,CAAC,aAAa,CAAC,YAAY;AAC3B,2BAAW,QAAQ;AAAA,cACvB;AAAA,YACJ;AACA,mBAAO;AAAA,UACX;AACA,UAAAW,QAAO,UAAU,YAAY,SAAU,YAAY;AAC/C,mBAAO,KAAK,OAAO,MAAM,CAAC,GAAG,UAAU;AAAA,UAC3C;AACA,UAAAA,QAAO,UAAU,eAAe,SAAU,QAAQ,WAAW;AACzD,gBAAI,IAAI;AACR,gBAAI,cAAc,QAAQ;AAAE,0BAAY;AAAA,YAAO;AAC/C,gBAAI,OAAO;AACX,gBAAI,oBAAoB,oBAAI,IAAI;AAChC,gBAAI,eAAe,YAAY,IAAI,MAAM;AACzC,gBAAI,cAAc,CAAC,KAAK,QAAQ;AAChC,gBAAI,iBAAiB;AACrB,gBAAI,gBAAgB,CAAC;AACrB,gBAAI,UAAU,SAAUC,IAAG;AACvB,kBAAI,aAAa,YAAYA,EAAC;AAC9B,kBAAI,kBAAkB,IAAI,WAAW,KAAK,GAAG;AACzC,uBAAO;AAAA,cACX;AACA,kBAAI,MAAM,WAAW;AACrB,kBAAI,WAAW,eAAeD;AAC9B,sBAAQ,eAAe,mBAAmB;AAC1C,uBAAS,eAAe,WAAW,KAAK;AACxC,kBAAI,iBAAiB,aAAa,OAAO,IAAI,UAAU;AACvD,kBAAI,cAAe,aAAa,CAAC;AAMjC,2BAAa,SAAS,UAAU;AAChC,kBAAI,mBAAmB,aAAa,iBAAiB,IAAI,UAAU;AACnE,kBAAI,UAAW,cACT,MAAM,KAAK,WAAW,UAAU,IAChC,MAAM,KAAK,WAAW,QAAQ,OAAO,CAAC;AAK5C,kBAAI,CAAC,aACD,YACA,IAAI,YAAY,oBAAoB;AACpC,oBAAI,qBAAqB,IAAI,YAAY;AACzC,mCAAmB,QAAQ,SAAU,iBAAiB;AAClD,sBAAI,CAAC,iBAAiB,IAAI,eAAe,KACrC,WAAW,WAAW,IAAI,eAAe,GAAG;AAC5C,wBAAI,aAAa;AACb,8BAAQ,KAAK,eAAe;AAAA,oBAChC,OACK;AACD,8BAAQ,KAAK,EAAE,IAAIjB,SAAQ,UAAU,KAAK,OAAO,gBAAiB,CAAC;AAAA,oBACvE;AAAA,kBACJ;AAAA,gBACJ,CAAC;AAAA,cACL;AACA,uBAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK;AAC9C,oBAAI,SAAU,cACR,EAAE,IAAIA,SAAQ,UAAU,KAAK,OAAO,QAAQ,CAAC,EAAE,IAC/C,QAAQ,CAAC;AAEf,oBAAI,OAAO,OAAOA,SAAQ,UAAU,OAAO;AACvC,0BAAQ,eAAe,OAAO,EAAE;AAChC;AAAA,gBACJ;AACA,oBAAI,aAAa,OAAO;AAIxB,oBAAI,OAAO,OAAOA,SAAQ,UAAU,QAAQ;AAOxC,sBAAI,UAAU;AACV,4BAAQ,eAAe,OAAO,KAAK,UAAU;AAAA,kBACjD,OACK;AACD,4BAAQ,eAAe,OAAO,EAAE;AAChC,6BAAS,eAAe,UAAU;AAAA,kBACtC;AACA;AAAA,gBACJ;AAEA,oBAAI,QAAQ,WAAW,SAAS,UAAU;AAC1C,oBAAIM,QAAO,WAAW,QAAQ,UAAU;AACxC,oBAAI,UAAU;AAEV,sBAAIa,UAAU,IAAI,YAAY,WAC1B,IAAI,YAAY,QAAQ,UAAU;AACtC,sBAAIA,WAAU,CAACA,QAAO,KAAK,KAAK,QAAQ,OAAO,IAAI,GAAG;AAClD,wBAAI,SAAS,MAAM,UAAU,GAAG;AAC5B,wCAAkB,IAAI,MAAM,UAAU,EAAE,KAAK;AAAA,oBACjD;AACA;AAAA,kBACJ;AAAA,gBACJ,OACK;AAED,sBAAI,SAAS,WAAW;AACxB,sBAAIA,UAAS,WAAW,kBAAkB;AAC1C,sBAAIA,WAAU,CAACA,QAAO,KAAK,QAAQ,QAAQ,IAAI,UAAU,EAAE,IAAI,UAAU,GAAG,OAAO,IAAI,GAAG;AACtF,wBAAI,SAAS,MAAM,UAAU,GAAG;AAC5B,wCAAkB,IAAI,MAAM,UAAU,EAAE,KAAK;AAAA,oBACjD;AACA;AAAA,kBACJ;AAAA,gBACJ;AAEA,oBAAI,MAAM,UAAU,GAAG;AACnB,8BAAY,KAAK,MAAM,UAAU,CAAC;AAClC;AAAA,gBACJ;AAIA,oBAAI,OAAO,OAAOnB,SAAQ,UAAU,OAAO;AAIvC,sBAAI,OAAO,OAAOA,SAAQ,UAAU,OAAO,UAAU;AAIjD,kCAAc,KAAK,MAAM,gBAAgB,KAAK,WAAW,OAAO,UAAU,OAAO,QAAQ,OAAO,SAAS,KAAK,CAAC,CAAC;AAChH,qCAAiB,IAAI,UAAU;AAAA,kBACnC,OACK;AACD,wBAAI,iBAAiB,IAAI,UAAU,GAAG;AAIlC,oCAAc,KAAK,MAAM,gBAAgB,KAAK,WAAW,OAAO,UAAU,OAAO,QAAQ,OAAO,SAAS,KAAK,CAAC,CAAC;AAAA,oBACpH,OACK;AAID,uCAAiB,IAAI,UAAU;AAC/B,8BAAQ,eAAeA,SAAQ,UAAU,GAAG;AAC5C,+BAAS,eAAe,UAAU;AAClC,0BAAI,eAAe,WAAW;AAI1B,4BAAI,eAAe,WAAW,IAAI,UAAU,EAAE,IAAI,UAAU;AAC5D,iCAAS,eAAe,YAAY;AAAA,sBACxC;AACA,0BAAI,MAAM,UAAU,GAAG;AACnB,iCAAS,eAAe,MAAM,UAAU,EAAE,KAAK;AAAA,sBACnD,OACK;AAGD,+BAAOM,KAAI,EAAE,eAAe,KAAK;AAAA,sBACrC;AAAA,oBACJ;AAAA,kBACJ;AAAA,gBACJ,WACS,MAAM,UAAU,KAAK,CAAC,UAAU;AAOrC,0BAAQ,eAAeN,SAAQ,UAAU,GAAG;AAC5C,2BAAS,eAAe,UAAU;AAClC,sBAAI,eAAe,WAAW;AAI1B,wBAAI,eAAe,WAAW,IAAI,UAAU,EAAE,IAAI,UAAU;AAC5D,6BAAS,eAAe,YAAY;AAAA,kBACxC;AACA,2BAAS,eAAe,MAAM,UAAU,EAAE,KAAK;AAAA,gBACnD;AAAA,cACJ;AAAA,YACJ;AACA,qBAAS,IAAI,GAAG,IAAI,gBAAgB,KAAK;AACrC,sBAAQ,CAAC;AAAA,YACb;AACA,mBAAO;AAAA,UACX;AACA,UAAAiB,QAAO,UAAU,QAAQ,WAAY;AACjC,gBAAI;AACJ,gBAAI,SAAS,IAAK,KAAK;AACvB,gBAAI,SAAS,KAAK,YAAY;AAC9B,qBAAS,SAAS,QAAQ;AACtB,kBAAI,OAAQ,KAAK,KAAK,MAAO,YACzB,SAAS,KAAK,KAAK,KAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,YAAY;AAE1F,uBAAO,KAAK,IAAI,KAAK,KAAK,EAAE,MAAM;AAAA,cACtC,OACK;AAED,uBAAO,KAAK,IAAI,KAAK,KAAK;AAAA,cAC9B;AAAA,YACJ;AACA,mBAAO;AAAA,UACX;AACA,UAAAA,QAAO,UAAU,SAAS,WAAY;AAClC,gBAAI,SAAS,KAAK,YAAY;AAC9B,gBAAIG,cAAa,KAAK,YAAY;AAClC,gBAAI,MAAM,CAAC;AACX,qBAAS,SAAS,QAAQ;AACtB,kBAAI,CAACA,YAAW,KAAK,KAAK,KAAK,KAAK,MAAM,QAAQ,OAAQ,KAAK,KAAK,MAAO,aAAa;AACpF,oBAAI,KAAK,IAAK,OAAQ,KAAK,KAAK,EAAE,QAAQ,MAAO,aAC3C,KAAK,KAAK,EAAE,QAAQ,EAAE,IACtB,KAAK,IAAI,OAAO,KAAK,CAAC;AAAA,cAChC;AAAA,YACJ;AACA,mBAAO;AAAA,UACX;AACA,UAAAH,QAAO,UAAU,oBAAoB,WAAY;AAC7C,iBAAK,SAAS,WAAW;AAAA,UAC7B;AACA,UAAAA,QAAO,UAAU,aAAa,SAAU,OAAO;AAC3C,mBAAO,KAAK,KAAK,YAAY,cAAc,KAAK,CAAC;AAAA,UACrD;AACA,UAAAA,QAAO,UAAU,gBAAgB,SAAU,OAAO;AAC9C,iBAAK,KAAK,YAAY,cAAc,KAAK,CAAC,IAAI;AAAA,UAClD;AACA,UAAAA,QAAO,UAAU,kBAAkB,SAAU,OAAOX,OAAM,YAAY;AAClE,gBAAIA,MAAK,YAAY,WAAW,SAAS;AACrC,sBAAQ,OAAO,OAAO;AACtB,uBAAS,OAAO,WAAW,OAAO;AAAA,YACtC;AAAA,UACJ;AACA,UAAAW,QAAO,UAAU,gBAAgB,SAAU,OAAO,IAAI,aAAa;AAC/D,gBAAIX;AACJ,gBAAI,MAAM,GAAG,MAAM,MAAM,SAAS;AAC9B,iBAAG;AACH,cAAAA,QAAO,KAAK,YAAY,SAAS,IAAI,OAAO,OAAO,EAAE,CAAC;AAAA,YAC1D;AACA,mBAAOA,SAAQ;AAAA,UACnB;AACA,UAAAW,QAAO,UAAU,qBAAqB,SAAUX,OAAM;AAClD,gBAAI,WAAW,IAAIA,MAAK;AAExB,qBAAS,SAAS,OAAO,KAAK,SAAS;AACvC,mBAAO;AAAA,UACX;AACA,UAAAW,QAAO,UAAU,kBAAkB,SAAU,SAAS;AAClD,gBAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACpC,gBAAI,eAAe,oBAAI,IAAI;AAC3B,gBAAI,QAAQ,KAAK,SAAS,KAAK;AAC/B,gBAAI,UAAU,SAAUC,IAAG;AACvB,kBAAI,SAAS,QAAQA,EAAC;AACtB,kBAAI,QAAQ,OAAO;AACnB,kBAAI,MAAM,MAAM,IAAI,KAAK;AACzB,kBAAI,aAAa,IAAI,YAAY;AAIjC,mBAAK,OAAO,KAAKlB,SAAQ,UAAU,YAAYA,SAAQ,UAAU,UAC7D,OAAO,yBAAyBiB,SAAQ;AACxC,iBAAC,MAAM,KAAK,OAAO,cAAc,YAAY,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAGjB,SAAQ,UAAU,MAAM,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,SAAU,UAAU;AAAE,yBAAO,SAAS;AAAA,gBAAG,CAAC;AAAA,cACjN;AAEA,kBAAI,CAAC,YAAY;AACb,uBAAO;AAAA,cACX;AACA,kBAAI,eAAeiB,SAAQ;AACvB,oBAAI,CAAC,aAAa,IAAI,KAAK,GAAG;AAC1B,sBAAI;AAEA,qBAAC,KAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAWjB,SAAQ,UAAU,OAAO,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,SAAU,UAAU;AACrK,6BAAO,SAAS;AAAA,oBACpB,CAAC;AAAA,kBACL,SACO,GAAG;AACN,oBAAAiB,QAAO,QAAQ,CAAC;AAAA,kBACpB;AAAA,gBACJ;AACA,oBAAI;AACA,sBAAI,WAAW,eAAe,OAAO,KAAK,GAAG;AACzC,qBAAC,KAAK,WAAW,OAAO,KAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,SAAU,UAAU;AAChG,6BAAO,SAAS,OAAO,OAAO,OAAO,aAAa;AAAA,oBACtD,CAAC;AAAA,kBACL;AAAA,gBACJ,SACO,GAAG;AACN,kBAAAA,QAAO,QAAQ,CAAC;AAAA,gBACpB;AAAA,cACJ,OACK;AAED,oBAAI,OAAO,OAAOjB,SAAQ,UAAU,OAAO,OAAO,kBAAkB,QAAW;AAE3E,mBAAC,KAAK,WAAWA,SAAQ,UAAU,GAAG,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,SAAU,UAAU;AAAE,wBAAIqB;AAAI,2BAAO,SAAS,OAAO,QAAQA,MAAK,OAAO,kBAAkB,QAAQA,QAAO,SAASA,MAAK,OAAO,KAAK;AAAA,kBAAG,CAAC;AAAA,gBACrO,WACS,OAAO,OAAOrB,SAAQ,UAAU,QAAQ;AAK7C,sBAAI,OAAO,kBAAkB,QAAW;AAEpC,qBAAC,KAAK,WAAWA,SAAQ,UAAU,MAAM,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,SAAU,UAAU;AAAE,0BAAIqB;AAAI,6BAAO,SAAS,OAAO,gBAAgBA,MAAK,OAAO,kBAAkB,QAAQA,QAAO,SAASA,MAAK,OAAO,KAAK;AAAA,oBAAG,CAAC;AAAA,kBAChP;AAAA,gBACJ,WACS,OAAO,OAAOrB,SAAQ,UAAU,gBAAgB;AAErD,sBAAI,OAAO,kBAAkB,QAAW;AACpC,qBAAC,KAAK,WAAWA,SAAQ,UAAU,MAAM,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,SAAU,UAAU;AAAE,0BAAIqB;AAAI,6BAAO,SAAS,OAAO,gBAAgBA,MAAK,OAAO,kBAAkB,QAAQA,QAAO,SAASA,MAAK,OAAO,KAAK;AAAA,oBAAG,CAAC;AAAA,kBAChP;AAEA,mBAAC,KAAK,WAAWrB,SAAQ,UAAU,GAAG,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,SAAU,UAAU;AAAE,wBAAIqB;AAAI,2BAAO,SAAS,OAAO,QAAQA,MAAK,OAAO,kBAAkB,QAAQA,QAAO,SAASA,MAAK,OAAO,KAAK;AAAA,kBAAG,CAAC;AAAA,gBACrO;AAEA,oBAAI,OAAO,UAAU,OAAO,eAAe;AACvC,mBAAC,KAAK,WAAWrB,SAAQ,UAAU,OAAO,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,SAAU,UAAU;AAAE,wBAAIqB;AAAI,2BAAO,SAAS,OAAO,QAAQA,MAAK,OAAO,kBAAkB,QAAQA,QAAO,SAASA,MAAK,OAAO,KAAK;AAAA,kBAAG,CAAC;AAAA,gBACzO;AAAA,cACJ;AACA,2BAAa,IAAI,KAAK;AAAA,YAC1B;AACA,qBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,sBAAQ,CAAC;AAAA,YACb;AAAA,UACJ;AACA,UAAAJ,QAAO,cAAc,iBAAiB,OAAO;AAC7C,iBAAOA;AAAA,QACX,EAAE;AAAA;AAEF,eAAS,YAAY,QAAQ;AACzB,YAAI,cAAc,CAAC,OAAO,UAAU,CAAC;AACrC,YAAI,iBAAiB;AACrB,YAAI,OAAO,CAAC;AACZ,YAAI,mBAAmB;AACvB,YAAI,UAAU,SAAUC,IAAG;AACvB,cAAI,aAAa,YAAYA,EAAC;AAC9B,qBAAW,QAAQ,QAAQ,SAAU,QAAQ;AACzC,gBAAI,MAAM,WAAW;AACrB,gBAAI,aAAa,OAAO;AACxB,gBAAI,QAAS,IAAI,aAAa,IACxB,IAAI,aAAa,EAAE,cAAc,UAAU,IAC3C,IAAI,UAAU,EAAE,IAAI,UAAU;AACpC,6BAAiB,KAAK,IAAI,WAAW,SAAS,UAAU;AAAA,UAC5D,CAAC;AAAA,QACL;AACA,iBAAS,IAAI,GAAG,IAAI,gBAAgB,KAAK;AACrC,kBAAQ,CAAC;AAAA,QACb;AACA,eAAO;AAAA,MACX;AAEA,UAAI,oBAAoB,EAAE,SAAS,IAAI,QAAQ,EAAE;AAIjD,UAAI;AAAA;AAAA,QAAiC,SAAU,QAAQ;AACnD,oBAAUI,kBAAiB,MAAM;AACjC,mBAASA,mBAAkB;AACvB,mBAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,UAC/D;AACA,qBAAW;AAAA,YACP,KAAK,UAAU,iBAAiB;AAAA,UACpC,GAAGA,iBAAgB,WAAW,QAAQ,MAAM;AAC5C,qBAAW;AAAA,YACP,KAAK,UAAU,iBAAiB;AAAA,UACpC,GAAGA,iBAAgB,WAAW,QAAQ,MAAM;AAC5C,qBAAW;AAAA,YACP,KAAK,UAAU,iBAAiB;AAAA,UACpC,GAAGA,iBAAgB,WAAW,kBAAkB,MAAM;AACtD,iBAAOA;AAAA,QACX,EAAE,MAAM;AAAA;AACR,UAAI;AAAA;AAAA,QAAgC,SAAU,QAAQ;AAClD,oBAAUC,iBAAgB,MAAM;AAChC,mBAASA,kBAAiB;AACtB,gBAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,kBAAM,SAAS,IAAI,YAAY;AAC/B,mBAAO;AAAA,UACX;AACA,qBAAW;AAAA,YACP,KAAK,UAAU,iBAAiB;AAAA,UACpC,GAAGA,gBAAe,WAAW,MAAM,MAAM;AACzC,qBAAW;AAAA,YACP,KAAK,CAAC,eAAe,GAAG,iBAAiB;AAAA,UAC7C,GAAGA,gBAAe,WAAW,UAAU,MAAM;AAC7C,iBAAOA;AAAA,QACX,EAAE,MAAM;AAAA;AACR,UAAI;AAAA;AAAA,QAA4B,SAAU,QAAQ;AAC9C,oBAAUC,aAAY,MAAM;AAC5B,mBAASA,cAAa;AAClB,gBAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,kBAAM,QAAQ,IAAI,YAAY;AAC9B,mBAAO;AAAA,UACX;AACA,UAAAA,YAAW,SAAS,SAAU,UAAU;AACpC,gBAAI;AACJ,gBAAI,iBAAiB,SAAS;AAC9B,gBAAI,aAAa,IAAIA,YAAW;AAChC,uBAAW,WAAW,eAAe;AACrC,gBAAI,YAAY,SAAU,aAAa,QAAQ;AAC3C,uBAAS,aAAa,QAAQ;AAC1B,oBAAI,QAAQ,IAAI,gBAAgB;AAChC,sBAAM,OAAO;AACb,oBAAI,YAAY;AAChB,oBAAI,OAAQ,OAAO,SAAS,MAAO,UAAU;AACzC,8BAAY,OAAO,SAAS;AAAA,gBAChC,OACK;AACD,sBAAI,SAAS,OAAO,SAAS;AAC7B,sBAAI,kBAAkB;AAItB,sBAAI,OAAO,GAAG,MAAM,GAAG;AACnB,gCAAY;AACZ,sCAAkB,OAAO,SAAS;AAAA,kBACtC,OACK;AACD,gCAAY,OAAO,KAAK,MAAM,EAAE,CAAC;AACjC,wBAAI,OAAQ,OAAO,SAAS,MAAO,UAAU;AACzC,mCAAa,MAAM,OAAO,SAAS;AAAA,oBACvC,OACK;AACD,wCAAkB,OAAO,SAAS;AAAA,oBACtC;AAAA,kBACJ;AACA,wBAAM,iBAAkB,kBAClB,gBAAgB,UAChB;AAAA,gBACV;AACA,sBAAM,OAAO;AACb,4BAAY,OAAO,KAAK,KAAK;AAAA,cACjC;AACA,yBAAW,MAAM,KAAK,WAAW;AAAA,YACrC;AACA,gBAAI,SAAS,KAAK,eAAe,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG;AACnF,qBAAS,UAAU,OAAO;AACtB,kBAAI,SAAS,IAAI,eAAe;AAChC,qBAAO,KAAK,OAAO,MAAM;AACzB,wBAAU,QAAQ,MAAM,MAAM,EAAE,YAAY,MAAM;AAAA,YACtD;AACA,mBAAO,WAAW,UAAU;AAAA,UAChC;AACA,UAAAA,YAAW,SAAS,SAAU,OAAO,IAAI;AACrC,gBAAI,UAAU,IAAI,QAAQ;AAC1B,gBAAI,aAAa,IAAIA,YAAW;AAChC,uBAAW,OAAO,OAAO,EAAE;AAC3B,gBAAI,cAAc,WAAW,MAAM,OAAO,SAAU,OAAO,gBAAgB;AACvE,kBAAI;AAAA;AAAA,gBAAwB,SAAUC,SAAQ;AAC1C,4BAAU,GAAGA,OAAM;AACnB,2BAAS,IAAI;AACT,2BAAOA,YAAW,QAAQA,QAAO,MAAM,MAAM,SAAS,KAAK;AAAA,kBAC/D;AACA,yBAAO;AAAA,gBACX,EAAE,MAAM;AAAA;AACR,kBAAI,SAAS,eAAe;AAC5B,oBAAM,MAAM,IAAI;AAChB,sBAAQ,IAAI,QAAQ,MAAM;AAC1B,qBAAO;AAAA,YACX,GAAG,CAAC,CAAC;AACL,uBAAW,MAAM,QAAQ,SAAU,gBAAgB;AAC/C,kBAAI,aAAa,YAAY,eAAe,EAAE;AAC9C,6BAAe,OAAO,QAAQ,SAAU,OAAO;AAC3C,oBAAI;AACJ,oBAAI,MAAM,mBAAmB,QAAW;AACpC,sBAAIC,aAAY,MAAM;AACtB,sBAAI,UAAU,YAAY,MAAM,cAAc;AAE9C,sBAAI,CAAC,SAAS;AACV,wBAAI,WAAW,MAAM,KAAK,MAAM,GAAG;AACnC,oBAAAA,aAAY,SAAS,CAAC;AACtB,8BAAU,SAAS,CAAC;AAAA,kBACxB;AACA,sBAAIA,eAAc,OAAO;AACrB,yBAAK,SAAS,EAAE,QAAiB,CAAC,EAAE,WAAW,WAAW,MAAM,IAAI;AAAA,kBACxE,OACK;AACD,0BAAM,KAAK,CAAC,GAAG,GAAGA,UAAS,IAAI,SAAS,KAAK,EAAE,QAAiB,CAAC,EAAE,WAAW,WAAW,MAAM,IAAI;AAAA,kBACvG;AAAA,gBACJ,OACK;AACD,uBAAK,MAAM,MAAM,EAAE,QAAiB,CAAC,EAAE,WAAW,WAAW,MAAM,IAAI;AAAA,gBAC3E;AAAA,cACJ,CAAC;AAAA,YACL,CAAC;AACD,gBAAI,WAAW,YAAY,WAAW,QAAQ;AAC9C,gBAAI,eAAe,IAAI,SAAS;AAKhC,qBAAS,aAAa,SAAS,YAAY,QAAQ;AAC/C,kBAAI,YAAY,SAAS,YAAY,OAAO,SAAS;AACrD,kBAAI,OAAQ,cAAe,UAAU;AACjC,6BAAa,SAAS,IAAK,OAAQ,cAAe,aAC5C,IAAI,UAAU,IACd,KAAK,QAAQ,OAAO,KAAK,SAAS,EAAE,CAAC,CAAC,GAAG,YAAY;AAAA,cAC/D;AAAA,YACJ;AACA,mBAAO;AAAA,UACX;AACA,qBAAW;AAAA,YACP,KAAK,CAAC,cAAc,GAAG,iBAAiB;AAAA,UAC5C,GAAGF,YAAW,WAAW,SAAS,MAAM;AACxC,qBAAW;AAAA,YACP,KAAK,UAAU,iBAAiB;AAAA,UACpC,GAAGA,YAAW,WAAW,YAAY,MAAM;AAC3C,iBAAOA;AAAA,QACX,EAAE,MAAM;AAAA;AAER,mBAAa,OAAO,EAAE,aAAa,UAAU,CAAC;AAC9C,mBAAa,SAAS,EAAE,aAAa,YAAY,CAAC;AAClD,mBAAa,OAAO,EAAE,aAAa,UAAU,CAAC;AAC9C,mBAAa,cAAc,EAAE,aAAa,iBAAkB,CAAC;AAE7D,MAAAxB,SAAQ,cAAc;AACtB,MAAAA,SAAQ,mBAAmB;AAC3B,MAAAA,SAAQ,UAAU;AAClB,MAAAA,SAAQ,YAAY;AACpB,MAAAA,SAAQ,aAAa;AACrB,MAAAA,SAAQ,kBAAkB;AAC1B,MAAAA,SAAQ,iBAAiB;AACzB,MAAAA,SAAQ,SAAS;AACjB,MAAAA,SAAQ,mBAAmB;AAC3B,MAAAA,SAAQ,YAAY;AACpB,MAAAA,SAAQ,SAAS;AACjB,MAAAA,SAAQ,cAAc;AACtB,MAAAA,SAAQ,aAAa;AACrB,MAAAA,SAAQ,cAAc;AACtB,MAAAA,SAAQ,SAAS;AACjB,MAAAA,SAAQ,SAAS;AACjB,MAAAA,SAAQ,iBAAiB;AACzB,MAAAA,SAAQ,YAAY;AACpB,MAAAA,SAAQ,eAAe;AACvB,MAAAA,SAAQ,OAAO;AAAA,IAEnB,CAAE;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACj0GF,QAAA,UAAA,aAAA,iBAAA;AAEA,QAAA,eAAA;AACA,QAAA,aAAA;AACA,QAAA,eAAA;AAIA,QAAA,eAAA;AACA,QAAA,WAAA;AAEA,QAAA,WAAA;AAEA,QAAA,gBAAA;AAUA,QAAa,OAAb,MAAa,MAAI;MAwBb,YAAY,MAAc,YAAqC;AAfxD,aAAA,iBAAgB,GAAA,SAAA,cAAY;AAC5B,aAAA,WAAU,GAAA,SAAA,cAAY;AACtB,aAAA,WAAU,GAAA,SAAA,cAAY;AACnB,aAAA,UAAS,GAAA,SAAA,cAAY;AAKrB,aAAA,YAAqB;AAKrB,aAAA,qBAAoB,GAAA,aAAA,kBAAgB;AAG1C,aAAK,SAAS;AACd,aAAK,OAAO;AAEZ,YAAI,YAAY;AACZ,eAAK,aAAa,MAAK,GAAA,aAAA,eAAc,QAAQ;AAC7C,eAAK,aAAa;AACjB,eAAK,WAAgC,QAAQ,IAAI,WAAU;;AAGhE,aAAK,QAAQ,CAAC,MAAM,YAAW;AAAA,cAAA;AAAC,kBAAA,KAAA,QAAQ,UAAI,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,SAAG,6BAA6B,IAAI,KAAK,OAAO,EAAE;QAAC,CAAA;AAC/F,aAAK,QAAQ,MAAM,KAAK,mBAAkB,CAAE;MAChD;;MAGA,IAAI,KAAE;AAAK,eAAO,KAAK;MAAQ;MAExB,QACH,UACA,sBACA,OAAa,MACb,SAAa;AAEb,cAAM,aAAa,IAAI,aAAA,WAAU;AACjC,aAAK,aAAa;AAElB,mBAAW,OAAO,YAAY,MAAK,UAAU,kBAAkB,KAAK,IAAI;AACxE,mBAAW,OAAO,UAAU,SAAU,GAAa;;AAC/C,cAAI,CAAC,KAAK,WAAW;AACjB,aAAA,KAAA,QAAQ,UAAI,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,SAAG,4CAA4C,EAAE,IAAI,MAAM,EAAE,MAAM,EAAE;AACjF,iBAAK,QAAQ,OAAO,EAAE,MAAM,EAAE,MAAM;AACpC;;AAEJ,cAAI,EAAE,SAAS,cAAA,UAAU,mBAAmB,sBAAsB;AAC9D,iCAAoB;iBACjB;AACH,iBAAK,QAAQ,OAAO,EAAE,MAAM,EAAE,MAAM;AACpC,iBAAK,QAAO;;QAEpB;AACA,mBAAW,OAAO,UAAU,SAAU,GAAa;;AAC/C,WAAA,KAAA,QAAQ,UAAI,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,SAAG,kBAAkB,EAAE,IAAI,MAAM,EAAE,MAAM,EAAE;AACvD,eAAK,QAAQ,OAAO,EAAE,MAAM,EAAE,MAAM;QACxC;AAEA,mBAAW,QAAQ,UAAU,OAAO;MACxC;MAEO,MAAM,YAAqB,MAAI;AAClC,eAAO,IAAI,QAAQ,CAAC,YAAW;AAC3B,eAAK,QAAQ,CAAC,SAAS,QAAQ,IAAI,CAAC;AAEpC,cAAI,KAAK,YAAY;AACjB,gBAAI,WAAW;AACX,mBAAK,WAAW,KAAK,CAAC,WAAA,SAAS,UAAU,CAAC;mBAEvC;AACH,mBAAK,WAAW,MAAK;;iBAGtB;AACH,iBAAK,QAAQ,OAAO,cAAA,UAAU,SAAS;;QAE/C,CAAC;MACL;MAcO,UACH,MACA,UAAkC;AAElC,eAAO,KAAK,kBAAkB,GAAG,KAAK,qBAAqB,IAAI,GAAG,QAAQ;MAC9E;MAEO,KAAc,MAAuB,SAAW;AACnD,cAAM,eAAyB,CAAC,WAAA,SAAS,SAAS;AAElD,YAAI,OAAO,SAAU,UAAU;AAC3B,mBAAA,OAAO,OAAO,cAAc,IAAI;eAE7B;AACH,mBAAA,OAAO,OAAO,cAAc,IAAI;;AAGpC,YAAI;AAEJ,YAAI,YAAY,QAAW;AACvB,gBAAM,UAAU,QAAQ,OAAO,OAAO;AACtC,gBAAM,IAAI,WAAW,aAAa,SAAS,QAAQ,UAAU;AAC7D,cAAI,IAAI,IAAI,WAAW,YAAY,GAAG,CAAC;AACvC,cAAI,IAAI,IAAI,WAAW,OAAO,GAAG,aAAa,MAAM;eAEjD;AACH,gBAAM,IAAI,WAAW,YAAY;;AAGrC,aAAK,WAAW,KAAK,IAAI,MAAM;MACnC;MAEO,UAAU,MAAuB,OAAiC;AACrE,cAAM,eAAyB,CAAC,WAAA,SAAS,eAAe;AAExD,YAAI,OAAO,SAAU,UAAU;AAC3B,mBAAA,OAAO,OAAO,cAAc,IAAI;eAE7B;AACH,mBAAA,OAAO,OAAO,cAAc,IAAI;;AAGpC,YAAI;AACJ,cAAM,IAAI,WAAW,aAAa,UAAW,MAA0B,cAAe,MAAmB,OAAO;AAChH,YAAI,IAAI,IAAI,WAAW,YAAY,GAAG,CAAC;AACvC,YAAI,IAAI,IAAI,WAAW,KAAK,GAAG,aAAa,MAAM;AAElD,aAAK,WAAW,KAAK,IAAI,MAAM;MACnC;MAEA,IAAW,QAAK;AACZ,eAAO,KAAK,WAAW,SAAQ;MACnC;MAEO,qBAAkB;AACrB,aAAK,OAAO,MAAK;AACjB,aAAK,cAAc,MAAK;AACxB,aAAK,QAAQ,MAAK;AAClB,aAAK,QAAQ,MAAK;AAClB,aAAK,kBAAkB,SAAS,CAAA;MACpC;MAEU,kBAAkB,OAAmB;AAC3C,cAAM,QAAQ,MAAM,KAAK,IAAI,WAAW,MAAM,IAAI,CAAC;AACnD,cAAM,OAAO,MAAM,CAAC;AAEpB,YAAI,SAAS,WAAA,SAAS,WAAW;AAC7B,cAAI,SAAS;AAEb,gBAAM,qBAAoB,GAAA,WAAA,UAAS,OAAO,MAAM;AAChD,qBAAU,GAAA,WAAA,YAAW,iBAAiB;AAEtC,eAAK,gBAAe,GAAA,WAAA,UAAS,OAAO,MAAM;AAC1C,qBAAU,GAAA,WAAA,YAAW,KAAK,YAAY;AAGtC,cAAI,CAAC,KAAK,YAAY;AAClB,kBAAM,cAAa,GAAA,aAAA,eAAc,KAAK,YAAY;AAClD,iBAAK,aAAa,IAAI,WAAU;;AAGpC,cAAI,MAAM,SAAS,UAAU,KAAK,WAAW,WAAW;AACpD,iBAAK,WAAW,UAAU,OAAO,EAAE,OAAM,CAAE;;AAG/C,eAAK,oBAAoB,GAAG,KAAK,MAAM,IAAI,iBAAiB;AAE5D,eAAK,YAAY;AACjB,eAAK,OAAO,OAAM;AAGlB,eAAK,WAAW,KAAK,CAAC,WAAA,SAAS,SAAS,CAAC;mBAElC,SAAS,WAAA,SAAS,OAAO;AAChC,gBAAM,KAAsB,EAAE,QAAQ,EAAC;AAEvC,gBAAM2B,QAAO,SAAA,OAAO,OAAO,OAAO,EAAE;AACpC,gBAAM,UAAU,SAAA,OAAO,OAAO,OAAO,EAAE;AAEvC,eAAK,QAAQ,OAAOA,OAAM,OAAO;mBAE1B,SAAS,WAAA,SAAS,YAAY;AACrC,eAAK,MAAK;mBAEH,SAAS,WAAA,SAAS,kBAAkB;AAC3C,gBAAM,KAAK,EAAE,QAAQ,EAAC;AAEtB,gBAAM,UAAoB,KAAK,WAAW,SAAQ,EAAW,YAAY;AACzE,gBAAM,OAAO,QAAQ,IAAI,SAAA,OAAO,OAAO,OAAO,EAAE,CAAC;AAEjD,gBAAM,UAAkB,IAAK,KAAY;AACzC,kBAAQ,OAAO,OAAO,EAAE;AAExB,eAAK,gBAAgB,MAAM,OAAO;mBAE3B,SAAS,WAAA,SAAS,YAAY;AACrC,gBAAM,MAAK;AACX,eAAK,SAAS,KAAK;mBAEZ,SAAS,WAAA,SAAS,kBAAkB;AAC3C,gBAAM,MAAK;AACX,eAAK,MAAM,KAAK;mBAET,SAAS,WAAA,SAAS,WAAW;AACpC,gBAAM,KAAsB,EAAE,QAAQ,EAAC;AAEvC,gBAAM,OAAQ,SAAA,OAAO,YAAY,OAAO,EAAE,IACpC,SAAA,OAAO,OAAO,OAAO,EAAE,IACvB,SAAA,OAAO,OAAO,OAAO,EAAE;AAE7B,gBAAM,UAAW,MAAM,SAAS,GAAG,SAC7B,QAAQ,OAAO,MAAM,MAAM,GAAG,MAAM,IACpC;AAEN,eAAK,gBAAgB,MAAM,OAAO;mBAE3B,SAAS,WAAA,SAAS,iBAAiB;AAC1C,gBAAM,KAAsB,EAAE,QAAQ,EAAC;AAEvC,gBAAM,OAAQ,SAAA,OAAO,YAAY,OAAO,EAAE,IACpC,SAAA,OAAO,OAAO,OAAO,EAAE,IACvB,SAAA,OAAO,OAAO,OAAO,EAAE;AAE7B,eAAK,gBAAgB,MAAM,IAAI,WAAW,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC;;MAEzE;MAEU,SAAS,cAAsB;AACrC,aAAK,WAAW,SAAS,YAAY;AACrC,aAAK,cAAc,OAAO,KAAK,WAAW,SAAQ,CAAE;MACxD;MAEU,MAAM,aAAqB;AACjC,aAAK,WAAW,MAAM,WAAW;AACjC,aAAK,cAAc,OAAO,KAAK,WAAW,SAAQ,CAAE;MACxD;MAEQ,gBAAgB,MAAuC,SAAY;;AACvE,cAAM,cAAc,KAAK,qBAAqB,IAAI;AAElD,YAAI,KAAK,kBAAkB,OAAO,WAAW,GAAG;AAC5C,eAAK,kBAAkB,KAAK,aAAa,OAAO;mBAEzC,KAAK,kBAAkB,OAAO,GAAG,GAAG;AAC3C,eAAK,kBAAkB,KAAK,KAAK,MAAM,OAAO;eAE3C;AACH,WAAA,KAAA,QAAQ,UAAI,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,SAAG,qDAAqD,IAAI,IAAI;;MAEpF;MAEQ,UAAO;AACX,YAAI,KAAK,YAAY;AACjB,eAAK,WAAW,SAAQ;;MAEhC;MAEQ,qBAAqB,MAAqC;AAC9D,gBAAQ,OAAO,MAAO;UAElB,KAAK;AAAY,mBAAO,IAAK,KAAuB,OAAO;UAG3D,KAAK;AAAU,mBAAO;UAGtB,KAAK;AAAU,mBAAO,IAAI,IAAI;UAE9B;AAAS,kBAAM,IAAI,MAAM,uBAAuB;;MAExD;;AApSJ,YAAA,OAAA;;;;;ACvBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAAS,MAAM,KAAK,KAAK;AACxB,MAAI,UAAU,IAAI,WAAW,CAAC;AAC9B,MAAI,gBAAgB,IAAI;AACxB,MAAI,aAAa,IAAI;AACrB,MAAI,OAAO,IAAI;AAChB;AAEO,SAAS,KAAK,QAAQ,KAAK,MAAM;AACvC,SAAO,IAAI,QAAQ,SAAU,KAAK,KAAK;AACtC,WAAO,QAAQ,CAAC;AAChB,QAAI,MAAM,IAAI;AACd,QAAI,GAAG,KAAK,KAAK,MAAI,KAAK;AAC1B,QAAI,UAAU,KAAK,WAAW,CAAC;AAG/B,QAAI,KAAK;AAAS,UAAI,UAAU,KAAK;AACrC,QAAI,YAAY,IAAI,UAAU,SAAU,KAAK;AAC5C,UAAI,UAAU,IAAI,QAAQ;AAC1B,UAAI,GAAG;AAAA,IACR;AAEA,QAAI,KAAK,QAAQ,IAAI,QAAQ,GAAG;AAEhC,QAAI,SAAS,WAAY;AACxB,YAAM,IAAI,sBAAsB,EAAE,KAAK,EAAE,MAAM,SAAS;AACxD,YAAM,KAAK,GAAG;AAEd,aAAO,MAAM,IAAI,MAAM,GAAG;AACzB,cAAM,IAAI,MAAM,IAAI;AACpB,YAAI,QAAQ,IAAI,MAAM,EAAE,YAAY,CAAC,IAAI,IAAI,KAAK,IAAI;AAAA,MACvD;AAEA,YAAM,IAAI,QAAQ,cAAc;AAChC,UAAI,OAAO,CAAC,CAAC,CAAC,IAAI,QAAQ,kBAAkB,GAAG;AAC9C,YAAI;AACH,cAAI,OAAO,KAAK,MAAM,IAAI,MAAM,KAAK,OAAO;AAAA,QAC7C,SAAS,KAAK;AACb,gBAAM,KAAK,GAAG;AACd,iBAAO,IAAI,GAAG;AAAA,QACf;AAAA,MACD;AAEA,OAAC,IAAI,UAAU,MAAM,MAAM,KAAK,GAAG;AAAA,IACpC;AAEA,QAAI,OAAO,WAAW,OAAO,eAAe,UAAU;AAAA,IAEtD,WAAW,OAAO,OAAO,OAAO,UAAU;AACzC,cAAQ,cAAc,IAAI;AAC1B,YAAM,KAAK,UAAU,GAAG;AAAA,IACzB;AAEA,QAAI,kBAAkB,CAAC,CAAC,KAAK;AAE7B,SAAK,KAAK,SAAS;AAClB,UAAI,iBAAiB,GAAG,QAAQ,CAAC,CAAC;AAAA,IACnC;AAEA,QAAI,KAAK,GAAG;AAAA,EACb,CAAC;AACF;AA5DA,IA8DW,KACA,MACA,OACA,KACA;AAlEX;AAAA;AA8DO,IAAI,MAAoB,KAAK,KAAK,MAAM,KAAK;AAC7C,IAAI,OAAqB,KAAK,KAAK,MAAM,MAAM;AAC/C,IAAI,QAAsB,KAAK,KAAK,MAAM,OAAO;AACjD,IAAI,MAAoB,KAAK,KAAK,MAAM,QAAQ;AAChD,IAAI,MAAoB,KAAK,KAAK,MAAM,KAAK;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjEpD,QAAA,gBAAA;AACA,QAAA,SAAA,aAAA,uCAAA;AAEA,QAAa,OAAb,MAAiB;MAGb,YACc,QACH,UAAoC,CAAA,GAAE;AADnC,aAAA,SAAA;AACH,aAAA,UAAA;MACR;MAEI,IAAa,MAAc,UAAmC,CAAA,GAAE;AACnE,eAAO,KAAK,QAAQ,OAAO,MAAM,OAAO;MAC5C;MAEO,KAAc,MAAc,UAAmC,CAAA,GAAE;AACpE,eAAO,KAAK,QAAQ,QAAQ,MAAM,OAAO;MAC7C;MAEO,IAAa,MAAc,UAAmC,CAAA,GAAE;AACnE,eAAO,KAAK,QAAQ,OAAO,MAAM,OAAO;MAC5C;MAEO,IAAa,MAAc,UAAmC,CAAA,GAAE;AACnE,eAAO,KAAK,QAAQ,OAAO,MAAM,OAAO;MAC5C;MAEU,QAAQ,QAAwC,MAAc,UAAmC,CAAA,GAAE;AACzG,eAAO,OAAO,MAAM,EAAE,KAAK,OAAO,iBAAiB,EAAE,IAAI,GAAG,KAAK,WAAW,OAAO,CAAC,EAAE,MAAM,CAAC,MAAU;;AACnG,gBAAM,SAAS,EAAE;AACjB,gBAAM,YAAU,KAAA,EAAE,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE,UAAS,EAAE,iBAAiB,EAAE;AAEtD,cAAI,CAAC,UAAU,CAAC,SAAS;AACrB,kBAAM;;AAGV,gBAAM,IAAI,cAAA,YAAY,QAAQ,OAAO;QACzC,CAAC;MACL;MAEU,WAAW,SAAgC;AAEjD,gBAAQ,UAAU,OAAO,OAAO,CAAA,GAAI,KAAK,SAAS,QAAQ,OAAO;AAEjE,YAAI,KAAK,WAAW;AAChB,kBAAQ,QAAQ,eAAe,IAAI,UAAU,KAAK,SAAS;;AAG/D,YAAI,OAAQ,OAAQ,eAAe,GAAG,OAAO,GAAG,IAAI,UAAU;eAKvD;AAEH,kBAAQ,kBAAkB;;AAG9B,eAAO;MACX;;AAxDJ,YAAA,OAAA;;;;;;;;;;ACIA,QAAI;AAEJ,aAAS,aAAU;AACf,UAAI,CAAC,SAAU;AACX,YAAI;AACA,oBAAW,OAAQ,OAAQ,eAAe,GAAG,OAAO,GAAG,IAAI,eACrD,GAAG,IAAI,eACP,OAAO;iBAER,GAAG;;;AAKhB,UAAI,CAAC,SAAS;AAEV,kBAAU;UACN,OAAO,CAAA;UACP,SAAS,SAAU,KAAK,OAAK;AAAI,iBAAK,MAAM,GAAG,IAAI;UAAO;UAC1D,SAAS,SAAU,KAAG;AAAI,iBAAK,MAAM,GAAG;UAAG;UAC3C,YAAY,SAAU,KAAG;AAAI,mBAAO,KAAK,MAAM,GAAG;UAAG;;;AAI7D,aAAO;IACX;AAEA,aAAgB,QAAQ,KAAa,OAAa;AAC9C,iBAAU,EAAG,QAAQ,KAAK,KAAK;IACnC;AAFA,YAAA,UAAA;AAIA,aAAgB,WAAW,KAAW;AAClC,iBAAU,EAAG,WAAW,GAAG;IAC/B;AAFA,YAAA,aAAA;AAIA,aAAgB,QAAQ,KAAa,UAAkB;AACnD,YAAM,QAAa,WAAU,EAAG,QAAQ,GAAG;AAE3C,UACI,OAAQ,YAAa;MACrB,EAAE,iBAAiB,UACrB;AAEE,iBAAS,KAAK;aAEX;AAEH,cAAM,KAAK,CAAC,OAAO,SAAS,EAAE,CAAC;;IAEvC;AAdA,YAAA,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1CA,QAAA,YAAA;AACA,QAAA,eAAA;AAkBA,QAAa,OAAb,MAAiB;MAWb,YAAsB,MAAU;AAAV,aAAA,OAAA;AAVtB,aAAA,WAAyB;UACrB,MAAM;UACN,KAAK;;AAGT,2BAAA,IAAA,MAAgB,KAAK;AACrB,qCAAA,IAAA,MAAA,MAAA;AACA,4BAAA,IAAA,MAAiB,MAAS;AAC1B,sBAAA,IAAA,OAAW,GAAA,aAAA,kBAAgB,CAAE;AAGzB,SAAA,GAAA,UAAA,SAAQ,KAAK,SAAS,KAAK,CAAC,UAAU,KAAK,QAAQ,KAAK;MAC5D;MAEA,IAAW,MAAM,OAAa;AAC1B,aAAK,KAAK,YAAY;MAC1B;MAEA,IAAW,QAAK;AACZ,eAAO,KAAK,KAAK;MACrB;MAEO,SAAS,UAAsC;AAClD,cAAM,eAAe,uBAAA,MAAI,eAAA,GAAA,EAAU,GAAG,UAAU,QAAQ;AACxD,YAAI,CAAC,uBAAA,MAAI,oBAAA,GAAA,GAAgB;AACrB,iCAAA,MAAI,8BAA2B,IAAI,QAAc,CAAC,SAAS,WAAU;AACjE,iBAAK,YAAW,EAAG,KAAK,CAAC,aAAY;AACjC,mBAAK,WAAU,OAAA,OAAA,OAAA,OAAA,CAAA,GAAM,QAAQ,GAAA,EAAE,OAAO,KAAK,MAAK,CAAA,CAAA;YAEpD,CAAC,EAAE,MAAM,CAAC,MAAK;AAEX,mBAAK,WAAW,EAAE,MAAM,MAAM,OAAO,OAAS,CAAE;YAEpD,CAAC,EAAE,QAAQ,MAAK;AACZ,sBAAO;YACX,CAAC;UACL,CAAC,GAAC,GAAA;;AAEN,+BAAA,MAAI,oBAAiB,MAAI,GAAA;AACzB,eAAO;MACX;MAEa,cAAW;;AACpB,cAAI,KAAK,OAAO;AACZ,oBAAQ,MAAM,KAAK,KAAK,IAAI,GAAG,KAAK,SAAS,IAAI,WAAW,GAAG;iBAC5D;AACH,kBAAM,IAAI,MAAM,oBAAoB;;QAE5C,CAAC;;MAEY,6BAA6B,OAAe,UAAkB,SAAa;;AACpF,gBAAM,QAAQ,MAAM,KAAK,KAAK,KAAK,GAAG,KAAK,SAAS,IAAI,aAAa;YACjE,MAAM,EAAE,OAAO,UAAU,QAAO;WACnC,GAAG;AAEJ,eAAK,WAAW,IAAI;AAEpB,iBAAO;QACX,CAAC;;MAEY,2BAA2B,OAAe,UAAgB;;AACnE,gBAAM,QAAQ,MAAM,KAAK,KAAK,KAAK,GAAG,KAAK,SAAS,IAAI,UAAU;YAC9D,MAAM,EAAE,OAAO,SAAQ;WAC1B,GAAG;AAEJ,eAAK,WAAW,IAAI;AAEpB,iBAAO;QACX,CAAC;;MAEY,kBAAkB,SAAa;;AACxC,gBAAM,QAAQ,MAAM,KAAK,KAAK,KAAK,GAAG,KAAK,SAAS,IAAI,cAAc;YAClE,MAAM,EAAE,QAAO;WAClB,GAAG;AAEJ,eAAK,WAAW,IAAI;AAEpB,iBAAO;QACX,CAAC;;MAEY,uBAAuB,OAAa;;AAC7C,kBAAQ,MAAM,KAAK,KAAK,KAAK,GAAG,KAAK,SAAS,IAAI,oBAAoB;YAClE,MAAM,EAAE,MAAK;WAChB,GAAG;QACR,CAAC;;MAEY,mBAAmB,cAAsB,WAAmC,CAAA,GAAE;;AACvF,iBAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACnC,kBAAM,IAAI,SAAS,SAAS;AAC5B,kBAAM,IAAI,SAAS,UAAU;AAG7B,kBAAM,iBAAiB,KAAK,QAAQ,UAAU,KAAK,KAAK,KAAK;AAG7D,kBAAM,QAAQ,cAAe,aAAa,CAAC,EAAE,YAAW,IAAK,aAAa,UAAU,CAAC,CAAE;AACvF,kBAAM,MAAM,KAAK,KAAK,QAAQ,EAAE,iBAAiB,EAAE,GAAI,SAAS,UAAU,GAAG,KAAK,SAAS,IAAI,WAAY,IAAI,YAAY,GAAG,cAAc,EAAE;AAE9I,kBAAM,OAAQ,OAAO,QAAQ,IAAM,IAAI;AACvC,kBAAM,MAAO,OAAO,SAAS,IAAM,IAAI;AAEvC,mCAAA,MAAI,qBAAkB,OAAO,KAAK,KAAK,OAAO,wHAAwH,IAAI,cAAc,IAAI,WAAW,MAAM,YAAY,IAAI,GAAC,GAAA;AAE9N,kBAAM,YAAY,CAAC,UAAuB;AAKtC,kBAAI,MAAM,KAAK,SAAS,UAAa,MAAM,KAAK,UAAU,QAAW;AAAE;;AAEvE,4BAAc,gBAAgB;AAC9B,qCAAA,MAAI,qBAAA,GAAA,EAAgB,MAAK;AACzB,qCAAA,MAAI,qBAAkB,QAAS,GAAA;AAE/B,qBAAO,oBAAoB,WAAW,SAAS;AAE/C,kBAAI,MAAM,KAAK,UAAU,QAAW;AAChC,uBAAO,MAAM,KAAK,KAAK;qBAEpB;AACH,wBAAQ,MAAM,IAAI;AAClB,qBAAK,WAAW,MAAM,IAAI;;YAElC;AAEA,kBAAM,mBAAmB,YAAY,MAAK;AACtC,kBAAI,CAAC,uBAAA,MAAI,qBAAA,GAAA,KAAmB,uBAAA,MAAI,qBAAA,GAAA,EAAgB,QAAQ;AACpD,uCAAA,MAAI,qBAAkB,QAAS,GAAA;AAC/B,uBAAO,WAAW;AAClB,uBAAO,oBAAoB,WAAW,SAAS;;YAEvD,GAAG,GAAG;AAEN,mBAAO,iBAAiB,WAAW,SAAS;UAChD,CAAC;QACL,CAAC;;MAEY,UAAO;;AAChB,eAAK,WAAW,EAAE,MAAM,MAAM,OAAO,KAAI,CAAE;QAC/C,CAAC;;MAEO,WAAW,UAA2B;AAC1C,YAAI,SAAS,UAAU,QAAW;AAC9B,eAAK,QAAQ,SAAS;AAEtB,cAAI,SAAS,UAAU,MAAM;AACzB,aAAA,GAAA,UAAA,YAAW,KAAK,SAAS,GAAG;iBAEzB;AAEH,aAAA,GAAA,UAAA,SAAQ,KAAK,SAAS,KAAK,SAAS,KAAK;;;AAIjD,+BAAA,MAAI,eAAA,GAAA,EAAU,KAAK,UAAU,QAAQ;MACzC;;AA5JJ,YAAA,OAAA;;;;;;;;;;;ACYA,aAAgB,kBAAmB,KAAQ;;AACvC,YAAM,kBAAgB,KAAA,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAE,aAAY;AAEpD,YAAM,yBAAyB,IAAI,SAAS,MAAM,GAAG;AACrD,YAAM,YACF,CAAC,IAAI,SAAS,SAAS,mBAAmB;MAC1C,CAAC,IAAI,SAAS,SAAS,iBAAiB;MACxC,uBAAuB,SAAS,IAE9B,IAAI,uBAAuB,CAAC,CAAC,KAC7B;AAEN,aAAQ,IAAI,SAAS,WAAW,SAAS,IACnC,GAAG,IAAI,QAAQ,KAAK,aAAa,GAAG,SAAS,GAAG,IAAI,QAAQ,GAAG,IAAI,MAAM,KACzE,GAAG,IAAI,QAAQ,KAAK,aAAa,mBAAmB,SAAS,GAAG,IAAI,QAAQ,GAAG,IAAI,MAAM;IACnG;AAfA,YAAA,oBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChCA,QAAA,gBAAA;AACA,QAAA,SAAA;AAEA,QAAA,SAAA;AACA,QAAA,SAAA;AACA,QAAA,YAAA;AAIA,QAAa,iBAAb,MAAa,wBAAuB,MAAK;MAErC,YAAY,SAAiB,MAAY;AACrC,cAAM,OAAO;AACb,aAAK,OAAO;AACZ,eAAO,eAAe,MAAM,gBAAe,SAAS;MACxD;;AANJ,YAAA,iBAAA;AAWA,QAAM,mBAAoB,OAAQ,WAAY,eAAgB,SAAQ,KAAA,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAE,cAAc,cAChG,GAAG,OAAO,SAAS,SAAS,QAAQ,QAAQ,IAAI,CAAC,KAAK,OAAO,SAAS,QAAQ,GAAI,OAAO,SAAS,QAAQ,IAAI,OAAO,SAAS,IAAI,EAAG,KACrI;AAcN,QAAa,SAAb,MAAmB;MAOf,YACI,WAAsC,kBACtC,SAAuB;;AAEvB,YAAI,OAAQ,aAAc,UAAU;AAKhC,gBAAM,MAAO,SAAS,WAAW,GAAG,IAC9B,IAAI,IAAI,UAAU,gBAAgB,IAClC,IAAI,IAAI,QAAQ;AAEtB,gBAAM,SAAU,IAAI,aAAa,YAAY,IAAI,aAAa;AAC9D,gBAAM,OAAO,OAAO,IAAI,SAAS,SAAS,MAAM,GAAG;AAEnD,eAAK,WAAW;YACZ,UAAU,IAAI;YACd,UAAU,IAAI;YACd;YACA;;eAGD;AAIH,cAAI,SAAS,SAAS,QAAW;AAC7B,qBAAS,OAAQ,SAAS,SAAU,MAAM;;AAE9C,cAAI,SAAS,aAAa,QAAW;AACjC,qBAAS,WAAW;;AAExB,eAAK,WAAW;;AAIpB,YAAI,KAAK,SAAS,SAAS,SAAS,GAAG,GAAG;AACtC,eAAK,SAAS,WAAW,KAAK,SAAS,SAAS,MAAM,GAAG,EAAE;;AAG/D,aAAK,OAAO,IAAI,OAAA,KAAK,OAAM,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,YAAW,CAAA,CAAE;AACjD,aAAK,OAAO,IAAI,OAAA,KAAK,KAAK,IAAI;AAE9B,aAAK,aAAa,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS;AAK3B,YACI,CAAC,KAAK,cACN,OAAQ,WAAY,iBACpB,MAAAC,MAAA,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,cAAQ,QAAAA,QAAA,SAAA,SAAAA,IAAE,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAE,SAAS,iBAAiB,IACxD;AACE,eAAK,aAAa,UAAA;AAClB,kBAAQ,IAAI,wEAAwE;;MAE5F;MAEa,aAAgB,UAAkB,UAAuB,CAAA,GAAI,YAAiC;;AACvG,iBAAO,MAAM,KAAK,uBAA0B,gBAAgB,UAAU,SAAS,UAAU;QAC7F,CAAC;;MAEY,OAAU,UAAkB,UAAuB,CAAA,GAAI,YAAiC;;AACjG,iBAAO,MAAM,KAAK,uBAA0B,UAAU,UAAU,SAAS,UAAU;QACvF,CAAC;;MAEY,KAAQ,UAAkB,UAAuB,CAAA,GAAI,YAAiC;;AAC/F,iBAAO,MAAM,KAAK,uBAA0B,QAAQ,UAAU,SAAS,UAAU;QACrF,CAAC;;MAEY,SAAY,QAAgB,UAAuB,CAAA,GAAI,YAAiC;;AACjG,iBAAO,MAAM,KAAK,uBAA0B,YAAY,QAAQ,SAAS,UAAU;QACvF,CAAC;;;;;;;;;MASY,UAAa,mBAA2B,YAAiC;;AAClF,cAAI,OAAQ,sBAAuB,YAAY,OAAQ,eAAgB,UAAU;AAC7E,kBAAM,IAAI,MAAM,uJAAuJ;;AAE3K,gBAAM,CAAC,QAAQ,KAAK,IAAI,kBAAkB,MAAM,GAAG;AACzD,cAAI,CAAC,UAAU,CAAC,OAAO;AACtB,kBAAM,IAAI,MAAM,mFAAmF;;AAE9F,iBAAO,MAAM,KAAK,uBAA0B,aAAa,QAAQ,EAAE,mBAAmB,MAAK,GAAI,UAAU;QAC7G,CAAC;;MAEY,kBAAkC,WAAmB,IAAE;;AAChE,kBACI,MAAM,KAAK,KAAK,IAAI,aAAa,QAAQ,IAAI;YACzC,SAAS;cACL,UAAU;;WAEjB,GACH;QACN,CAAC;;MAEY,uBACT,UACA,YACA;;AAEA,gBAAM,OAAO,KAAK,WAAc,SAAS,KAAK,MAAM,UAAU;AAC9D,eAAK,SAAS,SAAS,KAAK;AAC5B,eAAK,YAAY,SAAS;AAE1B,gBAAM,UAAe,EAAE,WAAW,KAAK,UAAS;AAGhD,cAAI,SAAS,mBAAmB;AAC5B,oBAAQ,oBAAoB,SAAS;;AAGzC,gBAAM,aAAa,qBAAqB;AACxC,eAAK,QAAQ,KAAK,cAAc,SAAS,MAAM,OAAO,GAAG,SAAS,YAAY,MAAW,UAAA,MAAA,QAAA,QAAA,aAAA;AACrF,oBAAQ,KAAK,uBAAuB,OAAO,cAAc,MAAO,CAAC,6CAA6C,KAAK,MAAM,MAAM;AAE/H,gBAAI,aAAa;AACjB,gBAAI,kBAAkB;AAEtB,kBAAM,oBAAoB,MAAW,UAAA,MAAA,QAAA,QAAA,aAAA;AACjC;AAEA,kBAAI;AACA,sBAAM,KAAK,uBAAuB,UAAU,YAAY,UAAU;AAClE,wBAAQ,KAAK,uBAAuB,OAAO,cAAc,IAAM,CAAC,sDAAsD,KAAK,MAAM,GAAG;uBAE/H,GAAG;AACR,oBAAI,aAAa,iBAAiB;AAC9B,0BAAQ,KAAK,uBAAuB,OAAO,cAAc,MAAO,CAAC,iBAAiB,UAAU,WAAW,eAAe,GAAG;AACzH,6BAAW,mBAAmB,GAAI;uBAE/B;AACH,0BAAQ,KAAK,uBAAuB,OAAO,cAAc,KAAM,CAAC,yEAAyE;;;YAGrJ,CAAC;AAED,uBAAW,mBAAmB,GAAI;UACtC,CAAC,IAAG,YAAY,KAAK,KAAK,OAAO;AAEjC,iBAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACnC,kBAAM,UAAU,CAAC,MAAM,YAAY,OAAO,IAAI,cAAA,YAAY,MAAM,OAAO,CAAC;AACxE,uBAAW,QAAQ,KAAK,OAAO;AAE/B,uBAAW,QAAQ,EAAE,KAAK,MAAK;AAC3B,yBAAW,QAAQ,OAAO,OAAO;AACjC,sBAAQ,UAAU;YACtB,CAAC;UACL,CAAC;QACL,CAAC;;MAEe,uBACZ,QACA,UACA,UAAuB,CAAA,GACvB,YACA,mBAAwB;;AAExB,gBAAM,YACF,MAAM,KAAK,KAAK,KAAK,aAAa,MAAM,IAAI,QAAQ,IAAI;YACpD,SAAS;cACL,UAAU;cACV,gBAAgB;;YAEpB,MAAM,KAAK,UAAU,OAAO;WAC/B,GACH;AAGF,cAAI,SAAS,OAAO;AAChB,kBAAM,IAAI,eAAe,SAAS,OAAO,SAAS,IAAI;;AAI1D,cAAI,WAAW,aAAa;AACxB,qBAAS,oBAAoB,QAAQ;;AAGzC,iBAAO,MAAM,KAAK,uBAA0B,UAAU,YAAY,iBAAiB;QACvF,CAAC;;MAES,WAAc,UAAkB,YAAiC;AACvE,eAAO,IAAI,OAAA,KAAQ,UAAU,UAAU;MAC3C;MAEU,cAAc,MAAW,UAAe,CAAA,GAAE;AAChD,cAAM,SAAS,CAAA;AAGf,mBAAW,QAAQ,SAAS;AACxB,cAAI,CAAC,QAAQ,eAAe,IAAI,GAAG;AAC/B;;AAEJ,iBAAO,KAAK,GAAG,IAAI,IAAI,QAAQ,IAAI,CAAC,EAAE;;AAG1C,YAAI,WAAY,KAAK,SAAS,SACxB,WACA;AAEN,YAAI,KAAK,eAAe;AACpB,sBAAY,GAAG,KAAK,aAAa;eAE9B;AACH,sBAAY,GAAG,KAAK,SAAS,QAAQ,GAAG,KAAK,gBAAe,CAAE,GAAG,KAAK,SAAS,QAAQ;;AAG3F,cAAM,cAAc,GAAG,QAAQ,IAAI,KAAK,SAAS,IAAI,KAAK,MAAM,IAAI,OAAO,KAAK,GAAG,CAAC;AACpF,eAAQ,KAAK,aACP,KAAK,WAAW,IAAI,IAAI,WAAW,CAAC,IACpC;MACV;MAEU,gBAAgB,WAAmB,IAAE;AAC3C,cAAM,OAAO,SAAS,WAAW,GAAG,IAAI,WAAW,IAAI,QAAQ;AAC/D,cAAM,cAAc,GAAI,KAAK,SAAS,SAAU,UAAU,MAAM,MAAM,KAAK,SAAS,QAAQ,GAAG,KAAK,gBAAe,CAAE,GAAG,KAAK,SAAS,QAAQ,GAAG,IAAI;AACrJ,eAAQ,KAAK,aACP,KAAK,WAAW,IAAI,IAAI,WAAW,CAAC,IACpC;MACV;MAEU,kBAAe;AACrB,eAAQ,KAAK,SAAS,SAAS,MAAM,KAAK,SAAS,SAAS,MACtD,IAAI,KAAK,SAAS,IAAI,KACtB;MACV;;AA/OJ,YAAA,SAAA;;;;;;;;;;ACnCA,QAAA,WAAA;AAIA,QAAa,mBAAb,MAA6B;MAGzB,SAAS,UAAa;AAClB,eAAO,KAAK,MAAM,OAAO,QAAQ;MACrC;MAEA,WAAQ;AACJ,eAAO,KAAK;MAChB;MAEA,MAAM,SAAO;AACT,eAAO,KAAK,MAAM,OAAO,OAAO;MACpC;MAEA,WAAQ;;AACJ,SAAA,MAAA,KAAA,KAAK,WAAK,QAAA,OAAA,SAAA,SAAA,GAAG,UAAU,OAAC,QAAA,OAAA,SAAA,SAAA,GAAE,KAAK,UAAS;MAC5C;MAEA,UAAU,OAAiB,IAAa;AACpC,YAAI,KAAK,OAAO;AAEZ,gBAAM,aAAa,IAAI,SAAA,WAAU;AACjC,qBAAW,OAAO,OAAO,EAAE;eAExB;AAEH,eAAK,QAAQ,SAAA,WAAW,OAAO,OAAO,EAAE;;MAEhD;;AA7BJ,YAAA,mBAAA;;;;;;;;;;ACHA,QAAa,iBAAb,MAA2B;MACvB,SAAS,UAAa;MAAS;MAC/B,WAAQ;AAAK,eAAO;MAAM;MAC1B,MAAM,SAAO;MAAG;MAChB,WAAQ;MAAK;MACb,UAAU,OAAe;MAAG;;AALhC,YAAA,iBAAA;;;;;;;;;ACFA;AAEA,QAAA,WAAA;AAAS,WAAA,eAAA,SAAA,UAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,SAAA;IAAM,EAAA,CAAA;AAAe,WAAA,eAAA,SAAA,kBAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,SAAA;IAAc,EAAA,CAAA;AAC5C,QAAA,aAAA;AAAS,WAAA,eAAA,SAAA,YAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,WAAA;IAAQ,EAAA,CAAA;AAAE,WAAA,eAAA,SAAA,aAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,WAAA;IAAS,EAAA,CAAA;AAC5B,QAAA,SAAA;AAAS,WAAA,eAAA,SAAA,QAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,OAAA;IAAI,EAAA,CAAA;AACb,QAAA,SAAA;AAAS,WAAA,eAAA,SAAA,QAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,OAAA;IAAI,EAAA,CAAA;AAMb,QAAA,qBAAA;AAI6B,WAAA,eAAA,SAAA,oBAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAJpB,mBAAA;IAAgB,EAAA,CAAA;AACzB,QAAA,mBAAA;AACA,QAAA,eAAA;AAES,WAAA,eAAA,SAAA,sBAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAFA,aAAA;IAAkB,EAAA,CAAA;AAG3B,KAAA,GAAA,aAAA,oBAAmB,UAAU,mBAAA,gBAAgB;AAC7C,KAAA,GAAA,aAAA,oBAAmB,QAAQ,iBAAA,cAAc;;;", "names": ["CloseCode", "Protocol", "ErrorCode", "_a", "exports", "d", "b", "ChangeTree", "value", "ArraySchema", "type", "MapSchema", "k", "SchemaDefinition", "Context", "string", "CollectionSchema", "SetSchema", "ClientState", "ReferenceTracker", "EncodeSchemaError", "<PERSON><PERSON><PERSON>", "i", "filter", "deprecated", "_a", "ReflectionField", "ReflectionType", "Reflection", "_super", "fieldType", "code", "_a"]}