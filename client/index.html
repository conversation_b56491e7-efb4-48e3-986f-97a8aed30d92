<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>多人在线游戏</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #1a1a1a;
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }
        
        #game-container {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100vw;
            height: 100vh;
        }
        
        canvas {
            border: 2px solid #333;
            border-radius: 8px;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 18px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div id="game-container">
        <div class="loading" id="loading">加载中...</div>
    </div>
    <script type="module" src="/src/simple-main.ts"></script>
</body>
</html>
