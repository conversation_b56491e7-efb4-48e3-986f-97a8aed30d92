import Phaser from 'phaser';
import { Player, HexTile, Card, GameState } from '@multiplayer-game/shared';

/**
 * UI管理器
 * 统一管理游戏中的所有UI组件
 */

export interface UIPanel {
  container: Phaser.GameObjects.Container;
  isVisible: boolean;
  show(): void;
  hide(): void;
  update(data?: any): void;
}

export class UIManager {
  private scene: Phaser.Scene;
  private panels: Map<string, UIPanel> = new Map();
  private notifications: Phaser.GameObjects.Container[] = [];

  constructor(scene: Phaser.Scene) {
    this.scene = scene;
    this.initializeUI();
  }

  /**
   * 初始化UI系统
   */
  private initializeUI(): void {
    // 创建各种UI面板
    this.createTopBar();
    this.createResourcePanel();
    this.createMiniMap();
    this.createActionPanel();
    this.createCardPanel();
    this.createBuildingPanel();
    this.createDiplomacyPanel();
  }

  /**
   * 创建顶部信息栏
   */
  private createTopBar(): void {
    const topBar = new TopBarPanel(this.scene);
    this.panels.set('topBar', topBar);
  }

  /**
   * 创建资源面板
   */
  private createResourcePanel(): void {
    const resourcePanel = new ResourcePanel(this.scene);
    this.panels.set('resourcePanel', resourcePanel);
  }

  /**
   * 创建小地图
   */
  private createMiniMap(): void {
    const miniMap = new MiniMapPanel(this.scene);
    this.panels.set('miniMap', miniMap);
  }

  /**
   * 创建行动面板
   */
  private createActionPanel(): void {
    const actionPanel = new ActionPanel(this.scene);
    this.panels.set('actionPanel', actionPanel);
  }

  /**
   * 创建卡牌面板
   */
  private createCardPanel(): void {
    const cardPanel = new CardPanel(this.scene);
    this.panels.set('cardPanel', cardPanel);
  }

  /**
   * 创建建筑面板
   */
  private createBuildingPanel(): void {
    const buildingPanel = new BuildingPanel(this.scene);
    this.panels.set('buildingPanel', buildingPanel);
  }

  /**
   * 创建外交面板
   */
  private createDiplomacyPanel(): void {
    const diplomacyPanel = new DiplomacyPanel(this.scene);
    this.panels.set('diplomacyPanel', diplomacyPanel);
  }

  /**
   * 显示面板
   */
  public showPanel(panelName: string, data?: any): void {
    const panel = this.panels.get(panelName);
    if (panel) {
      panel.show();
      panel.update(data);
    }
  }

  /**
   * 隐藏面板
   */
  public hidePanel(panelName: string): void {
    const panel = this.panels.get(panelName);
    if (panel) {
      panel.hide();
    }
  }

  /**
   * 更新所有UI
   */
  public updateAll(gameState: any): void {
    for (const panel of this.panels.values()) {
      if (panel.isVisible) {
        panel.update(gameState);
      }
    }
  }

  /**
   * 显示通知
   */
  public showNotification(message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info'): void {
    const notification = this.createNotification(message, type);
    this.notifications.push(notification);

    // 3秒后自动消失
    this.scene.time.delayedCall(3000, () => {
      this.removeNotification(notification);
    });
  }

  /**
   * 创建通知
   */
  private createNotification(message: string, type: string): Phaser.GameObjects.Container {
    const colors = {
      info: 0x3498db,
      success: 0x27ae60,
      warning: 0xf39c12,
      error: 0xe74c3c
    };

    const container = this.scene.add.container(this.scene.scale.width - 20, 100 + this.notifications.length * 60);
    
    const background = this.scene.add.rectangle(0, 0, 300, 50, colors[type], 0.9);
    background.setStrokeStyle(2, colors[type]);
    
    const text = this.scene.add.text(0, 0, message, {
      fontSize: '14px',
      color: '#ffffff',
      wordWrap: { width: 280 }
    }).setOrigin(0.5);

    container.add([background, text]);
    container.setOrigin(1, 0);

    // 添加淡入动画
    container.setAlpha(0);
    this.scene.tweens.add({
      targets: container,
      alpha: 1,
      duration: 300,
      ease: 'Power2'
    });

    return container;
  }

  /**
   * 移除通知
   */
  private removeNotification(notification: Phaser.GameObjects.Container): void {
    const index = this.notifications.indexOf(notification);
    if (index !== -1) {
      this.notifications.splice(index, 1);
      
      // 淡出动画
      this.scene.tweens.add({
        targets: notification,
        alpha: 0,
        duration: 300,
        ease: 'Power2',
        onComplete: () => {
          notification.destroy();
          this.repositionNotifications();
        }
      });
    }
  }

  /**
   * 重新定位通知
   */
  private repositionNotifications(): void {
    this.notifications.forEach((notification, index) => {
      this.scene.tweens.add({
        targets: notification,
        y: 100 + index * 60,
        duration: 200,
        ease: 'Power2'
      });
    });
  }
}

/**
 * 顶部信息栏
 */
class TopBarPanel implements UIPanel {
  public container: Phaser.GameObjects.Container;
  public isVisible: boolean = true;
  private scene: Phaser.Scene;
  private turnText: Phaser.GameObjects.Text;
  private phaseText: Phaser.GameObjects.Text;
  private weatherText: Phaser.GameObjects.Text;

  constructor(scene: Phaser.Scene) {
    this.scene = scene;
    this.container = scene.add.container(0, 0);
    this.createElements();
  }

  private createElements(): void {
    // 背景
    const background = this.scene.add.rectangle(
      this.scene.scale.width / 2, 30, 
      this.scene.scale.width, 60, 
      0x2c3e50, 0.9
    );

    // 回合信息
    this.turnText = this.scene.add.text(20, 20, '回合: 1', {
      fontSize: '16px',
      color: '#ffffff',
      fontStyle: 'bold'
    });

    // 阶段信息
    this.phaseText = this.scene.add.text(150, 20, '阶段: 资源收集', {
      fontSize: '16px',
      color: '#f39c12'
    });

    // 天气信息
    this.weatherText = this.scene.add.text(this.scene.scale.width - 20, 20, '天气: 晴天', {
      fontSize: '16px',
      color: '#3498db'
    }).setOrigin(1, 0);

    this.container.add([background, this.turnText, this.phaseText, this.weatherText]);
  }

  public show(): void {
    this.isVisible = true;
    this.container.setVisible(true);
  }

  public hide(): void {
    this.isVisible = false;
    this.container.setVisible(false);
  }

  public update(data: any): void {
    if (data) {
      this.turnText.setText(`回合: ${data.currentTurn || 1}`);
      this.phaseText.setText(`阶段: ${data.strategicPhase || '未知'}`);
      this.weatherText.setText(`天气: ${data.weather?.current || '晴天'}`);
    }
  }
}

/**
 * 资源面板
 */
class ResourcePanel implements UIPanel {
  public container: Phaser.GameObjects.Container;
  public isVisible: boolean = true;
  private scene: Phaser.Scene;
  private goldText: Phaser.GameObjects.Text;
  private populationText: Phaser.GameObjects.Text;
  private experienceText: Phaser.GameObjects.Text;

  constructor(scene: Phaser.Scene) {
    this.scene = scene;
    this.container = scene.add.container(20, 80);
    this.createElements();
  }

  private createElements(): void {
    // 背景
    const background = this.scene.add.rectangle(0, 0, 200, 100, 0x34495e, 0.9);
    background.setStrokeStyle(2, 0x2c3e50);

    // 资源图标和文字
    this.goldText = this.scene.add.text(-80, -30, '💰 金币: 50', {
      fontSize: '14px',
      color: '#f1c40f'
    });

    this.populationText = this.scene.add.text(-80, -10, '👥 人口: 100', {
      fontSize: '14px',
      color: '#3498db'
    });

    this.experienceText = this.scene.add.text(-80, 10, '⭐ 经验: 0', {
      fontSize: '14px',
      color: '#9b59b6'
    });

    this.container.add([background, this.goldText, this.populationText, this.experienceText]);
  }

  public show(): void {
    this.isVisible = true;
    this.container.setVisible(true);
  }

  public hide(): void {
    this.isVisible = false;
    this.container.setVisible(false);
  }

  public update(data: any): void {
    if (data?.currentPlayer?.resources) {
      const resources = data.currentPlayer.resources;
      this.goldText.setText(`💰 金币: ${resources.gold}`);
      this.populationText.setText(`👥 人口: ${resources.population}`);
      this.experienceText.setText(`⭐ 经验: ${resources.experience}`);
    }
  }
}

/**
 * 小地图面板
 */
class MiniMapPanel implements UIPanel {
  public container: Phaser.GameObjects.Container;
  public isVisible: boolean = true;
  private scene: Phaser.Scene;

  constructor(scene: Phaser.Scene) {
    this.scene = scene;
    this.container = scene.add.container(scene.scale.width - 120, 80);
    this.createElements();
  }

  private createElements(): void {
    // 背景
    const background = this.scene.add.rectangle(0, 0, 200, 150, 0x2c3e50, 0.9);
    background.setStrokeStyle(2, 0x34495e);

    // 标题
    const title = this.scene.add.text(0, -60, '小地图', {
      fontSize: '14px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    this.container.add([background, title]);
  }

  public show(): void {
    this.isVisible = true;
    this.container.setVisible(true);
  }

  public hide(): void {
    this.isVisible = false;
    this.container.setVisible(false);
  }

  public update(data: any): void {
    // 更新小地图显示
  }
}

/**
 * 行动面板
 */
class ActionPanel implements UIPanel {
  public container: Phaser.GameObjects.Container;
  public isVisible: boolean = true;
  private scene: Phaser.Scene;

  constructor(scene: Phaser.Scene) {
    this.scene = scene;
    this.container = scene.add.container(20, scene.scale.height - 120);
    this.createElements();
  }

  private createElements(): void {
    // 背景
    const background = this.scene.add.rectangle(0, 0, 300, 100, 0x34495e, 0.9);
    background.setStrokeStyle(2, 0x2c3e50);

    // 行动按钮
    const endTurnButton = this.createButton(-100, 0, '结束回合', () => {
      console.log('结束回合');
    });

    const buildButton = this.createButton(0, 0, '建造', () => {
      console.log('建造建筑');
    });

    const tradeButton = this.createButton(100, 0, '贸易', () => {
      console.log('贸易');
    });

    this.container.add([background, endTurnButton, buildButton, tradeButton]);
  }

  private createButton(x: number, y: number, text: string, callback: () => void): Phaser.GameObjects.Container {
    const button = this.scene.add.container(x, y);
    
    const background = this.scene.add.rectangle(0, 0, 80, 30, 0x3498db);
    background.setStrokeStyle(2, 0x2980b9);
    background.setInteractive({ useHandCursor: true });
    
    const buttonText = this.scene.add.text(0, 0, text, {
      fontSize: '12px',
      color: '#ffffff'
    }).setOrigin(0.5);

    background.on('pointerdown', callback);
    
    background.on('pointerover', () => {
      background.setFillStyle(0x5dade2);
    });

    background.on('pointerout', () => {
      background.setFillStyle(0x3498db);
    });

    button.add([background, buttonText]);
    return button;
  }

  public show(): void {
    this.isVisible = true;
    this.container.setVisible(true);
  }

  public hide(): void {
    this.isVisible = false;
    this.container.setVisible(false);
  }

  public update(data: any): void {
    // 更新行动面板
  }
}

/**
 * 卡牌面板
 */
class CardPanel implements UIPanel {
  public container: Phaser.GameObjects.Container;
  public isVisible: boolean = false;
  private scene: Phaser.Scene;

  constructor(scene: Phaser.Scene) {
    this.scene = scene;
    this.container = scene.add.container(scene.scale.width / 2, scene.scale.height / 2);
    this.createElements();
    this.hide();
  }

  private createElements(): void {
    // 背景
    const background = this.scene.add.rectangle(0, 0, 600, 400, 0x2c3e50, 0.95);
    background.setStrokeStyle(3, 0x34495e);

    // 标题
    const title = this.scene.add.text(0, -180, '卡牌管理', {
      fontSize: '20px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    // 关闭按钮
    const closeButton = this.scene.add.text(280, -180, '✕', {
      fontSize: '20px',
      color: '#e74c3c'
    }).setOrigin(0.5);
    
    closeButton.setInteractive({ useHandCursor: true });
    closeButton.on('pointerdown', () => this.hide());

    this.container.add([background, title, closeButton]);
  }

  public show(): void {
    this.isVisible = true;
    this.container.setVisible(true);
  }

  public hide(): void {
    this.isVisible = false;
    this.container.setVisible(false);
  }

  public update(data: any): void {
    // 更新卡牌显示
  }
}

/**
 * 建筑面板
 */
class BuildingPanel implements UIPanel {
  public container: Phaser.GameObjects.Container;
  public isVisible: boolean = false;
  private scene: Phaser.Scene;

  constructor(scene: Phaser.Scene) {
    this.scene = scene;
    this.container = scene.add.container(scene.scale.width / 2, scene.scale.height / 2);
    this.createElements();
    this.hide();
  }

  private createElements(): void {
    // 背景
    const background = this.scene.add.rectangle(0, 0, 500, 350, 0x2c3e50, 0.95);
    background.setStrokeStyle(3, 0x34495e);

    // 标题
    const title = this.scene.add.text(0, -150, '建筑建造', {
      fontSize: '20px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    this.container.add([background, title]);
  }

  public show(): void {
    this.isVisible = true;
    this.container.setVisible(true);
  }

  public hide(): void {
    this.isVisible = false;
    this.container.setVisible(false);
  }

  public update(data: any): void {
    // 更新建筑选项
  }
}

/**
 * 外交面板
 */
class DiplomacyPanel implements UIPanel {
  public container: Phaser.GameObjects.Container;
  public isVisible: boolean = false;
  private scene: Phaser.Scene;

  constructor(scene: Phaser.Scene) {
    this.scene = scene;
    this.container = scene.add.container(scene.scale.width / 2, scene.scale.height / 2);
    this.createElements();
    this.hide();
  }

  private createElements(): void {
    // 背景
    const background = this.scene.add.rectangle(0, 0, 550, 400, 0x2c3e50, 0.95);
    background.setStrokeStyle(3, 0x34495e);

    // 标题
    const title = this.scene.add.text(0, -180, '外交关系', {
      fontSize: '20px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    this.container.add([background, title]);
  }

  public show(): void {
    this.isVisible = true;
    this.container.setVisible(true);
  }

  public hide(): void {
    this.isVisible = false;
    this.container.setVisible(false);
  }

  public update(data: any): void {
    // 更新外交信息
  }
}
