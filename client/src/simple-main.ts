// 最简单的测试版本
console.log('🎮 开始加载简单版本...');

// 隐藏加载提示
const loadingElement = document.getElementById('loading');
if (loadingElement) {
  loadingElement.style.display = 'none';
}

// 创建简单的游戏界面
const container = document.getElementById('game-container');
if (container) {
  container.innerHTML = `
    <div style="
      width: 1024px; 
      height: 768px; 
      background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460, #533483);
      margin: 0 auto;
      border-radius: 10px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: white;
      font-family: Arial, sans-serif;
    ">
      <h1 style="font-size: 48px; margin-bottom: 20px; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">
        🎮 多人在线游戏
      </h1>
      <p style="font-size: 18px; margin-bottom: 40px; opacity: 0.8;">
        基于 Phaser3 & Colyseus 构建
      </p>
      
      <div style="display: flex; gap: 20px; margin-bottom: 40px;">
        <button id="testBtn" style="
          padding: 15px 30px;
          font-size: 16px;
          background: #4CAF50;
          color: white;
          border: none;
          border-radius: 5px;
          cursor: pointer;
          transition: background 0.3s;
        ">测试服务器连接</button>
        
        <button id="startBtn" style="
          padding: 15px 30px;
          font-size: 16px;
          background: #2196F3;
          color: white;
          border: none;
          border-radius: 5px;
          cursor: pointer;
          transition: background 0.3s;
        ">启动完整游戏</button>
      </div>
      
      <div id="status" style="
        width: 80%;
        max-height: 200px;
        overflow-y: auto;
        background: rgba(0,0,0,0.3);
        padding: 20px;
        border-radius: 5px;
        font-family: monospace;
        font-size: 14px;
      "></div>
    </div>
  `;

  // 添加事件监听器
  const testBtn = document.getElementById('testBtn');
  const startBtn = document.getElementById('startBtn');
  const status = document.getElementById('status');

  function addLog(message: string, type: 'info' | 'success' | 'error' = 'info') {
    if (status) {
      const color = type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3';
      const time = new Date().toLocaleTimeString();
      status.innerHTML += `<div style="color: ${color}; margin: 5px 0;">[${time}] ${message}</div>`;
      status.scrollTop = status.scrollHeight;
    }
  }

  if (testBtn) {
    testBtn.addEventListener('click', async () => {
      addLog('开始测试服务器连接...', 'info');
      
      try {
        const response = await fetch('http://localhost:2567/rooms/stats/overview');
        const data = await response.json();
        addLog('✅ 服务器连接成功！', 'success');
        addLog(`📊 统计信息: ${JSON.stringify(data.data)}`, 'info');
      } catch (error) {
        addLog(`❌ 服务器连接失败: ${error}`, 'error');
      }
    });
  }

  if (startBtn) {
    startBtn.addEventListener('click', async () => {
      addLog('开始加载完整游戏...', 'info');
      
      try {
        // 动态导入完整游戏
        addLog('导入 Phaser...', 'info');
        const Phaser = (await import('phaser')).default;
        
        addLog('导入游戏配置...', 'info');
        const { GAME_CONFIG } = await import('./types/shared');
        
        addLog('导入场景...', 'info');
        const { LoginScene } = await import('./scenes/LoginScene');
        const { LobbyScene } = await import('./scenes/LobbyScene');
        const { GameScene } = await import('./scenes/GameScene');
        
        addLog('导入网络管理器...', 'info');
        const { NetworkManager } = await import('./network/NetworkManager');
        
        addLog('创建游戏实例...', 'info');
        
        // 清空容器
        if (container) {
          container.innerHTML = '';
        }
        
        // 游戏配置
        const config: Phaser.Types.Core.GameConfig = {
          type: Phaser.AUTO,
          width: GAME_CONFIG.GAME_WIDTH,
          height: GAME_CONFIG.GAME_HEIGHT,
          parent: 'game-container',
          backgroundColor: '#2c3e50',
          scene: [LoginScene, LobbyScene, GameScene],
          physics: {
            default: 'arcade',
            arcade: {
              gravity: { y: 0 },
              debug: false
            }
          },
          scale: {
            mode: Phaser.Scale.FIT,
            autoCenter: Phaser.Scale.CENTER_BOTH,
            min: {
              width: 800,
              height: 600
            },
            max: {
              width: 1600,
              height: 1200
            }
          }
        };

        // 创建游戏实例
        const game = new Phaser.Game(config);

        // 初始化网络管理器
        const networkManager = NetworkManager.getInstance();

        // 将网络管理器添加到游戏注册表中
        game.registry.set('networkManager', networkManager);

        addLog('🎮 游戏启动成功！', 'success');
        
      } catch (error) {
        addLog(`❌ 游戏启动失败: ${error}`, 'error');
        console.error('游戏启动详细错误:', error);
      }
    });
  }

  // 页面加载完成后的初始化
  addLog('🚀 简单版本加载完成', 'success');
  addLog('点击"测试服务器连接"检查后端状态', 'info');
  addLog('点击"启动完整游戏"加载 Phaser3 游戏', 'info');
}

console.log('✅ 简单版本初始化完成');
