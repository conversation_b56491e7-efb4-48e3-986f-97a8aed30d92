import Phaser from 'phaser';
import { GAME_CONFIG } from '@multiplayer-game/shared';
import { LoginScene } from './scenes/LoginScene';
import { LobbyScene } from './scenes/LobbyScene';
import { GameScene } from './scenes/GameScene';
import { NetworkManager } from './network/NetworkManager';

// 游戏配置
const config: Phaser.Types.Core.GameConfig = {
  type: Phaser.AUTO,
  width: GAME_CONFIG.GAME_WIDTH,
  height: GAME_CONFIG.GAME_HEIGHT,
  parent: 'game-container',
  backgroundColor: '#2c3e50',
  scene: [LoginScene, LobbyScene, GameScene],
  physics: {
    default: 'arcade',
    arcade: {
      gravity: { y: 0 },
      debug: false
    }
  },
  scale: {
    mode: Phaser.Scale.FIT,
    autoCenter: Phaser.Scale.CENTER_BOTH,
    min: {
      width: 800,
      height: 600
    },
    max: {
      width: 1600,
      height: 1200
    }
  }
};

// 创建游戏实例
const game = new Phaser.Game(config);

// 初始化网络管理器
const networkManager = NetworkManager.getInstance();

// 将网络管理器添加到游戏注册表中，以便场景可以访问
game.registry.set('networkManager', networkManager);

// 隐藏加载提示
const loadingElement = document.getElementById('loading');
if (loadingElement) {
  loadingElement.style.display = 'none';
}

console.log('🎮 游戏客户端启动成功！');
