console.log('🎮 开始加载游戏...');

// 隐藏加载提示
const loadingElement = document.getElementById('loading');
if (loadingElement) {
  loadingElement.style.display = 'none';
}

// 先测试基础导入
try {
  console.log('导入 Phaser...');
  const Phaser = await import('phaser');
  console.log('✅ Phaser 导入成功');

  console.log('导入共享类型...');
  const { GAME_CONFIG } = await import('./types/shared');
  console.log('✅ 共享类型导入成功');

  console.log('导入场景...');
  const { LoginScene } = await import('./scenes/LoginScene');
  const { LobbyScene } = await import('./scenes/LobbyScene');
  const { GameScene } = await import('./scenes/GameScene');
  const { StrategicMapScene } = await import('./scenes/StrategicMapScene');
  const { CombatScene } = await import('./scenes/CombatScene');
  console.log('✅ 场景导入成功');

  console.log('导入网络管理器...');
  const { NetworkManager } = await import('./network/NetworkManager');
  console.log('✅ 网络管理器导入成功');

  // 游戏配置
  const config: Phaser.Types.Core.GameConfig = {
    type: Phaser.AUTO,
    width: GAME_CONFIG.GAME_WIDTH,
    height: GAME_CONFIG.GAME_HEIGHT,
    parent: 'game-container',
    backgroundColor: '#2c3e50',
    scene: [LoginScene, LobbyScene, GameScene, StrategicMapScene, CombatScene],
    physics: {
      default: 'arcade',
      arcade: {
        gravity: { y: 0 },
        debug: false
      }
    },
    scale: {
      mode: Phaser.Scale.FIT,
      autoCenter: Phaser.Scale.CENTER_BOTH,
      min: {
        width: 800,
        height: 600
      },
      max: {
        width: 1600,
        height: 1200
      }
    }
  };

  console.log('创建游戏实例...');
  // 创建游戏实例
  const game = new Phaser.Game(config);

  // 初始化网络管理器
  const networkManager = NetworkManager.getInstance();

  // 将网络管理器添加到游戏注册表中，以便场景可以访问
  game.registry.set('networkManager', networkManager);

  console.log('🎮 游戏客户端启动成功！');

} catch (error) {
  console.error('❌ 游戏加载失败:', error);

  // 显示错误信息
  const container = document.getElementById('game-container');
  if (container) {
    container.innerHTML = `
      <div style="color: white; text-align: center; padding: 50px;">
        <h2>游戏加载失败</h2>
        <p>错误信息: ${error}</p>
        <button onclick="location.reload()" style="padding: 10px 20px; margin-top: 20px;">重新加载</button>
      </div>
    `;
  }
}
