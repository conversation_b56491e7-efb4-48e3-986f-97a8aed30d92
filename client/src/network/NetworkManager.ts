import { Client, Room } from 'colyseus.js';
import { AuthResponse, LoginRequest, RegisterRequest, User } from '@multiplayer-game/shared';

export class NetworkManager {
  private static instance: NetworkManager;
  private client: Client;
  private currentUser: User | null = null;
  private currentRoom: Room | null = null;
  private token: string | null = null;

  private constructor() {
    // 连接到Colyseus服务器
    this.client = new Client('ws://localhost:2567');
  }

  public static getInstance(): NetworkManager {
    if (!NetworkManager.instance) {
      NetworkManager.instance = new NetworkManager();
    }
    return NetworkManager.instance;
  }

  // 用户认证
  public async login(username: string, password: string): Promise<AuthResponse> {
    try {
      const response = await fetch('http://localhost:2567/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password } as LoginRequest),
      });

      const result: AuthResponse = await response.json();
      
      if (result.success && result.user && result.token) {
        this.currentUser = result.user;
        this.token = result.token;
        localStorage.setItem('auth_token', result.token);
        localStorage.setItem('user', JSON.stringify(result.user));
      }

      return result;
    } catch (error) {
      console.error('登录错误:', error);
      return {
        success: false,
        message: '网络连接错误'
      };
    }
  }

  public async register(username: string, password: string): Promise<AuthResponse> {
    try {
      const response = await fetch('http://localhost:2567/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password } as RegisterRequest),
      });

      const result: AuthResponse = await response.json();
      
      if (result.success && result.user && result.token) {
        this.currentUser = result.user;
        this.token = result.token;
        localStorage.setItem('auth_token', result.token);
        localStorage.setItem('user', JSON.stringify(result.user));
      }

      return result;
    } catch (error) {
      console.error('注册错误:', error);
      return {
        success: false,
        message: '网络连接错误'
      };
    }
  }

  // 自动登录（使用存储的token）
  public autoLogin(): boolean {
    const token = localStorage.getItem('auth_token');
    const userStr = localStorage.getItem('user');
    
    if (token && userStr) {
      try {
        this.token = token;
        this.currentUser = JSON.parse(userStr);
        return true;
      } catch (error) {
        console.error('自动登录失败:', error);
        this.logout();
      }
    }
    
    return false;
  }

  // 登出
  public logout(): void {
    this.currentUser = null;
    this.token = null;
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user');
    
    if (this.currentRoom) {
      this.currentRoom.leave();
      this.currentRoom = null;
    }
  }

  // 加入房间
  public async joinRoom(roomName: string, options: any = {}): Promise<Room | null> {
    try {
      if (!this.token) {
        throw new Error('未登录');
      }

      const room = await this.client.joinOrCreate(roomName, {
        ...options,
        token: this.token,
        user: this.currentUser
      });

      this.currentRoom = room;
      this.setupRoomErrorHandling(room);
      return room;
    } catch (error) {
      console.error('加入房间失败:', error);
      return null;
    }
  }

  // 加入指定房间
  public async joinSpecificRoom(roomId: string): Promise<Room | null> {
    try {
      if (!this.token) {
        throw new Error('未登录');
      }

      const room = await this.client.joinById(roomId, {
        token: this.token,
        user: this.currentUser
      });

      this.currentRoom = room;
      this.setupRoomErrorHandling(room);
      return room;
    } catch (error) {
      console.error('加入指定房间失败:', error);
      return null;
    }
  }

  // 设置房间错误处理
  private setupRoomErrorHandling(room: Room) {
    room.onError((code, message) => {
      console.error('房间错误:', code, message);
    });

    room.onLeave((code) => {
      console.log('离开房间:', code);
      this.currentRoom = null;
    });
  }

  // 离开房间
  public leaveRoom(): void {
    if (this.currentRoom) {
      this.currentRoom.leave();
      this.currentRoom = null;
    }
  }

  // Getters
  public getCurrentUser(): User | null {
    return this.currentUser;
  }

  public getCurrentRoom(): Room | null {
    return this.currentRoom;
  }

  public isLoggedIn(): boolean {
    return this.currentUser !== null && this.token !== null;
  }

  // 发送房间消息
  public sendRoomMessage(type: string, data?: any): void {
    if (this.currentRoom) {
      this.currentRoom.send(type, data);
    } else {
      console.warn('没有连接到房间，无法发送消息');
    }
  }

  // 获取网络状态
  public getNetworkStatus() {
    return {
      isLoggedIn: this.isLoggedIn(),
      hasRoom: this.currentRoom !== null,
      roomId: this.currentRoom?.id,
      userId: this.currentUser?.id,
      username: this.currentUser?.username
    };
  }

  // 重连房间
  public async reconnectRoom(): Promise<Room | null> {
    if (this.currentRoom) {
      try {
        await this.currentRoom.reconnect();
        return this.currentRoom;
      } catch (error) {
        console.error('重连房间失败:', error);
        this.currentRoom = null;
        return null;
      }
    }
    return null;
  }

  // 检查连接状态
  public isConnected(): boolean {
    return this.currentRoom?.connection.readyState === WebSocket.OPEN;
  }
}
