import { Client, Room } from 'colyseus.js';
import { AuthResponse, LoginRequest, RegisterRequest, User } from '../types/shared';

export class NetworkManager {
  private static instance: NetworkManager;
  private client: Client;
  private currentUser: User | null = null;
  private currentRoom: Room | null = null;
  private token: string | null = null;

  private constructor() {
    // 连接到Colyseus服务器
    this.client = new Client('ws://localhost:2567');
  }

  public static getInstance(): NetworkManager {
    if (!NetworkManager.instance) {
      NetworkManager.instance = new NetworkManager();
    }
    return NetworkManager.instance;
  }

  // 用户认证
  public async login(username: string, password: string): Promise<AuthResponse> {
    try {
      const response = await fetch('http://localhost:2567/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password } as LoginRequest),
      });

      const result: AuthResponse = await response.json();
      
      if (result.success && result.user && result.token) {
        this.currentUser = result.user;
        this.token = result.token;
        localStorage.setItem('auth_token', result.token);
        localStorage.setItem('user', JSON.stringify(result.user));
      }

      return result;
    } catch (error) {
      console.error('登录错误:', error);
      return {
        success: false,
        message: '网络连接错误'
      };
    }
  }

  public async register(username: string, password: string): Promise<AuthResponse> {
    try {
      const response = await fetch('http://localhost:2567/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password } as RegisterRequest),
      });

      const result: AuthResponse = await response.json();
      
      if (result.success && result.user && result.token) {
        this.currentUser = result.user;
        this.token = result.token;
        localStorage.setItem('auth_token', result.token);
        localStorage.setItem('user', JSON.stringify(result.user));
      }

      return result;
    } catch (error) {
      console.error('注册错误:', error);
      return {
        success: false,
        message: '网络连接错误'
      };
    }
  }

  // 自动登录（使用存储的token）
  public autoLogin(): boolean {
    const token = localStorage.getItem('auth_token');
    const userStr = localStorage.getItem('user');
    
    if (token && userStr) {
      try {
        this.token = token;
        this.currentUser = JSON.parse(userStr);
        return true;
      } catch (error) {
        console.error('自动登录失败:', error);
        this.logout();
      }
    }
    
    return false;
  }

  // 登出
  public logout(): void {
    this.currentUser = null;
    this.token = null;
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user');
    
    if (this.currentRoom) {
      this.currentRoom.leave();
      this.currentRoom = null;
    }
  }

  // 加入房间（模拟实现）
  public async joinRoom(roomName: string, options: any = {}): Promise<Room | null> {
    try {
      if (!this.token) {
        throw new Error('未登录');
      }

      console.log('模拟创建房间:', roomName, options);

      // 创建一个模拟的房间对象
      const mockRoom = this.createMockRoom(options.name || '新房间');
      this.currentRoom = mockRoom as any;

      return mockRoom as any;
    } catch (error) {
      console.error('加入房间失败:', error);
      return null;
    }
  }

  // 加入指定房间
  public async joinSpecificRoom(roomId: string): Promise<Room | null> {
    try {
      if (!this.token) {
        throw new Error('未登录');
      }

      const room = await this.client.joinById(roomId, {
        token: this.token,
        user: this.currentUser
      });

      this.currentRoom = room;
      this.setupRoomErrorHandling(room);
      return room;
    } catch (error) {
      console.error('加入指定房间失败:', error);
      return null;
    }
  }

  // 设置房间错误处理
  private setupRoomErrorHandling(room: Room) {
    room.onError((code, message) => {
      console.error('房间错误:', code, message);
    });

    room.onLeave((code) => {
      console.log('离开房间:', code);
      this.currentRoom = null;
    });
  }

  // 离开房间
  public leaveRoom(): void {
    if (this.currentRoom) {
      this.currentRoom.leave();
      this.currentRoom = null;
    }
  }

  // Getters
  public getCurrentUser(): User | null {
    return this.currentUser;
  }

  public getCurrentRoom(): Room | null {
    return this.currentRoom;
  }

  public isLoggedIn(): boolean {
    return this.currentUser !== null && this.token !== null;
  }

  // 发送房间消息
  public sendRoomMessage(type: string, data?: any): void {
    if (this.currentRoom) {
      this.currentRoom.send(type, data);
    } else {
      console.warn('没有连接到房间，无法发送消息');
    }
  }

  // 获取网络状态
  public getNetworkStatus() {
    return {
      isLoggedIn: this.isLoggedIn(),
      hasRoom: this.currentRoom !== null,
      roomId: this.currentRoom?.id,
      userId: this.currentUser?.id,
      username: this.currentUser?.username
    };
  }

  // 重连房间
  public async reconnectRoom(): Promise<Room | null> {
    if (this.currentRoom) {
      try {
        await this.currentRoom.reconnect();
        return this.currentRoom;
      } catch (error) {
        console.error('重连房间失败:', error);
        this.currentRoom = null;
        return null;
      }
    }
    return null;
  }

  // 检查连接状态
  public isConnected(): boolean {
    return this.currentRoom !== null;
  }

  // 创建模拟房间
  private createMockRoom(roomName: string) {
    const roomId = 'room_' + Math.random().toString(36).substring(2);

    const mockRoom = {
      id: roomId,
      sessionId: 'session_' + Math.random().toString(36).substring(2),
      name: roomName,

      // 模拟状态
      state: {
        players: new Map(),
        gameState: 'waiting',
        maxPlayers: 8,
        playerCount: 0
      },

      // 模拟方法
      send: (type: string, data?: any) => {
        console.log('发送消息:', type, data);

        // 模拟一些响应
        setTimeout(() => {
          if (type === 'player_ready') {
            this.simulateMessage('player_ready_changed', {
              playerId: mockRoom.sessionId,
              isReady: true,
              username: this.currentUser?.username
            });
          } else if (type === 'add_bots') {
            this.handleAddBotsRequest(data);
          } else if (type === 'remove_bots') {
            this.handleRemoveBotsRequest();
          }
        }, 100);
      },

      leave: () => {
        console.log('离开房间:', roomId);
        this.currentRoom = null;
      },

      // 事件监听器
      onStateChange: (callback: Function) => {
        console.log('监听状态变化');
        // 存储回调以便后续调用
        mockRoom._stateChangeCallback = callback;

        // 模拟初始状态
        setTimeout(() => {
          callback({
            players: mockRoom.state.players,
            gameState: mockRoom.state.gameState,
            maxPlayers: mockRoom.state.maxPlayers,
            playerCount: mockRoom.state.playerCount
          });
        }, 100);
      },

      onMessage: (type: string, callback: Function) => {
        console.log('监听消息:', type);
        // 存储回调以便后续调用
        if (!mockRoom._messageHandlers) {
          mockRoom._messageHandlers = new Map();
        }
        mockRoom._messageHandlers.set(type, callback);
      },

      onError: (callback: Function) => {
        console.log('监听错误');
      },

      onLeave: (callback: Function) => {
        console.log('监听离开');
      },

      _messageHandlers: new Map(),
      _stateChangeCallback: null,

      // 添加更新状态的方法
      updateState: function() {
        if (this._stateChangeCallback) {
          this._stateChangeCallback({
            players: this.state.players,
            gameState: this.state.gameState,
            maxPlayers: this.state.maxPlayers,
            playerCount: this.state.playerCount
          });
        }
      }
    };

    // 添加当前玩家到房间状态
    mockRoom.state.players.set(mockRoom.sessionId, {
      id: mockRoom.sessionId,
      username: this.currentUser?.username || '玩家',
      isReady: false,
      x: Math.random() * 800 + 100,
      y: Math.random() * 600 + 100
    });
    mockRoom.state.playerCount = mockRoom.state.players.size;

    // 模拟欢迎消息
    setTimeout(() => {
      this.simulateMessage('welcome', {
        message: `欢迎 ${this.currentUser?.username} 加入房间 ${roomName}！`,
        roomId: roomId,
        playerId: mockRoom.sessionId
      });
    }, 500);

    return mockRoom;
  }

  // 模拟消息发送
  private simulateMessage(type: string, data: any) {
    if (this.currentRoom && (this.currentRoom as any)._messageHandlers) {
      const handler = (this.currentRoom as any)._messageHandlers.get(type);
      if (handler) {
        handler(data);
      }
    }
  }

  // 处理添加机器人请求
  private async handleAddBotsRequest(data: any) {
    try {
      const response = await fetch('http://localhost:2567/bots/add', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          roomId: this.currentRoom?.id,
          count: data.count || 3
        })
      });

      const result = await response.json();

      if (result.success) {
        // 将机器人添加到模拟房间状态
        if (this.currentRoom && result.bots) {
          const mockRoom = this.currentRoom as any;

          result.bots.forEach((bot: any, index: number) => {
            // 添加到房间状态
            mockRoom.state.players.set(bot.id, {
              id: bot.id,
              username: bot.username,
              isReady: bot.isReady,
              x: bot.position.x,
              y: bot.position.y
            });

            // 更新玩家计数
            mockRoom.state.playerCount = mockRoom.state.players.size;

            // 模拟玩家加入事件
            setTimeout(() => {
              this.simulateMessage('player_joined', {
                player: {
                  id: bot.id,
                  username: bot.username,
                  isReady: bot.isReady,
                  x: bot.position.x,
                  y: bot.position.y
                },
                message: `${bot.username} (机器人) 加入了游戏`
              });

              // 触发状态更新
              if (mockRoom.updateState) {
                mockRoom.updateState();
              }
            }, 100 * (index + 1)); // 错开时间
          });
        }

        this.simulateMessage('bots_added', {
          message: result.message,
          bots: result.bots
        });
      } else {
        this.simulateMessage('error', {
          message: result.message || '添加机器人失败'
        });
      }
    } catch (error) {
      console.error('添加机器人错误:', error);
      this.simulateMessage('error', {
        message: '网络错误，添加机器人失败'
      });
    }
  }

  // 处理移除机器人请求
  private async handleRemoveBotsRequest() {
    try {
      const response = await fetch('http://localhost:2567/bots/remove', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          roomId: this.currentRoom?.id
        })
      });

      const result = await response.json();

      if (result.success) {
        this.simulateMessage('bots_removed', {
          message: result.message
        });
      } else {
        this.simulateMessage('error', {
          message: result.message || '移除机器人失败'
        });
      }
    } catch (error) {
      console.error('移除机器人错误:', error);
      this.simulateMessage('error', {
        message: '网络错误，移除机器人失败'
      });
    }
  }
}
