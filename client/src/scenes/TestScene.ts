import Phaser from 'phaser';
import { NetworkManager } from '../network/NetworkManager';

/**
 * 测试场景
 * 用于验证场景切换功能
 */
export class TestScene extends Phaser.Scene {
  private networkManager!: NetworkManager;

  constructor() {
    super({ key: 'TestScene' });
  }

  init(data: any) {
    this.networkManager = data.networkManager;
    console.log('🧪 测试场景初始化', data);
  }

  create() {
    console.log('🧪 测试场景创建成功！');
    
    // 创建背景
    this.add.rectangle(this.scale.width / 2, this.scale.height / 2, this.scale.width, this.scale.height, 0x27ae60);
    
    // 成功消息
    const title = this.add.text(this.scale.width / 2, this.scale.height / 2 - 100, '🎉 场景切换成功！', {
      fontSize: '48px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    const subtitle = this.add.text(this.scale.width / 2, this.scale.height / 2, '这证明游戏架构工作正常', {
      fontSize: '24px',
      color: '#ffffff'
    }).setOrigin(0.5);

    // 功能按钮
    this.createButtons();

    // 显示游戏信息
    this.showGameInfo();
  }

  private createButtons(): void {
    const buttonY = this.scale.height / 2 + 100;
    
    // 返回大厅按钮
    const backButton = this.createButton(this.scale.width / 2 - 100, buttonY, '返回大厅', () => {
      console.log('返回大厅');
      this.scene.start('LobbyScene', { networkManager: this.networkManager });
    });

    // 测试战略地图按钮
    const mapButton = this.createButton(this.scale.width / 2 + 100, buttonY, '测试地图', () => {
      console.log('尝试切换到战略地图');
      try {
        this.scene.start('SimpleStrategicMapScene', { networkManager: this.networkManager });
      } catch (error) {
        console.error('切换失败:', error);
        this.showMessage('战略地图场景加载失败', 0xe74c3c);
      }
    });
  }

  private createButton(x: number, y: number, text: string, callback: () => void): Phaser.GameObjects.Container {
    const button = this.add.container(x, y);
    
    const background = this.add.rectangle(0, 0, 150, 50, 0x3498db);
    background.setStrokeStyle(3, 0x2980b9);
    background.setInteractive({ useHandCursor: true });
    
    const buttonText = this.add.text(0, 0, text, {
      fontSize: '16px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    background.on('pointerdown', callback);
    
    background.on('pointerover', () => {
      background.setFillStyle(0x5dade2);
    });

    background.on('pointerout', () => {
      background.setFillStyle(0x3498db);
    });

    button.add([background, buttonText]);
    return button;
  }

  private showGameInfo(): void {
    const info = this.add.text(20, 20, 
      '🎮 游戏状态检查:\n' +
      '✅ Phaser引擎正常\n' +
      '✅ 场景系统正常\n' +
      '✅ 网络管理器正常\n' +
      '✅ 事件系统正常', {
      fontSize: '16px',
      color: '#ffffff',
      lineSpacing: 5
    });

    // 显示技术信息
    const techInfo = this.add.text(this.scale.width - 20, 20, 
      '🔧 技术信息:\n' +
      `Phaser版本: ${Phaser.VERSION}\n` +
      `画布大小: ${this.scale.width}x${this.scale.height}\n` +
      `渲染器: ${this.renderer.type === Phaser.WEBGL ? 'WebGL' : 'Canvas'}\n` +
      `场景数量: ${this.scene.manager.scenes.length}`, {
      fontSize: '14px',
      color: '#ecf0f1',
      lineSpacing: 5
    }).setOrigin(1, 0);
  }

  private showMessage(text: string, color: number): void {
    const message = this.add.text(this.scale.width / 2, this.scale.height / 2 + 200, text, {
      fontSize: '18px',
      color: '#ffffff',
      backgroundColor: Phaser.Display.Color.IntegerToRGB(color).rgba,
      padding: { x: 20, y: 10 }
    }).setOrigin(0.5);

    // 3秒后消失
    this.time.delayedCall(3000, () => {
      message.destroy();
    });
  }
}
