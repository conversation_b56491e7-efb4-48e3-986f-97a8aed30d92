import Phaser from 'phaser';
import { NetworkManager } from '../network/NetworkManager';

/**
 * 简化的战略地图场景
 * 用于测试场景切换功能
 */
export class SimpleStrategicMapScene extends Phaser.Scene {
  private networkManager!: NetworkManager;

  constructor() {
    super({ key: 'SimpleStrategicMapScene' });
  }

  init(data: any) {
    this.networkManager = data.networkManager;
    console.log('🗺️ 简化战略地图场景初始化', data);
  }

  create() {
    console.log('🗺️ 简化战略地图场景创建');
    
    // 创建背景
    this.add.rectangle(this.scale.width / 2, this.scale.height / 2, this.scale.width, this.scale.height, 0x2c3e50);
    
    // 标题
    const title = this.add.text(this.scale.width / 2, 100, '🗺️ 战略地图', {
      fontSize: '32px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    // 说明文字
    const description = this.add.text(this.scale.width / 2, 200, 
      '欢迎来到战略地图！\n\n这里是SLG战略层的核心界面\n你可以在这里：\n• 管理领土和资源\n• 移动军队\n• 建造建筑\n• 制定战略', {
      fontSize: '18px',
      color: '#ecf0f1',
      align: 'center',
      lineSpacing: 10
    }).setOrigin(0.5);

    // 创建一个简单的六边形网格示意
    this.createSimpleHexGrid();

    // 创建UI按钮
    this.createUIButtons();

    // 显示游戏信息
    this.createGameInfo();
  }

  /**
   * 创建简单的六边形网格示意
   */
  private createSimpleHexGrid(): void {
    const centerX = this.scale.width / 2;
    const centerY = this.scale.height / 2 + 50;
    const hexSize = 30;
    
    // 创建一个3x3的六边形网格示意
    for (let row = 0; row < 3; row++) {
      for (let col = 0; col < 4; col++) {
        const x = centerX + (col - 1.5) * hexSize * 1.5;
        const y = centerY + (row - 1) * hexSize * Math.sqrt(3) + (col % 2) * hexSize * Math.sqrt(3) / 2;
        
        // 创建六边形
        const hex = this.add.graphics();
        hex.lineStyle(2, 0x3498db);
        hex.fillStyle(0x34495e, 0.7);
        
        // 绘制六边形
        hex.beginPath();
        for (let i = 0; i < 6; i++) {
          const angle = (i * 60) * Math.PI / 180;
          const hexX = x + hexSize * Math.cos(angle);
          const hexY = y + hexSize * Math.sin(angle);
          
          if (i === 0) {
            hex.moveTo(hexX, hexY);
          } else {
            hex.lineTo(hexX, hexY);
          }
        }
        hex.closePath();
        hex.fillPath();
        hex.strokePath();

        // 添加地形图标
        const terrainIcons = ['🏔️', '🌲', '🌾', '💎'];
        const icon = terrainIcons[(row * 4 + col) % terrainIcons.length];
        this.add.text(x, y, icon, {
          fontSize: '16px'
        }).setOrigin(0.5);
      }
    }
  }

  /**
   * 创建UI按钮
   */
  private createUIButtons(): void {
    const buttonY = this.scale.height - 80;
    
    // 结束回合按钮
    const endTurnButton = this.createButton(150, buttonY, '结束回合', () => {
      console.log('🔄 结束回合');
      this.showMessage('回合结束！', 0x27ae60);
    });

    // 建造按钮
    const buildButton = this.createButton(300, buttonY, '建造', () => {
      console.log('🏗️ 建造建筑');
      this.showMessage('建造功能开发中...', 0xf39c12);
    });

    // 军队按钮
    const armyButton = this.createButton(450, buttonY, '军队', () => {
      console.log('⚔️ 军队管理');
      this.showMessage('军队管理功能开发中...', 0xf39c12);
    });

    // 战斗按钮
    const combatButton = this.createButton(600, buttonY, '进入战斗', () => {
      console.log('⚔️ 切换到战斗场景');
      this.scene.start('CombatScene', { 
        networkManager: this.networkManager,
        cards: this.getTestCards()
      });
    });

    // 返回按钮
    const backButton = this.createButton(this.scale.width - 100, buttonY, '返回大厅', () => {
      console.log('🏠 返回大厅');
      this.scene.start('LobbyScene', { networkManager: this.networkManager });
    });
  }

  /**
   * 创建按钮
   */
  private createButton(x: number, y: number, text: string, callback: () => void): Phaser.GameObjects.Container {
    const button = this.add.container(x, y);
    
    const background = this.add.rectangle(0, 0, 120, 40, 0x3498db);
    background.setStrokeStyle(2, 0x2980b9);
    background.setInteractive({ useHandCursor: true });
    
    const buttonText = this.add.text(0, 0, text, {
      fontSize: '14px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    background.on('pointerdown', callback);
    
    background.on('pointerover', () => {
      background.setFillStyle(0x5dade2);
    });

    background.on('pointerout', () => {
      background.setFillStyle(0x3498db);
    });

    button.add([background, buttonText]);
    return button;
  }

  /**
   * 创建游戏信息显示
   */
  private createGameInfo(): void {
    // 资源信息
    const resourcesText = this.add.text(20, 20, '💰 金币: 100  👥 人口: 50  ⭐ 经验: 25', {
      fontSize: '16px',
      color: '#f1c40f',
      fontStyle: 'bold'
    });

    // 回合信息
    const turnText = this.add.text(20, 50, '🔄 第1回合 - 资源收集阶段', {
      fontSize: '16px',
      color: '#3498db'
    });

    // 天气信息
    const weatherText = this.add.text(this.scale.width - 20, 20, '🌤️ 天气: 晴天', {
      fontSize: '16px',
      color: '#27ae60'
    }).setOrigin(1, 0);
  }

  /**
   * 显示消息
   */
  private showMessage(text: string, color: number): void {
    const message = this.add.text(this.scale.width / 2, this.scale.height / 2 - 100, text, {
      fontSize: '20px',
      color: '#ffffff',
      backgroundColor: Phaser.Display.Color.IntegerToRGB(color).rgba,
      padding: { x: 20, y: 10 }
    }).setOrigin(0.5);

    // 3秒后消失
    this.time.delayedCall(3000, () => {
      message.destroy();
    });
  }

  /**
   * 获取测试卡牌
   */
  private getTestCards(): any[] {
    return [
      { id: 'test1', heroName: '刘备', cost: 3, rarity: 'legendary' },
      { id: 'test2', heroName: '关羽', cost: 4, rarity: 'epic' },
      { id: 'test3', heroName: '张飞', cost: 4, rarity: 'epic' }
    ];
  }
}
