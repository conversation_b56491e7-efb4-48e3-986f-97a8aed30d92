import Phaser from 'phaser';
import { Room } from 'colyseus.js';
import { NetworkManager } from '../network/NetworkManager';
import { GameState, GAME_CONFIG } from '../types/shared';

export class GameScene extends Phaser.Scene {
  private networkManager!: NetworkManager;
  private room!: Room;
  private players: Map<string, Phaser.GameObjects.Sprite> = new Map();
  private playerTexts: Map<string, Phaser.GameObjects.Text> = new Map();
  private gameUI!: Phaser.GameObjects.Container;
  private gameState: GameState = GameState.WAITING;
  private myPlayer!: Phaser.GameObjects.Sprite;
  private cursors!: Phaser.Types.Input.Keyboard.CursorKeys;
  private wasdKeys!: any;

  constructor() {
    super({ key: 'GameScene' });
  }

  init(data: any) {
    this.room = data.room;
  }

  create() {
    this.networkManager = this.registry.get('networkManager');
    
    if (!this.room) {
      console.error('没有房间数据，返回大厅');
      this.scene.start('LobbyScene');
      return;
    }

    this.createBackground();
    this.createGameArea();
    this.createUI();
    this.setupInput();
    this.setupRoomEvents();

    console.log('游戏场景创建完成，房间ID:', this.room.id);
  }

  private createBackground() {
    // 创建游戏背景
    const graphics = this.add.graphics();
    graphics.fillStyle(0x2c3e50);
    graphics.fillRect(0, 0, this.scale.width, this.scale.height);
    
    // 添加网格线
    graphics.lineStyle(1, 0x34495e, 0.5);
    const gridSize = 50;
    
    for (let x = 0; x <= this.scale.width; x += gridSize) {
      graphics.moveTo(x, 0);
      graphics.lineTo(x, this.scale.height);
    }
    
    for (let y = 0; y <= this.scale.height; y += gridSize) {
      graphics.moveTo(0, y);
      graphics.lineTo(this.scale.width, y);
    }
    
    graphics.strokePath();
  }

  private createGameArea() {
    // 创建游戏区域边界
    const gameArea = this.add.rectangle(
      this.scale.width / 2,
      this.scale.height / 2,
      GAME_CONFIG.GAME_WIDTH - 100,
      GAME_CONFIG.GAME_HEIGHT - 150,
      0x000000,
      0
    );
    gameArea.setStrokeStyle(3, 0x3498db);
  }

  private createUI() {
    this.gameUI = this.add.container(0, 0);
    
    // 顶部信息栏
    const topBar = this.add.rectangle(this.scale.width / 2, 30, this.scale.width, 60, 0x2c3e50, 0.9);
    topBar.setStrokeStyle(2, 0x34495e);
    
    // 房间信息
    const roomInfo = this.add.text(20, 20, `房间: ${this.room.name || this.room.id}`, {
      fontSize: '14px',
      color: '#ffffff'
    });
    
    // 玩家数量
    const playerCount = this.add.text(20, 40, '玩家: 0/8', {
      fontSize: '12px',
      color: '#bdc3c7'
    });
    
    // 游戏状态
    const gameStatus = this.add.text(this.scale.width / 2, 30, '等待玩家加入...', {
      fontSize: '16px',
      color: '#e74c3c',
      fontStyle: 'bold'
    }).setOrigin(0.5);
    
    // 控制按钮
    this.createControlButtons();

    // 游戏说明
    this.createInstructions();

    this.gameUI.add([topBar, roomInfo, playerCount, gameStatus]);
  }

  private createControlButtons() {
    const buttonY = this.scale.height - 40;

    // 准备按钮
    const readyButton = this.createButton(80, buttonY, '准备', () => {
      this.handleReady();
    }, 80, 30);

    // 添加机器人按钮
    const addBotsButton = this.createButton(180, buttonY, '添加机器人', () => {
      this.handleAddBots();
    }, 100, 30);

    // 移除机器人按钮
    const removeBotsButton = this.createButton(300, buttonY, '移除机器人', () => {
      this.handleRemoveBots();
    }, 100, 30);

    // 离开房间按钮
    const leaveButton = this.createButton(420, buttonY, '离开房间', () => {
      this.handleLeaveRoom();
    }, 100, 30);

    this.gameUI.add([readyButton, addBotsButton, removeBotsButton, leaveButton]);
  }

  private createInstructions() {
    const instructions = this.add.text(this.scale.width - 20, 100,
      '游戏说明:\n' +
      '• 使用方向键或WASD移动\n' +
      '• 点击"准备"开始游戏\n' +
      '• 🔴 红色圆圈是你的角色\n' +
      '• 🔵 蓝色圆圈是其他玩家\n' +
      '• 🟣 紫色圆圈是机器人🤖\n' +
      '• 点击"添加机器人"测试多人功能', {
      fontSize: '11px',
      color: '#bdc3c7',
      backgroundColor: '#2c3e50',
      padding: { x: 10, y: 8 },
      align: 'left'
    }).setOrigin(1, 0);

    this.gameUI.add(instructions);
  }

  private setupInput() {
    // 创建键盘输入
    this.cursors = this.input.keyboard!.createCursorKeys();
    this.wasdKeys = this.input.keyboard!.addKeys('W,S,A,D');
  }

  update() {
    // 只有在游戏进行中才能移动
    if (this.gameState === GameState.PLAYING && this.myPlayer) {
      this.handlePlayerMovement();
    }
  }

  private handlePlayerMovement() {
    const speed = 200;
    let velocityX = 0;
    let velocityY = 0;

    // 检查键盘输入
    if (this.cursors.left.isDown || this.wasdKeys.A.isDown) {
      velocityX = -speed;
    } else if (this.cursors.right.isDown || this.wasdKeys.D.isDown) {
      velocityX = speed;
    }

    if (this.cursors.up.isDown || this.wasdKeys.W.isDown) {
      velocityY = -speed;
    } else if (this.cursors.down.isDown || this.wasdKeys.S.isDown) {
      velocityY = speed;
    }

    // 如果有移动，更新位置并发送到服务器
    if (velocityX !== 0 || velocityY !== 0) {
      const deltaTime = this.game.loop.delta / 1000;
      const newX = this.myPlayer.x + velocityX * deltaTime;
      const newY = this.myPlayer.y + velocityY * deltaTime;

      // 边界检查
      const bounds = this.getGameBounds();
      const clampedX = Phaser.Math.Clamp(newX, bounds.left + 20, bounds.right - 20);
      const clampedY = Phaser.Math.Clamp(newY, bounds.top + 20, bounds.bottom - 20);

      // 更新本地位置
      this.myPlayer.setPosition(clampedX, clampedY);

      // 发送位置到服务器
      this.room.send('player_move', {
        x: clampedX,
        y: clampedY
      });
    }
  }

  private getGameBounds() {
    return {
      left: 50,
      right: this.scale.width - 50,
      top: 100,
      bottom: this.scale.height - 100
    };
  }

  private createButton(x: number, y: number, text: string, callback: () => void): Phaser.GameObjects.Container {
    const button = this.add.container(x, y);
    
    const background = this.add.rectangle(0, 0, 100, 30, 0x3498db);
    background.setStrokeStyle(2, 0x2980b9);
    
    const buttonText = this.add.text(0, 0, text, {
      fontSize: '12px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    button.add([background, buttonText]);
    button.setSize(100, 30);
    button.setInteractive({ useHandCursor: true });

    button.on('pointerdown', callback);
    
    button.on('pointerover', () => {
      background.setFillStyle(0x5dade2);
    });

    button.on('pointerout', () => {
      background.setFillStyle(0x3498db);
    });

    return button;
  }

  private setupRoomEvents() {
    // 监听房间状态变化
    this.room.onStateChange((state) => {
      this.updateGameState(state);
    });

    // 对于模拟房间，我们需要手动添加当前玩家
    if (this.room.sessionId) {
      setTimeout(() => {
        this.addPlayer(this.room.sessionId, {
          id: this.room.sessionId,
          username: this.networkManager.getCurrentUser()?.username || '玩家',
          isReady: false,
          x: Math.random() * (this.scale.width - 200) + 100,
          y: Math.random() * (this.scale.height - 200) + 100
        });
      }, 200);
    }

    // 监听房间消息
    this.room.onMessage('welcome', (message) => {
      console.log('欢迎消息:', message);
      this.showMessage(message.message, 0x27ae60);
    });

    this.room.onMessage('player_joined', (message) => {
      this.showMessage(message.message, 0x3498db);
      // 如果有玩家数据，添加到游戏中
      if (message.player) {
        this.addPlayer(message.player.id, message.player);
        this.updatePlayerCount();
      }
    });

    this.room.onMessage('player_left', (message) => {
      this.showMessage(message.message, 0xe74c3c);
    });

    this.room.onMessage('player_ready_changed', (message) => {
      const status = message.isReady ? '已准备' : '取消准备';
      this.showMessage(`${message.username} ${status}`, 0xf39c12);
    });

    this.room.onMessage('all_players_ready', (message) => {
      this.showMessage(message.message, 0x27ae60);
    });

    this.room.onMessage('game_started', (message) => {
      this.showMessage(message.message, 0x27ae60);
      this.gameState = GameState.PLAYING;
    });

    this.room.onMessage('error', (message) => {
      this.showMessage(message.message, 0xe74c3c);
    });

    this.room.onMessage('bots_added', (message) => {
      this.showMessage(message.message, 0x3498db);
      console.log('机器人已添加:', message.bots);
    });

    this.room.onMessage('bots_removed', (message) => {
      this.showMessage(message.message, 0xf39c12);
    });
  }

  private updateGameState(state: any) {
    this.updatePlayerCount();

    const gameStatusText = this.gameUI.list[3] as Phaser.GameObjects.Text;

    switch (state.gameState) {
      case GameState.WAITING:
        gameStatusText.setText('等待玩家准备...');
        gameStatusText.setColor('#e74c3c');
        break;
      case GameState.STARTING:
        gameStatusText.setText('游戏即将开始...');
        gameStatusText.setColor('#f39c12');
        break;
      case GameState.PLAYING:
        gameStatusText.setText('游戏进行中');
        gameStatusText.setColor('#27ae60');
        break;
      case GameState.FINISHED:
        gameStatusText.setText('游戏结束');
        gameStatusText.setColor('#9b59b6');
        break;
    }
  }

  private updatePlayerCount() {
    const playerCountText = this.gameUI.list[2] as Phaser.GameObjects.Text;
    const currentPlayerCount = this.players.size;
    const maxPlayers = 8;
    playerCountText.setText(`玩家: ${currentPlayerCount}/${maxPlayers}`);
  }

  private addPlayer(sessionId: string, player: any) {
    // 检查是否是当前玩家
    const isMyPlayer = sessionId === this.room.sessionId;
    // 检查是否是机器人（通过用户名判断）
    const isBot = player.username.includes('机器人') ||
                  player.username.includes('智能助手') ||
                  player.username.includes('代码骑士') ||
                  player.username.includes('程序守护') ||
                  player.username.includes('算法大师') ||
                  player.username.includes('数字精灵') ||
                  player.username.includes('虚拟战士') ||
                  player.username.includes('自动玩家');

    let color, strokeColor, backgroundColor;
    if (isMyPlayer) {
      color = 0xe74c3c;        // 红色 - 自己
      strokeColor = 0xc0392b;
      backgroundColor = '#e74c3c';
    } else if (isBot) {
      color = 0x9b59b6;        // 紫色 - 机器人
      strokeColor = 0x8e44ad;
      backgroundColor = '#9b59b6';
    } else {
      color = 0x3498db;        // 蓝色 - 其他玩家
      strokeColor = 0x2980b9;
      backgroundColor = '#3498db';
    }

    // 创建玩家精灵
    const playerSprite = this.add.circle(player.x, player.y, 20, color);
    playerSprite.setStrokeStyle(3, strokeColor);

    // 为机器人添加特殊标识
    if (isBot) {
      // 添加一个小的机器人图标
      const botIcon = this.add.text(player.x, player.y, '🤖', {
        fontSize: '16px'
      }).setOrigin(0.5);

      // 将图标也存储起来，以便后续更新位置
      (playerSprite as any).botIcon = botIcon;
    }

    // 创建玩家名称文本
    const displayName = isBot ? `🤖 ${player.username}` : player.username;
    const playerText = this.add.text(player.x, player.y - 35, displayName, {
      fontSize: '12px',
      color: '#ffffff',
      backgroundColor: backgroundColor,
      padding: { x: 4, y: 2 }
    }).setOrigin(0.5);

    this.players.set(sessionId, playerSprite);
    this.playerTexts.set(sessionId, playerText);

    // 如果是当前玩家，保存引用
    if (isMyPlayer) {
      this.myPlayer = playerSprite;
    }

    // 更新玩家计数
    this.updatePlayerCount();

    console.log(`玩家 ${player.username} 已添加，当前玩家数: ${this.players.size}`);
  }

  private removePlayer(sessionId: string) {
    const playerSprite = this.players.get(sessionId);
    const playerText = this.playerTexts.get(sessionId);

    if (playerSprite) {
      // 如果有机器人图标，也要销毁
      if ((playerSprite as any).botIcon) {
        (playerSprite as any).botIcon.destroy();
      }
      playerSprite.destroy();
      this.players.delete(sessionId);
    }

    if (playerText) {
      playerText.destroy();
      this.playerTexts.delete(sessionId);
    }

    // 更新玩家计数
    this.updatePlayerCount();

    console.log(`玩家已移除，当前玩家数: ${this.players.size}`);
  }

  private updatePlayer(sessionId: string, player: any) {
    const playerSprite = this.players.get(sessionId);
    const playerText = this.playerTexts.get(sessionId);
    const isMyPlayer = sessionId === this.room.sessionId;

    if (playerSprite && playerText) {
      // 只更新其他玩家的位置，不更新自己的位置（本地控制）
      if (!isMyPlayer) {
        playerSprite.setPosition(player.x, player.y);
        playerText.setPosition(player.x, player.y - 35);

        // 如果有机器人图标，也要更新位置
        if ((playerSprite as any).botIcon) {
          (playerSprite as any).botIcon.setPosition(player.x, player.y);
        }
      } else {
        // 只更新自己的名称位置
        playerText.setPosition(playerSprite.x, playerSprite.y - 35);
      }

      // 更新颜色（准备状态）
      if (player.isReady) {
        const readyColor = isMyPlayer ? 0x27ae60 : 0x27ae60;
        const readyStroke = isMyPlayer ? 0x229954 : 0x229954;
        playerSprite.setFillStyle(readyColor);
        playerSprite.setStrokeStyle(3, readyStroke);
      } else {
        const normalColor = isMyPlayer ? 0xe74c3c : 0x3498db;
        const normalStroke = isMyPlayer ? 0xc0392b : 0x2980b9;
        playerSprite.setFillStyle(normalColor);
        playerSprite.setStrokeStyle(3, normalStroke);
      }
    }
  }

  private showMessage(text: string, color: number) {
    const message = this.add.text(this.scale.width / 2, 100, text, {
      fontSize: '16px',
      color: `#${color.toString(16)}`,
      backgroundColor: '#000000',
      padding: { x: 10, y: 5 }
    }).setOrigin(0.5);
    
    // 3秒后消失
    this.time.delayedCall(3000, () => {
      message.destroy();
    });
  }

  private handleReady() {
    this.room.send('player_ready');
  }

  private handleLeaveRoom() {
    this.room.leave();
    this.scene.start('LobbyScene');
  }

  private handleAddBots() {
    // 发送添加机器人消息
    this.networkManager.sendRoomMessage('add_bots', {
      count: 3, // 添加3个机器人
      behavior: 'active' // 活跃行为
    });
  }

  private handleRemoveBots() {
    // 发送移除机器人消息
    this.networkManager.sendRoomMessage('remove_bots');
  }
}
