import Phaser from 'phaser';
import { Room } from 'colyseus.js';
import { NetworkManager } from '../network/NetworkManager';
import { GameState, GAME_CONFIG } from '@multiplayer-game/shared';

export class GameScene extends Phaser.Scene {
  private networkManager!: NetworkManager;
  private room!: Room;
  private players: Map<string, Phaser.GameObjects.Sprite> = new Map();
  private playerTexts: Map<string, Phaser.GameObjects.Text> = new Map();
  private gameUI!: Phaser.GameObjects.Container;
  private gameState: GameState = GameState.WAITING;
  private myPlayer!: Phaser.GameObjects.Sprite;
  private cursors!: Phaser.Types.Input.Keyboard.CursorKeys;
  private wasdKeys!: any;

  constructor() {
    super({ key: 'GameScene' });
  }

  init(data: any) {
    this.room = data.room;
  }

  create() {
    this.networkManager = this.registry.get('networkManager');
    
    if (!this.room) {
      console.error('没有房间数据，返回大厅');
      this.scene.start('LobbyScene');
      return;
    }

    this.createBackground();
    this.createGameArea();
    this.createUI();
    this.setupInput();
    this.setupRoomEvents();

    console.log('游戏场景创建完成，房间ID:', this.room.id);
  }

  private createBackground() {
    // 创建游戏背景
    const graphics = this.add.graphics();
    graphics.fillStyle(0x2c3e50);
    graphics.fillRect(0, 0, this.scale.width, this.scale.height);
    
    // 添加网格线
    graphics.lineStyle(1, 0x34495e, 0.5);
    const gridSize = 50;
    
    for (let x = 0; x <= this.scale.width; x += gridSize) {
      graphics.moveTo(x, 0);
      graphics.lineTo(x, this.scale.height);
    }
    
    for (let y = 0; y <= this.scale.height; y += gridSize) {
      graphics.moveTo(0, y);
      graphics.lineTo(this.scale.width, y);
    }
    
    graphics.strokePath();
  }

  private createGameArea() {
    // 创建游戏区域边界
    const gameArea = this.add.rectangle(
      this.scale.width / 2,
      this.scale.height / 2,
      GAME_CONFIG.GAME_WIDTH - 100,
      GAME_CONFIG.GAME_HEIGHT - 150,
      0x000000,
      0
    );
    gameArea.setStrokeStyle(3, 0x3498db);
  }

  private createUI() {
    this.gameUI = this.add.container(0, 0);
    
    // 顶部信息栏
    const topBar = this.add.rectangle(this.scale.width / 2, 30, this.scale.width, 60, 0x2c3e50, 0.9);
    topBar.setStrokeStyle(2, 0x34495e);
    
    // 房间信息
    const roomInfo = this.add.text(20, 20, `房间: ${this.room.id}`, {
      fontSize: '14px',
      color: '#ffffff'
    });
    
    // 玩家数量
    const playerCount = this.add.text(20, 40, '玩家: 0/8', {
      fontSize: '12px',
      color: '#bdc3c7'
    });
    
    // 游戏状态
    const gameStatus = this.add.text(this.scale.width / 2, 30, '等待玩家加入...', {
      fontSize: '16px',
      color: '#e74c3c',
      fontStyle: 'bold'
    }).setOrigin(0.5);
    
    // 控制按钮
    this.createControlButtons();

    // 游戏说明
    this.createInstructions();

    this.gameUI.add([topBar, roomInfo, playerCount, gameStatus]);
  }

  private createControlButtons() {
    const buttonY = this.scale.height - 40;
    
    // 准备按钮
    const readyButton = this.createButton(100, buttonY, '准备', () => {
      this.handleReady();
    });
    
    // 离开房间按钮
    const leaveButton = this.createButton(250, buttonY, '离开房间', () => {
      this.handleLeaveRoom();
    });
    
    this.gameUI.add([readyButton, leaveButton]);
  }

  private createInstructions() {
    const instructions = this.add.text(this.scale.width - 20, 100,
      '游戏说明:\n' +
      '• 使用方向键或WASD移动\n' +
      '• 点击"准备"开始游戏\n' +
      '• 红色圆圈是你的角色\n' +
      '• 蓝色圆圈是其他玩家', {
      fontSize: '12px',
      color: '#bdc3c7',
      backgroundColor: '#2c3e50',
      padding: { x: 10, y: 8 },
      align: 'left'
    }).setOrigin(1, 0);

    this.gameUI.add(instructions);
  }

  private setupInput() {
    // 创建键盘输入
    this.cursors = this.input.keyboard!.createCursorKeys();
    this.wasdKeys = this.input.keyboard!.addKeys('W,S,A,D');
  }

  update() {
    // 只有在游戏进行中才能移动
    if (this.gameState === GameState.PLAYING && this.myPlayer) {
      this.handlePlayerMovement();
    }
  }

  private handlePlayerMovement() {
    const speed = 200;
    let velocityX = 0;
    let velocityY = 0;

    // 检查键盘输入
    if (this.cursors.left.isDown || this.wasdKeys.A.isDown) {
      velocityX = -speed;
    } else if (this.cursors.right.isDown || this.wasdKeys.D.isDown) {
      velocityX = speed;
    }

    if (this.cursors.up.isDown || this.wasdKeys.W.isDown) {
      velocityY = -speed;
    } else if (this.cursors.down.isDown || this.wasdKeys.S.isDown) {
      velocityY = speed;
    }

    // 如果有移动，更新位置并发送到服务器
    if (velocityX !== 0 || velocityY !== 0) {
      const deltaTime = this.game.loop.delta / 1000;
      const newX = this.myPlayer.x + velocityX * deltaTime;
      const newY = this.myPlayer.y + velocityY * deltaTime;

      // 边界检查
      const bounds = this.getGameBounds();
      const clampedX = Phaser.Math.Clamp(newX, bounds.left + 20, bounds.right - 20);
      const clampedY = Phaser.Math.Clamp(newY, bounds.top + 20, bounds.bottom - 20);

      // 更新本地位置
      this.myPlayer.setPosition(clampedX, clampedY);

      // 发送位置到服务器
      this.room.send('player_move', {
        x: clampedX,
        y: clampedY
      });
    }
  }

  private getGameBounds() {
    return {
      left: 50,
      right: this.scale.width - 50,
      top: 100,
      bottom: this.scale.height - 100
    };
  }

  private createButton(x: number, y: number, text: string, callback: () => void): Phaser.GameObjects.Container {
    const button = this.add.container(x, y);
    
    const background = this.add.rectangle(0, 0, 100, 30, 0x3498db);
    background.setStrokeStyle(2, 0x2980b9);
    
    const buttonText = this.add.text(0, 0, text, {
      fontSize: '12px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    button.add([background, buttonText]);
    button.setSize(100, 30);
    button.setInteractive({ useHandCursor: true });

    button.on('pointerdown', callback);
    
    button.on('pointerover', () => {
      background.setFillStyle(0x5dade2);
    });

    button.on('pointerout', () => {
      background.setFillStyle(0x3498db);
    });

    return button;
  }

  private setupRoomEvents() {
    // 监听房间状态变化
    this.room.onStateChange((state) => {
      this.updateGameState(state);
    });

    // 监听玩家加入
    this.room.state.players.onAdd = (player, sessionId) => {
      console.log('玩家加入:', player.username);
      this.addPlayer(sessionId, player);
    };

    // 监听玩家离开
    this.room.state.players.onRemove = (player, sessionId) => {
      console.log('玩家离开:', player.username);
      this.removePlayer(sessionId);
    };

    // 监听玩家状态变化
    this.room.state.players.onChange = (player, sessionId) => {
      this.updatePlayer(sessionId, player);
    };

    // 监听房间消息
    this.room.onMessage('welcome', (message) => {
      console.log('欢迎消息:', message);
      this.showMessage(message.message, 0x27ae60);
    });

    this.room.onMessage('player_joined', (message) => {
      this.showMessage(message.message, 0x3498db);
    });

    this.room.onMessage('player_left', (message) => {
      this.showMessage(message.message, 0xe74c3c);
    });

    this.room.onMessage('player_ready_changed', (message) => {
      const status = message.isReady ? '已准备' : '取消准备';
      this.showMessage(`${message.username} ${status}`, 0xf39c12);
    });

    this.room.onMessage('all_players_ready', (message) => {
      this.showMessage(message.message, 0x27ae60);
    });

    this.room.onMessage('game_started', (message) => {
      this.showMessage(message.message, 0x27ae60);
      this.gameState = GameState.PLAYING;
    });

    this.room.onMessage('error', (message) => {
      this.showMessage(message.message, 0xe74c3c);
    });
  }

  private updateGameState(state: any) {
    // 更新UI显示
    const playerCountText = this.gameUI.list[2] as Phaser.GameObjects.Text;
    const gameStatusText = this.gameUI.list[3] as Phaser.GameObjects.Text;
    
    playerCountText.setText(`玩家: ${state.players.size}/${state.maxPlayers}`);
    
    switch (state.gameState) {
      case GameState.WAITING:
        gameStatusText.setText('等待玩家准备...');
        gameStatusText.setColor('#e74c3c');
        break;
      case GameState.STARTING:
        gameStatusText.setText('游戏即将开始...');
        gameStatusText.setColor('#f39c12');
        break;
      case GameState.PLAYING:
        gameStatusText.setText('游戏进行中');
        gameStatusText.setColor('#27ae60');
        break;
      case GameState.FINISHED:
        gameStatusText.setText('游戏结束');
        gameStatusText.setColor('#9b59b6');
        break;
    }
  }

  private addPlayer(sessionId: string, player: any) {
    // 检查是否是当前玩家
    const isMyPlayer = sessionId === this.room.sessionId;
    const color = isMyPlayer ? 0xe74c3c : 0x3498db;
    const strokeColor = isMyPlayer ? 0xc0392b : 0x2980b9;

    // 创建玩家精灵
    const playerSprite = this.add.circle(player.x, player.y, 20, color);
    playerSprite.setStrokeStyle(3, strokeColor);

    // 创建玩家名称文本
    const playerText = this.add.text(player.x, player.y - 35, player.username, {
      fontSize: '12px',
      color: '#ffffff',
      backgroundColor: isMyPlayer ? '#e74c3c' : '#000000',
      padding: { x: 4, y: 2 }
    }).setOrigin(0.5);

    this.players.set(sessionId, playerSprite);
    this.playerTexts.set(sessionId, playerText);

    // 如果是当前玩家，保存引用
    if (isMyPlayer) {
      this.myPlayer = playerSprite;
    }
  }

  private removePlayer(sessionId: string) {
    const playerSprite = this.players.get(sessionId);
    const playerText = this.playerTexts.get(sessionId);
    
    if (playerSprite) {
      playerSprite.destroy();
      this.players.delete(sessionId);
    }
    
    if (playerText) {
      playerText.destroy();
      this.playerTexts.delete(sessionId);
    }
  }

  private updatePlayer(sessionId: string, player: any) {
    const playerSprite = this.players.get(sessionId);
    const playerText = this.playerTexts.get(sessionId);
    const isMyPlayer = sessionId === this.room.sessionId;

    if (playerSprite && playerText) {
      // 只更新其他玩家的位置，不更新自己的位置（本地控制）
      if (!isMyPlayer) {
        playerSprite.setPosition(player.x, player.y);
        playerText.setPosition(player.x, player.y - 35);
      } else {
        // 只更新自己的名称位置
        playerText.setPosition(playerSprite.x, playerSprite.y - 35);
      }

      // 更新颜色（准备状态）
      if (player.isReady) {
        const readyColor = isMyPlayer ? 0x27ae60 : 0x27ae60;
        const readyStroke = isMyPlayer ? 0x229954 : 0x229954;
        playerSprite.setFillStyle(readyColor);
        playerSprite.setStrokeStyle(3, readyStroke);
      } else {
        const normalColor = isMyPlayer ? 0xe74c3c : 0x3498db;
        const normalStroke = isMyPlayer ? 0xc0392b : 0x2980b9;
        playerSprite.setFillStyle(normalColor);
        playerSprite.setStrokeStyle(3, normalStroke);
      }
    }
  }

  private showMessage(text: string, color: number) {
    const message = this.add.text(this.scale.width / 2, 100, text, {
      fontSize: '16px',
      color: `#${color.toString(16)}`,
      backgroundColor: '#000000',
      padding: { x: 10, y: 5 }
    }).setOrigin(0.5);
    
    // 3秒后消失
    this.time.delayedCall(3000, () => {
      message.destroy();
    });
  }

  private handleReady() {
    this.room.send('player_ready');
  }

  private handleLeaveRoom() {
    this.room.leave();
    this.scene.start('LobbyScene');
  }
}
