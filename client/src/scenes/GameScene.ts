import Phaser from 'phaser';
import { Room } from 'colyseus.js';
import { NetworkManager } from '../network/NetworkManager';
import { GameState, GAME_CONFIG } from '../types/shared';

export class GameScene extends Phaser.Scene {
  private networkManager!: NetworkManager;
  private room!: Room;
  private players: Map<string, Phaser.GameObjects.Sprite> = new Map();
  private playerTexts: Map<string, Phaser.GameObjects.Text> = new Map();
  private gameUI!: Phaser.GameObjects.Container;
  private gameState: GameState = GameState.WAITING;
  private myPlayer!: Phaser.GameObjects.Sprite;
  private cursors!: Phaser.Types.Input.Keyboard.CursorKeys;
  private wasdKeys!: any;
  private updateTimer!: Phaser.Time.TimerEvent;
  private playerCountText!: Phaser.GameObjects.Text;
  private gameStatusText!: Phaser.GameObjects.Text;

  constructor() {
    super({ key: 'GameScene' });
  }

  init(data: any) {
    this.room = data.room;
  }

  create() {
    this.networkManager = this.registry.get('networkManager');
    
    if (!this.room) {
      console.error('没有房间数据，返回大厅');
      this.scene.start('LobbyScene');
      return;
    }

    this.createBackground();
    this.createGameArea();
    this.createUI();
    this.setupInput();
    this.setupRoomEvents();
    this.startUpdateTimer();

    console.log('游戏场景创建完成，房间ID:', this.room.id);
  }

  private createBackground() {
    // 创建游戏背景
    const graphics = this.add.graphics();
    graphics.fillStyle(0x2c3e50);
    graphics.fillRect(0, 0, this.scale.width, this.scale.height);
    
    // 添加网格线
    graphics.lineStyle(1, 0x34495e, 0.5);
    const gridSize = 50;
    
    for (let x = 0; x <= this.scale.width; x += gridSize) {
      graphics.moveTo(x, 0);
      graphics.lineTo(x, this.scale.height);
    }
    
    for (let y = 0; y <= this.scale.height; y += gridSize) {
      graphics.moveTo(0, y);
      graphics.lineTo(this.scale.width, y);
    }
    
    graphics.strokePath();
  }

  private createGameArea() {
    // 创建游戏区域边界
    const gameArea = this.add.rectangle(
      this.scale.width / 2,
      this.scale.height / 2,
      GAME_CONFIG.GAME_WIDTH - 100,
      GAME_CONFIG.GAME_HEIGHT - 150,
      0x000000,
      0
    );
    gameArea.setStrokeStyle(3, 0x3498db);
  }

  private createUI() {
    this.gameUI = this.add.container(0, 0);
    
    // 顶部信息栏
    const topBar = this.add.rectangle(this.scale.width / 2, 30, this.scale.width, 60, 0x2c3e50, 0.9);
    topBar.setStrokeStyle(2, 0x34495e);
    
    // 房间信息
    const roomInfo = this.add.text(20, 20, `房间: ${this.room.name || this.room.id}`, {
      fontSize: '14px',
      color: '#ffffff'
    });
    
    // 玩家数量 - 保存引用
    this.playerCountText = this.add.text(20, 40, '玩家: 1/8', {
      fontSize: '12px',
      color: '#bdc3c7'
    });

    // 游戏状态 - 保存引用
    this.gameStatusText = this.add.text(this.scale.width / 2, 30, '等待玩家加入...', {
      fontSize: '16px',
      color: '#e74c3c',
      fontStyle: 'bold'
    }).setOrigin(0.5);
    
    // 控制按钮
    this.createControlButtons();

    // 游戏说明
    this.createInstructions();

    this.gameUI.add([topBar, roomInfo, this.playerCountText, this.gameStatusText]);
  }

  private createControlButtons() {
    const buttonY = this.scale.height - 40;

    // 准备按钮
    const readyButton = this.createButton(80, buttonY, '准备', () => {
      this.handleReady();
    }, 80, 30);

    // 添加机器人按钮
    const addBotsButton = this.createButton(180, buttonY, '添加机器人', () => {
      this.handleAddBots();
    }, 100, 30);

    // 移除机器人按钮
    const removeBotsButton = this.createButton(300, buttonY, '移除机器人', () => {
      this.handleRemoveBots();
    }, 100, 30);

    // 离开房间按钮
    const leaveButton = this.createButton(420, buttonY, '离开房间', () => {
      this.handleLeaveRoom();
    }, 100, 30);

    this.gameUI.add([readyButton, addBotsButton, removeBotsButton, leaveButton]);
  }

  private createInstructions() {
    const instructions = this.add.text(this.scale.width - 20, 100,
      '游戏说明:\n' +
      '• 使用方向键或WASD移动\n' +
      '• 点击"准备"开始游戏\n' +
      '• 🔴 红色圆圈是你的角色\n' +
      '• 🔵 蓝色圆圈是其他玩家\n' +
      '• 🟣 紫色圆圈是机器人🤖\n' +
      '• 点击"添加机器人"测试多人功能', {
      fontSize: '11px',
      color: '#bdc3c7',
      backgroundColor: '#2c3e50',
      padding: { x: 10, y: 8 },
      align: 'left'
    }).setOrigin(1, 0);

    this.gameUI.add(instructions);
  }

  private setupInput() {
    // 创建键盘输入
    this.cursors = this.input.keyboard!.createCursorKeys();
    this.wasdKeys = this.input.keyboard!.addKeys('W,S,A,D');
  }

  update() {
    // 只有在游戏进行中才能移动
    if (this.gameState === GameState.PLAYING && this.myPlayer) {
      this.handlePlayerMovement();
    }
  }

  private handlePlayerMovement() {
    const speed = 200;
    let velocityX = 0;
    let velocityY = 0;

    // 检查键盘输入
    if (this.cursors.left.isDown || this.wasdKeys.A.isDown) {
      velocityX = -speed;
    } else if (this.cursors.right.isDown || this.wasdKeys.D.isDown) {
      velocityX = speed;
    }

    if (this.cursors.up.isDown || this.wasdKeys.W.isDown) {
      velocityY = -speed;
    } else if (this.cursors.down.isDown || this.wasdKeys.S.isDown) {
      velocityY = speed;
    }

    // 如果有移动，更新位置并发送到服务器
    if (velocityX !== 0 || velocityY !== 0) {
      const deltaTime = this.game.loop.delta / 1000;
      const newX = this.myPlayer.x + velocityX * deltaTime;
      const newY = this.myPlayer.y + velocityY * deltaTime;

      // 边界检查
      const bounds = this.getGameBounds();
      const clampedX = Phaser.Math.Clamp(newX, bounds.left + 20, bounds.right - 20);
      const clampedY = Phaser.Math.Clamp(newY, bounds.top + 20, bounds.bottom - 20);

      // 更新本地位置
      this.myPlayer.setPosition(clampedX, clampedY);

      // 发送位置到服务器
      this.room.send('player_move', {
        x: clampedX,
        y: clampedY
      });
    }
  }

  private getGameBounds() {
    return {
      left: 50,
      right: this.scale.width - 50,
      top: 100,
      bottom: this.scale.height - 100
    };
  }

  private createButton(x: number, y: number, text: string, callback: () => void): Phaser.GameObjects.Container {
    const button = this.add.container(x, y);
    
    const background = this.add.rectangle(0, 0, 100, 30, 0x3498db);
    background.setStrokeStyle(2, 0x2980b9);
    
    const buttonText = this.add.text(0, 0, text, {
      fontSize: '12px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    button.add([background, buttonText]);
    button.setSize(100, 30);
    button.setInteractive({ useHandCursor: true });

    button.on('pointerdown', callback);
    
    button.on('pointerover', () => {
      background.setFillStyle(0x5dade2);
    });

    button.on('pointerout', () => {
      background.setFillStyle(0x3498db);
    });

    return button;
  }

  private setupRoomEvents() {
    // 监听房间状态变化
    this.room.onStateChange((state) => {
      console.log('房间状态更新:', state);
      this.updateGameState(state);
    });

    // 对于模拟房间，我们需要手动添加当前玩家
    if (this.room.sessionId) {
      setTimeout(() => {
        this.addPlayer(this.room.sessionId, {
          id: this.room.sessionId,
          username: this.networkManager.getCurrentUser()?.username || '玩家',
          isReady: false,
          x: Math.random() * (this.scale.width - 200) + 100,
          y: Math.random() * (this.scale.height - 200) + 100
        });

        // 手动触发一次状态更新以确保UI正确显示
        this.updatePlayerCount();
      }, 200);
    }

    // 监听房间消息
    this.room.onMessage('welcome', (message) => {
      console.log('欢迎消息:', message);
      this.showMessage(message.message, 0x27ae60);
    });

    this.room.onMessage('player_joined', (message) => {
      this.showMessage(message.message, 0x3498db);
      console.log('收到玩家加入消息:', message);

      // 如果有玩家数据，添加到游戏中
      if (message.player) {
        console.log(`准备添加玩家: ${message.player.username} (ID: ${message.player.id})`);
        this.addPlayer(message.player.id, message.player);

        // 延迟更新以确保所有机器人都添加完成
        setTimeout(() => {
          this.updatePlayerCount();
        }, 100);
      }
    });

    this.room.onMessage('player_left', (message) => {
      this.showMessage(message.message, 0xe74c3c);
    });

    this.room.onMessage('player_ready_changed', (message) => {
      const status = message.isReady ? '已准备' : '取消准备';
      this.showMessage(`${message.username} ${status}`, 0xf39c12);
    });

    this.room.onMessage('all_players_ready', (message) => {
      this.showMessage(message.message, 0x27ae60);
    });

    this.room.onMessage('game_started', (message) => {
      this.showMessage(message.message, 0x27ae60);
      this.gameState = GameState.PLAYING;

      // 切换到战略地图场景
      setTimeout(() => {
        console.log('🗺️ 启动真实游戏');
        this.startRealGame();
      }, 2000);
    });

    this.room.onMessage('error', (message) => {
      this.showMessage(message.message, 0xe74c3c);
    });

    this.room.onMessage('bots_added', (message) => {
      this.showMessage(message.message, 0x3498db);
      console.log('机器人已添加:', message.bots);

      // 延迟更新玩家计数，等待所有机器人都加入完成
      const botCount = message.bots ? message.bots.length : 3;
      setTimeout(() => {
        this.updatePlayerCount();
        console.log(`等待 ${botCount} 个机器人加入完成，当前玩家数: ${this.players.size}`);
      }, 200 * botCount + 300);
    });

    this.room.onMessage('bots_removed', (message) => {
      this.showMessage(message.message, 0xf39c12);
    });
  }

  private updateGameState(state: any) {
    // 更新玩家计数 - 使用直接引用
    if (this.playerCountText) {
      const playerCount = state.playerCount || state.players?.size || this.players.size;
      const maxPlayers = state.maxPlayers || 8;
      this.playerCountText.setText(`玩家: ${playerCount}/${maxPlayers}`);
    }

    if (this.gameStatusText) {
      switch (state.gameState) {
        case GameState.WAITING:
          this.gameStatusText.setText('等待玩家准备...');
          this.gameStatusText.setColor('#e74c3c');
          break;
        case GameState.STARTING:
          this.gameStatusText.setText('游戏即将开始...');
          this.gameStatusText.setColor('#f39c12');
          break;
        case GameState.PLAYING:
          this.gameStatusText.setText('游戏进行中');
          this.gameStatusText.setColor('#27ae60');
          break;
        case GameState.FINISHED:
          this.gameStatusText.setText('游戏结束');
          this.gameStatusText.setColor('#9b59b6');
          break;
      }
    }
  }

  private updatePlayerCount() {
    try {
      if (this.playerCountText) {
        const currentPlayerCount = this.players.size;
        const maxPlayers = 8;
        const newText = `玩家: ${currentPlayerCount}/${maxPlayers}`;

        // 只有当文本真的改变时才更新
        if (this.playerCountText.text !== newText) {
          this.playerCountText.setText(newText);
          console.log('更新玩家计数:', newText);
        }
      }
    } catch (error) {
      console.error('更新玩家计数失败:', error);
    }
  }

  private addPlayer(sessionId: string, player: any) {
    console.log(`开始添加玩家: ${player.username}, ID: ${sessionId}, 当前玩家数: ${this.players.size}`);

    // 检查玩家是否已经存在
    if (this.players.has(sessionId)) {
      console.log(`玩家 ${player.username} 已存在，跳过添加`);
      return;
    }

    // 检查是否是当前玩家
    const isMyPlayer = sessionId === this.room.sessionId;
    // 检查是否是机器人（通过用户名判断）
    const isBot = player.username.includes('机器人') ||
                  player.username.includes('智能助手') ||
                  player.username.includes('代码骑士') ||
                  player.username.includes('程序守护') ||
                  player.username.includes('算法大师') ||
                  player.username.includes('数字精灵') ||
                  player.username.includes('虚拟战士') ||
                  player.username.includes('自动玩家');

    let color, strokeColor, backgroundColor;
    if (isMyPlayer) {
      color = 0xe74c3c;        // 红色 - 自己
      strokeColor = 0xc0392b;
      backgroundColor = '#e74c3c';
    } else if (isBot) {
      color = 0x9b59b6;        // 紫色 - 机器人
      strokeColor = 0x8e44ad;
      backgroundColor = '#9b59b6';
    } else {
      color = 0x3498db;        // 蓝色 - 其他玩家
      strokeColor = 0x2980b9;
      backgroundColor = '#3498db';
    }

    // 创建玩家精灵
    const playerSprite = this.add.circle(player.x, player.y, 20, color);
    playerSprite.setStrokeStyle(3, strokeColor);

    // 为机器人添加特殊标识
    if (isBot) {
      // 添加一个小的机器人图标
      const botIcon = this.add.text(player.x, player.y, '🤖', {
        fontSize: '16px'
      }).setOrigin(0.5);

      // 将图标也存储起来，以便后续更新位置
      (playerSprite as any).botIcon = botIcon;
    }

    // 创建玩家名称文本
    const displayName = isBot ? `🤖 ${player.username}` : player.username;
    const playerText = this.add.text(player.x, player.y - 35, displayName, {
      fontSize: '12px',
      color: '#ffffff',
      backgroundColor: backgroundColor,
      padding: { x: 4, y: 2 }
    }).setOrigin(0.5);

    this.players.set(sessionId, playerSprite);
    this.playerTexts.set(sessionId, playerText);

    // 如果是当前玩家，保存引用
    if (isMyPlayer) {
      this.myPlayer = playerSprite;
    }

    // 更新玩家计数
    this.updatePlayerCount();

    console.log(`玩家 ${player.username} 已添加，当前玩家数: ${this.players.size}`);
  }

  private removePlayer(sessionId: string) {
    const playerSprite = this.players.get(sessionId);
    const playerText = this.playerTexts.get(sessionId);

    if (playerSprite) {
      // 如果有机器人图标，也要销毁
      if ((playerSprite as any).botIcon) {
        (playerSprite as any).botIcon.destroy();
      }
      playerSprite.destroy();
      this.players.delete(sessionId);
    }

    if (playerText) {
      playerText.destroy();
      this.playerTexts.delete(sessionId);
    }

    // 更新玩家计数
    this.updatePlayerCount();

    console.log(`玩家已移除，当前玩家数: ${this.players.size}`);
  }

  private updatePlayer(sessionId: string, player: any) {
    const playerSprite = this.players.get(sessionId);
    const playerText = this.playerTexts.get(sessionId);
    const isMyPlayer = sessionId === this.room.sessionId;

    if (playerSprite && playerText) {
      // 只更新其他玩家的位置，不更新自己的位置（本地控制）
      if (!isMyPlayer) {
        playerSprite.setPosition(player.x, player.y);
        playerText.setPosition(player.x, player.y - 35);

        // 如果有机器人图标，也要更新位置
        if ((playerSprite as any).botIcon) {
          (playerSprite as any).botIcon.setPosition(player.x, player.y);
        }
      } else {
        // 只更新自己的名称位置
        playerText.setPosition(playerSprite.x, playerSprite.y - 35);
      }

      // 更新颜色（准备状态）
      if (player.isReady) {
        const readyColor = isMyPlayer ? 0x27ae60 : 0x27ae60;
        const readyStroke = isMyPlayer ? 0x229954 : 0x229954;
        playerSprite.setFillStyle(readyColor);
        playerSprite.setStrokeStyle(3, readyStroke);
      } else {
        const normalColor = isMyPlayer ? 0xe74c3c : 0x3498db;
        const normalStroke = isMyPlayer ? 0xc0392b : 0x2980b9;
        playerSprite.setFillStyle(normalColor);
        playerSprite.setStrokeStyle(3, normalStroke);
      }
    }
  }

  private showMessage(text: string, color: number) {
    const message = this.add.text(this.scale.width / 2, 100, text, {
      fontSize: '16px',
      color: `#${color.toString(16)}`,
      backgroundColor: '#000000',
      padding: { x: 10, y: 5 }
    }).setOrigin(0.5);
    
    // 3秒后消失
    this.time.delayedCall(3000, () => {
      message.destroy();
    });
  }

  private handleReady() {
    this.room.send('player_ready');

    // 临时：直接触发游戏开始（用于演示）
    setTimeout(() => {
      console.log('🎮 模拟游戏开始');
      this.showMessage('游戏开始！进入战略地图...', 0x27ae60);

      setTimeout(() => {
        console.log('🗺️ 启动真实游戏');
        this.startRealGame();
      }, 2000);
    }, 1000);
  }

  private handleLeaveRoom() {
    this.room.leave();
    this.scene.start('LobbyScene');
  }

  private handleAddBots() {
    // 发送添加机器人消息
    this.networkManager.sendRoomMessage('add_bots', {
      count: 3, // 添加3个机器人
      behavior: 'active' // 活跃行为
    });
  }

  private handleRemoveBots() {
    // 发送移除机器人消息
    this.networkManager.sendRoomMessage('remove_bots');
  }

  private startUpdateTimer() {
    // 每秒更新一次玩家计数，确保UI正确显示
    this.updateTimer = this.time.addEvent({
      delay: 1000,
      callback: () => {
        this.updatePlayerCount();
      },
      loop: true
    });
  }

  destroy() {
    // 清理定时器
    if (this.updateTimer) {
      this.updateTimer.destroy();
    }
    super.destroy();
  }

  /**
   * 创建简单的测试界面
   */
  private createTestInterface(): void {
    console.log('🎮 创建游戏测试界面');

    // 清除现有内容
    this.children.removeAll();

    // 创建新的背景
    this.add.rectangle(this.scale.width / 2, this.scale.height / 2, this.scale.width, this.scale.height, 0x27ae60);

    // 成功标题
    const title = this.add.text(this.scale.width / 2, 150, '🎉 游戏启动成功！', {
      fontSize: '48px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    // 说明文字
    const description = this.add.text(this.scale.width / 2, 250,
      '恭喜！三国SLG+自走棋游戏核心系统运行正常\n\n' +
      '✅ 网络连接正常\n' +
      '✅ 多人房间系统正常\n' +
      '✅ 机器人系统正常\n' +
      '✅ 游戏状态管理正常\n' +
      '✅ 场景系统正常', {
      fontSize: '20px',
      color: '#ffffff',
      align: 'center',
      lineSpacing: 10
    }).setOrigin(0.5);

    // 功能演示按钮
    this.createDemoButtons();

    // 显示技术信息
    this.showTechInfo();
  }

  /**
   * 创建演示按钮
   */
  private createDemoButtons(): void {
    const buttonY = this.scale.height - 150;

    // 战略地图演示
    this.createDemoButton(this.scale.width / 2 - 200, buttonY, '战略地图演示', () => {
      this.showStrategicMapDemo();
    });

    // 自走棋演示
    this.createDemoButton(this.scale.width / 2, buttonY, '自走棋演示', () => {
      this.showAutoBattleDemo();
    });

    // 真实游戏
    this.createDemoButton(this.scale.width / 2 + 100, buttonY, '真实游戏', () => {
      this.scene.start('RealStrategicMapScene', {
        networkManager: this.networkManager,
        playerId: 'player1'
      });
    });

    // 返回大厅
    this.createDemoButton(this.scale.width / 2 + 250, buttonY, '返回大厅', () => {
      this.scene.start('LobbyScene', { networkManager: this.networkManager });
    });
  }

  /**
   * 创建演示按钮
   */
  private createDemoButton(x: number, y: number, text: string, callback: () => void): void {
    const button = this.add.container(x, y);

    const background = this.add.rectangle(0, 0, 160, 50, 0x3498db);
    background.setStrokeStyle(3, 0x2980b9);
    background.setInteractive({ useHandCursor: true });

    const buttonText = this.add.text(0, 0, text, {
      fontSize: '16px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    background.on('pointerdown', callback);

    background.on('pointerover', () => {
      background.setFillStyle(0x5dade2);
    });

    background.on('pointerout', () => {
      background.setFillStyle(0x3498db);
    });

    button.add([background, buttonText]);
  }

  /**
   * 显示战略地图演示
   */
  private showStrategicMapDemo(): void {
    console.log('🗺️ 战略地图演示');

    // 清除内容
    this.children.removeAll();

    // 创建战略地图背景
    this.add.rectangle(this.scale.width / 2, this.scale.height / 2, this.scale.width, this.scale.height, 0x2c3e50);

    // 标题
    this.add.text(this.scale.width / 2, 80, '🗺️ 战略地图演示', {
      fontSize: '32px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    // 创建简单的六边形网格
    this.createSimpleHexGrid();

    // 显示游戏信息
    this.add.text(20, 20, '💰 金币: 100  👥 人口: 50  ⭐ 经验: 25', {
      fontSize: '16px',
      color: '#f1c40f',
      fontStyle: 'bold'
    });

    this.add.text(20, 50, '🔄 第1回合 - 资源收集阶段', {
      fontSize: '16px',
      color: '#3498db'
    });

    this.add.text(this.scale.width - 20, 20, '🌤️ 天气: 晴天', {
      fontSize: '16px',
      color: '#27ae60'
    }).setOrigin(1, 0);

    // 返回按钮
    this.createDemoButton(this.scale.width / 2, this.scale.height - 50, '返回主界面', () => {
      this.createTestInterface();
    });
  }

  /**
   * 显示自走棋演示
   */
  private showAutoBattleDemo(): void {
    console.log('⚔️ 自走棋演示');

    // 清除内容
    this.children.removeAll();

    // 创建战斗背景
    this.add.rectangle(this.scale.width / 2, this.scale.height / 2, this.scale.width, this.scale.height, 0x1a1a2e);

    // 标题
    this.add.text(this.scale.width / 2, 80, '⚔️ 自走棋战斗演示', {
      fontSize: '32px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    // 创建简单的战斗棋盘
    this.createSimpleBattleBoard();

    // 显示卡牌
    this.showSampleCards();

    // 返回按钮
    this.createDemoButton(this.scale.width / 2, this.scale.height - 50, '返回主界面', () => {
      this.createTestInterface();
    });
  }

  /**
   * 创建简单六边形网格
   */
  private createSimpleHexGrid(): void {
    const centerX = this.scale.width / 2;
    const centerY = this.scale.height / 2 + 50;
    const hexSize = 25;

    for (let row = 0; row < 3; row++) {
      for (let col = 0; col < 5; col++) {
        const x = centerX + (col - 2) * hexSize * 1.5;
        const y = centerY + (row - 1) * hexSize * Math.sqrt(3) + (col % 2) * hexSize * Math.sqrt(3) / 2;

        const hex = this.add.graphics();
        hex.lineStyle(2, 0x3498db);
        hex.fillStyle(0x34495e, 0.7);

        hex.beginPath();
        for (let i = 0; i < 6; i++) {
          const angle = (i * 60) * Math.PI / 180;
          const hexX = x + hexSize * Math.cos(angle);
          const hexY = y + hexSize * Math.sin(angle);

          if (i === 0) {
            hex.moveTo(hexX, hexY);
          } else {
            hex.lineTo(hexX, hexY);
          }
        }
        hex.closePath();
        hex.fillPath();
        hex.strokePath();

        const terrainIcons = ['🏔️', '🌲', '🌾', '💎', '🏰'];
        const icon = terrainIcons[(row * 5 + col) % terrainIcons.length];
        this.add.text(x, y, icon, { fontSize: '14px' }).setOrigin(0.5);
      }
    }
  }

  /**
   * 创建简单战斗棋盘
   */
  private createSimpleBattleBoard(): void {
    const boardX = this.scale.width / 2;
    const boardY = this.scale.height / 2;
    const cellSize = 30;
    const boardWidth = 8;
    const boardHeight = 4;

    for (let x = 0; x < boardWidth; x++) {
      for (let y = 0; y < boardHeight; y++) {
        const cellX = boardX - (boardWidth * cellSize) / 2 + x * cellSize + cellSize / 2;
        const cellY = boardY - (boardHeight * cellSize) / 2 + y * cellSize + cellSize / 2;

        let cellColor = 0x34495e;
        if (x < 2) {
          cellColor = 0x27ae60; // 我方区域
        } else if (x >= 6) {
          cellColor = 0xe74c3c; // 敌方区域
        }

        const cell = this.add.rectangle(cellX, cellY, cellSize - 2, cellSize - 2, cellColor, 0.5);
        cell.setStrokeStyle(1, 0x7f8c8d);
      }
    }
  }

  /**
   * 显示示例卡牌
   */
  private showSampleCards(): void {
    const cardY = this.scale.height - 120;
    const cards = ['刘备', '关羽', '张飞'];

    cards.forEach((name, index) => {
      const cardX = this.scale.width / 2 - 100 + index * 100;

      const cardBg = this.add.rectangle(cardX, cardY, 70, 90, 0xf39c12);
      cardBg.setStrokeStyle(2, 0xffffff);

      this.add.text(cardX, cardY - 10, name, {
        fontSize: '12px',
        color: '#ffffff',
        fontStyle: 'bold'
      }).setOrigin(0.5);

      this.add.text(cardX, cardY + 20, '传说', {
        fontSize: '10px',
        color: '#ecf0f1'
      }).setOrigin(0.5);
    });
  }

  /**
   * 显示技术信息
   */
  private showTechInfo(): void {
    this.add.text(this.scale.width - 20, this.scale.height - 100,
      '🔧 技术栈:\n' +
      '• TypeScript + Phaser 3\n' +
      '• Colyseus 多人网络\n' +
      '• Vite 构建工具\n' +
      '• 模块化架构设计', {
      fontSize: '14px',
      color: '#ecf0f1',
      lineSpacing: 5
    }).setOrigin(1, 1);
  }

  /**
   * 启动真实游戏
   */
  private startRealGame(): void {
    console.log('🎮 启动真实SLG+自走棋游戏');

    // 清除现有内容
    this.children.removeAll();

    // 创建简化的测试界面先确认基本功能
    this.createSimpleTestGame();
  }

  /**
   * 创建简化的测试游戏
   */
  private createSimpleTestGame(): void {
    console.log('🧪 创建简化测试游戏');

    // 创建背景
    const bg = this.add.rectangle(this.scale.width / 2, this.scale.height / 2, this.scale.width, this.scale.height, 0x2c3e50);
    console.log('✅ 背景创建成功');

    // 标题
    const title = this.add.text(this.scale.width / 2, 100, '🎮 真实游戏测试', {
      fontSize: '32px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);
    console.log('✅ 标题创建成功');

    // 创建一个简单的可点击区域
    const testButton = this.add.rectangle(this.scale.width / 2, 200, 200, 60, 0x3498db);
    testButton.setStrokeStyle(3, 0x2980b9);
    testButton.setInteractive({ useHandCursor: true });

    const buttonText = this.add.text(this.scale.width / 2, 200, '点击测试', {
      fontSize: '20px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    console.log('✅ 测试按钮创建成功');

    // 添加点击事件
    testButton.on('pointerdown', () => {
      console.log('🖱️ 按钮被点击！');
      this.showMessage('按钮点击成功！', 0x27ae60);

      // 点击后创建真实地图
      setTimeout(() => {
        this.createRealStrategicMap();
      }, 1000);
    });

    testButton.on('pointerover', () => {
      testButton.setFillStyle(0x5dade2);
    });

    testButton.on('pointerout', () => {
      testButton.setFillStyle(0x3498db);
    });

    // 说明文字
    const instruction = this.add.text(this.scale.width / 2, 300,
      '如果你能看到这个界面并且按钮可以点击\n说明基本功能正常\n点击按钮进入真实游戏', {
      fontSize: '16px',
      color: '#ecf0f1',
      align: 'center',
      lineSpacing: 5
    }).setOrigin(0.5);

    // 返回按钮
    const backButton = this.add.rectangle(100, this.scale.height - 50, 120, 40, 0xe74c3c);
    backButton.setStrokeStyle(2, 0xc0392b);
    backButton.setInteractive({ useHandCursor: true });

    const backText = this.add.text(100, this.scale.height - 50, '返回大厅', {
      fontSize: '14px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    backButton.on('pointerdown', () => {
      console.log('🏠 返回大厅');
      this.scene.start('LobbyScene', { networkManager: this.networkManager });
    });

    backButton.on('pointerover', () => {
      backButton.setFillStyle(0xec7063);
    });

    backButton.on('pointerout', () => {
      backButton.setFillStyle(0xe74c3c);
    });

    console.log('✅ 简化测试游戏创建完成');
  }

  /**
   * 创建真实的战略地图
   */
  private createRealStrategicMap(): void {
    // 创建背景
    this.add.rectangle(this.scale.width / 2, this.scale.height / 2, this.scale.width, this.scale.height, 0x2c3e50);

    // 标题
    this.add.text(this.scale.width / 2, 50, '🗺️ 三国战略地图', {
      fontSize: '28px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    // 创建真实的地图网格
    this.createRealMapGrid();

    // 创建游戏UI
    this.createRealGameUI();

    // 显示游戏说明
    this.showGameInstructions();
  }

  /**
   * 创建真实地图网格
   */
  private createRealMapGrid(): void {
    const mapWidth = 10;
    const mapHeight = 6;
    const tileSize = 50;
    const startX = (this.scale.width - mapWidth * tileSize) / 2;
    const startY = 120;

    // 地形类型和颜色
    const terrains = [
      { name: '平原', color: 0x90EE90, icon: '🌾' },
      { name: '山脉', color: 0x8B4513, icon: '🏔️' },
      { name: '森林', color: 0x228B22, icon: '🌲' },
      { name: '河流', color: 0x4169E1, icon: '🌊' }
    ];

    // 创建地图数据
    const mapData: any[][] = [];
    for (let x = 0; x < mapWidth; x++) {
      mapData[x] = [];
      for (let y = 0; y < mapHeight; y++) {
        const terrain = terrains[Math.floor(Math.random() * terrains.length)];
        mapData[x][y] = {
          x, y, terrain,
          owner: null,
          army: null
        };
      }
    }

    // 放置初始军队
    mapData[1][2] = { ...mapData[1][2], army: { name: '刘备军', owner: 'player1', strength: 3 }, owner: 'player1' };
    mapData[8][3] = { ...mapData[8][3], army: { name: '曹操军', owner: 'player2', strength: 3 }, owner: 'player2' };

    // 渲染地图
    for (let x = 0; x < mapWidth; x++) {
      for (let y = 0; y < mapHeight; y++) {
        const tile = mapData[x][y];
        const tileX = startX + x * tileSize;
        const tileY = startY + y * tileSize;

        // 创建地块
        const tileRect = this.add.rectangle(
          tileX + tileSize / 2,
          tileY + tileSize / 2,
          tileSize - 2,
          tileSize - 2,
          tile.terrain.color
        );

        // 边框颜色
        if (tile.owner === 'player1') {
          tileRect.setStrokeStyle(3, 0x27ae60); // 绿色
        } else if (tile.owner === 'player2') {
          tileRect.setStrokeStyle(3, 0xe74c3c); // 红色
        } else {
          tileRect.setStrokeStyle(1, 0x666666);
        }

        // 地形图标
        this.add.text(
          tileX + tileSize / 2,
          tileY + tileSize / 2 - 8,
          tile.terrain.icon,
          { fontSize: '16px' }
        ).setOrigin(0.5);

        // 军队图标
        if (tile.army) {
          const armyIcon = this.add.text(
            tileX + tileSize / 2,
            tileY + tileSize / 2 + 8,
            '⚔️',
            { fontSize: '18px' }
          ).setOrigin(0.5);

          // 军队强度
          this.add.text(
            tileX + tileSize - 5,
            tileY + 5,
            tile.army.strength.toString(),
            { fontSize: '12px', color: '#ffffff', fontStyle: 'bold' }
          ).setOrigin(1, 0);

          // 点击事件
          armyIcon.setInteractive({ useHandCursor: true });
          armyIcon.on('pointerdown', () => {
            if (tile.army.owner === 'player1') {
              this.selectArmy(tile);
            }
          });
        }

        // 地块点击事件
        tileRect.setInteractive();
        tileRect.on('pointerdown', () => {
          this.onMapTileClick(tile, mapData);
        });
      }
    }

    // 存储地图数据供后续使用
    (this as any).mapData = mapData;
    (this as any).selectedTile = null;
  }

  /**
   * 选择军队
   */
  private selectArmy(tile: any): void {
    (this as any).selectedTile = tile;
    this.showMessage(`选择了 ${tile.army.name}`, 0x3498db);
  }

  /**
   * 处理地图点击
   */
  private onMapTileClick(targetTile: any, mapData: any[][]): void {
    const selectedTile = (this as any).selectedTile;

    if (!selectedTile || !selectedTile.army) {
      return;
    }

    // 检查是否相邻
    const distance = Math.abs(selectedTile.x - targetTile.x) + Math.abs(selectedTile.y - targetTile.y);
    if (distance !== 1) {
      this.showMessage('只能移动到相邻地块！', 0xe74c3c);
      return;
    }

    // 检查是否有敌军
    if (targetTile.army && targetTile.army.owner !== selectedTile.army.owner) {
      // 触发战斗！
      this.triggerRealCombat(selectedTile, targetTile);
      return;
    }

    // 移动军队
    this.moveArmy(selectedTile, targetTile, mapData);
  }

  /**
   * 移动军队
   */
  private moveArmy(fromTile: any, toTile: any, mapData: any[][]): void {
    // 移动军队
    toTile.army = fromTile.army;
    toTile.owner = fromTile.owner;
    fromTile.army = null;

    // 取消选择
    (this as any).selectedTile = null;

    this.showMessage(`${toTile.army.name} 移动成功！`, 0x27ae60);

    // 重新渲染地图
    this.children.removeAll();
    this.createRealStrategicMap();
  }

  /**
   * 触发真实战斗
   */
  private triggerRealCombat(attackerTile: any, defenderTile: any): void {
    this.showMessage('⚔️ 战斗开始！进入自走棋模式...', 0xe74c3c);

    // 2秒后进入战斗
    this.time.delayedCall(2000, () => {
      this.startRealCombat(attackerTile, defenderTile);
    });
  }

  /**
   * 开始真实战斗
   */
  private startRealCombat(attackerTile: any, defenderTile: any): void {
    console.log('⚔️ 开始真实自走棋战斗');

    // 清除内容
    this.children.removeAll();

    // 创建战斗界面
    this.createRealCombatInterface(attackerTile, defenderTile);
  }

  /**
   * 创建真实战斗界面
   */
  private createRealCombatInterface(attackerTile: any, defenderTile: any): void {
    // 战斗背景
    this.add.rectangle(this.scale.width / 2, this.scale.height / 2, this.scale.width, this.scale.height, 0x1a1a2e);

    // 标题
    this.add.text(this.scale.width / 2, 50, '⚔️ 自走棋战斗', {
      fontSize: '28px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    // 对战信息
    this.add.text(this.scale.width / 2, 90,
      `${attackerTile.army.name} VS ${defenderTile.army.name}`, {
      fontSize: '18px',
      color: '#f39c12'
    }).setOrigin(0.5);

    // 创建战斗棋盘
    this.createRealCombatBoard();

    // 显示卡牌
    this.showCombatCards(attackerTile, defenderTile);

    // 开始自动战斗
    this.startAutoCombat(attackerTile, defenderTile);
  }

  /**
   * 创建战斗棋盘
   */
  private createRealCombatBoard(): void {
    const boardWidth = 8;
    const boardHeight = 4;
    const cellSize = 50;
    const startX = (this.scale.width - boardWidth * cellSize) / 2;
    const startY = 150;

    // 棋盘背景
    const boardBg = this.add.rectangle(
      this.scale.width / 2,
      startY + (boardHeight * cellSize) / 2,
      boardWidth * cellSize + 10,
      boardHeight * cellSize + 10,
      0x2c3e50,
      0.8
    );
    boardBg.setStrokeStyle(3, 0x3498db);

    // 绘制格子
    for (let x = 0; x < boardWidth; x++) {
      for (let y = 0; y < boardHeight; y++) {
        const cellX = startX + x * cellSize;
        const cellY = startY + y * cellSize;

        let cellColor = 0x34495e;
        if (x < 2) {
          cellColor = 0x27ae60; // 攻击方区域
        } else if (x >= 6) {
          cellColor = 0xe74c3c; // 防守方区域
        }

        const cell = this.add.rectangle(
          cellX + cellSize / 2,
          cellY + cellSize / 2,
          cellSize - 2,
          cellSize - 2,
          cellColor,
          0.5
        );
        cell.setStrokeStyle(1, 0x7f8c8d);
      }
    }

    // 放置战斗单位
    this.placeCombatUnits(startX, startY, cellSize);
  }

  /**
   * 放置战斗单位
   */
  private placeCombatUnits(startX: number, startY: number, cellSize: number): void {
    // 攻击方单位
    const attackerUnits = ['刘', '关', '张'];
    attackerUnits.forEach((unit, index) => {
      const x = startX + cellSize / 2;
      const y = startY + (index + 0.5) * cellSize;

      const unitCircle = this.add.circle(x, y, 20, 0x27ae60);
      unitCircle.setStrokeStyle(2, 0xffffff);

      this.add.text(x, y, unit, {
        fontSize: '16px',
        color: '#ffffff',
        fontStyle: 'bold'
      }).setOrigin(0.5);
    });

    // 防守方单位
    const defenderUnits = ['曹', '夏', '许'];
    defenderUnits.forEach((unit, index) => {
      const x = startX + 7 * cellSize + cellSize / 2;
      const y = startY + (index + 0.5) * cellSize;

      const unitCircle = this.add.circle(x, y, 20, 0xe74c3c);
      unitCircle.setStrokeStyle(2, 0xffffff);

      this.add.text(x, y, unit, {
        fontSize: '16px',
        color: '#ffffff',
        fontStyle: 'bold'
      }).setOrigin(0.5);
    });
  }

  /**
   * 显示战斗卡牌
   */
  private showCombatCards(attackerTile: any, defenderTile: any): void {
    const cardY = this.scale.height - 80;

    // 攻击方卡牌
    const attackerCards = ['刘备', '关羽', '张飞'];
    attackerCards.forEach((card, index) => {
      const cardX = 100 + index * 100;

      const cardBg = this.add.rectangle(cardX, cardY, 70, 80, 0x27ae60);
      cardBg.setStrokeStyle(2, 0xffffff);

      this.add.text(cardX, cardY, card, {
        fontSize: '12px',
        color: '#ffffff',
        fontStyle: 'bold'
      }).setOrigin(0.5);
    });

    // 防守方卡牌
    const defenderCards = ['曹操', '夏侯惇', '许褚'];
    defenderCards.forEach((card, index) => {
      const cardX = this.scale.width - 100 - index * 100;

      const cardBg = this.add.rectangle(cardX, cardY, 70, 80, 0xe74c3c);
      cardBg.setStrokeStyle(2, 0xffffff);

      this.add.text(cardX, cardY, card, {
        fontSize: '12px',
        color: '#ffffff',
        fontStyle: 'bold'
      }).setOrigin(0.5);
    });
  }

  /**
   * 开始自动战斗
   */
  private startAutoCombat(attackerTile: any, defenderTile: any): void {
    // 显示战斗进行中
    const battleText = this.add.text(this.scale.width / 2, this.scale.height / 2, '⚔️ 激烈战斗中...', {
      fontSize: '24px',
      color: '#f39c12',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    // 3秒后显示结果
    this.time.delayedCall(3000, () => {
      battleText.destroy();
      this.showCombatResult(attackerTile, defenderTile);
    });
  }

  /**
   * 显示战斗结果
   */
  private showCombatResult(attackerTile: any, defenderTile: any): void {
    // 随机决定胜负
    const attackerWins = Math.random() > 0.5;
    const winner = attackerWins ? attackerTile.army.name : defenderTile.army.name;
    const resultColor = attackerWins ? '#27ae60' : '#e74c3c';

    const resultText = this.add.text(this.scale.width / 2, this.scale.height / 2,
      `🏆 ${winner} 获胜！`, {
      fontSize: '32px',
      color: resultColor,
      fontStyle: 'bold'
    }).setOrigin(0.5);

    // 返回按钮
    const returnButton = this.createDemoButton(this.scale.width / 2, this.scale.height / 2 + 100, '返回战略地图', () => {
      // 更新地图状态
      if (attackerWins) {
        defenderTile.army = attackerTile.army;
        defenderTile.owner = attackerTile.owner;
        attackerTile.army = null;
      } else {
        attackerTile.army = null;
      }

      // 返回战略地图
      this.children.removeAll();
      this.createRealStrategicMap();
    });

    // 3秒后自动返回
    this.time.delayedCall(5000, () => {
      if (attackerWins) {
        defenderTile.army = attackerTile.army;
        defenderTile.owner = attackerTile.owner;
        attackerTile.army = null;
      } else {
        attackerTile.army = null;
      }

      this.children.removeAll();
      this.createRealStrategicMap();
    });
  }

  /**
   * 创建真实游戏UI
   */
  private createRealGameUI(): void {
    // 资源信息
    this.add.text(20, 20, '💰 金币: 100  👥 人口: 50  🏰 领土: 3', {
      fontSize: '16px',
      color: '#f1c40f',
      fontStyle: 'bold'
    });

    // 回合信息
    this.add.text(20, 45, '🔄 第1回合 - 移动阶段', {
      fontSize: '16px',
      color: '#3498db'
    });

    // 返回按钮
    this.createDemoButton(this.scale.width - 100, this.scale.height - 30, '返回大厅', () => {
      this.scene.start('LobbyScene', { networkManager: this.networkManager });
    });
  }

  /**
   * 显示游戏说明
   */
  private showGameInstructions(): void {
    this.add.text(this.scale.width - 20, 100,
      '🎮 游戏说明:\n' +
      '• 点击己方军队选择\n' +
      '• 点击相邻地块移动\n' +
      '• 移动到敌军触发战斗\n' +
      '• 观看自走棋战斗\n' +
      '• 获胜占领敌方领土', {
      fontSize: '14px',
      color: '#ecf0f1',
      lineSpacing: 5
    }).setOrigin(1, 0);
  }
}
