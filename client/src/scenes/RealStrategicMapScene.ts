import Phaser from 'phaser';
import { NetworkManager } from '../network/NetworkManager';

/**
 * 真实的战略地图场景
 * 实现真正的SLG游戏玩法
 */

interface MapTile {
  x: number;
  y: number;
  terrain: 'plain' | 'mountain' | 'forest' | 'river';
  owner: string | null;
  army: Army | null;
  building: string | null;
}

interface Army {
  id: string;
  ownerId: string;
  cards: any[];
  strength: number;
  position: { x: number; y: number };
}

interface Player {
  id: string;
  name: string;
  color: number;
  resources: { gold: number; population: number };
  armies: Army[];
  territories: number;
}

export class RealStrategicMapScene extends Phaser.Scene {
  private networkManager!: NetworkManager;
  private mapTiles: MapTile[][] = [];
  private players: Map<string, Player> = new Map();
  private currentPlayer: string = '';
  private selectedArmy: Army | null = null;
  private mapContainer!: Phaser.GameObjects.Container;
  private uiContainer!: Phaser.GameObjects.Container;
  
  // 地图参数
  private readonly MAP_WIDTH = 12;
  private readonly MAP_HEIGHT = 8;
  private readonly TILE_SIZE = 50;
  private readonly MAP_OFFSET_X = 100;
  private readonly MAP_OFFSET_Y = 100;
  
  // 游戏状态
  private currentTurn = 1;
  private turnTimeLeft = 60;
  private gamePhase: 'deploy' | 'move' | 'combat' | 'end' = 'deploy';

  constructor() {
    super({ key: 'RealStrategicMapScene' });
  }

  init(data: any) {
    this.networkManager = data.networkManager;
    this.currentPlayer = data.playerId || 'player1';
    console.log('🗺️ 真实战略地图场景初始化', data);
  }

  create() {
    console.log('🗺️ 创建真实战略地图场景');
    
    // 创建容器
    this.mapContainer = this.add.container(0, 0);
    this.uiContainer = this.add.container(0, 0);
    
    // 初始化地图
    this.initializeMap();
    
    // 初始化玩家
    this.initializePlayers();
    
    // 创建UI
    this.createUI();
    
    // 渲染地图
    this.renderMap();
    
    // 开始游戏循环
    this.startGameLoop();
  }

  /**
   * 初始化地图
   */
  private initializeMap(): void {
    this.mapTiles = [];
    
    for (let x = 0; x < this.MAP_WIDTH; x++) {
      this.mapTiles[x] = [];
      for (let y = 0; y < this.MAP_HEIGHT; y++) {
        // 随机生成地形
        const terrains: MapTile['terrain'][] = ['plain', 'mountain', 'forest', 'river'];
        const terrain = terrains[Math.floor(Math.random() * terrains.length)];
        
        this.mapTiles[x][y] = {
          x,
          y,
          terrain,
          owner: null,
          army: null,
          building: null
        };
      }
    }
    
    console.log('🗺️ 地图初始化完成');
  }

  /**
   * 初始化玩家
   */
  private initializePlayers(): void {
    // 创建测试玩家
    const player1: Player = {
      id: 'player1',
      name: '刘备',
      color: 0x27ae60,
      resources: { gold: 100, population: 50 },
      armies: [],
      territories: 0
    };

    const player2: Player = {
      id: 'player2',
      name: '曹操',
      color: 0xe74c3c,
      resources: { gold: 100, population: 50 },
      armies: [],
      territories: 0
    };

    this.players.set('player1', player1);
    this.players.set('player2', player2);

    // 为每个玩家创建初始军队
    this.createInitialArmies();
    
    console.log('👥 玩家初始化完成');
  }

  /**
   * 创建初始军队
   */
  private createInitialArmies(): void {
    // 玩家1的军队
    const army1: Army = {
      id: 'army1',
      ownerId: 'player1',
      cards: [
        { name: '刘备', attack: 8, health: 12, cost: 3 },
        { name: '关羽', attack: 10, health: 10, cost: 4 },
        { name: '张飞', attack: 9, health: 11, cost: 4 }
      ],
      strength: 3,
      position: { x: 1, y: 3 }
    };

    // 玩家2的军队
    const army2: Army = {
      id: 'army2',
      ownerId: 'player2',
      cards: [
        { name: '曹操', attack: 9, health: 11, cost: 3 },
        { name: '夏侯惇', attack: 8, health: 10, cost: 3 },
        { name: '许褚', attack: 10, health: 9, cost: 4 }
      ],
      strength: 3,
      position: { x: 10, y: 4 }
    };

    // 将军队放置到地图上
    this.mapTiles[army1.position.x][army1.position.y].army = army1;
    this.mapTiles[army1.position.x][army1.position.y].owner = 'player1';
    
    this.mapTiles[army2.position.x][army2.position.y].army = army2;
    this.mapTiles[army2.position.x][army2.position.y].owner = 'player2';

    // 添加到玩家军队列表
    this.players.get('player1')!.armies.push(army1);
    this.players.get('player2')!.armies.push(army2);
  }

  /**
   * 渲染地图
   */
  private renderMap(): void {
    // 清除现有地图
    this.mapContainer.removeAll();

    for (let x = 0; x < this.MAP_WIDTH; x++) {
      for (let y = 0; y < this.MAP_HEIGHT; y++) {
        const tile = this.mapTiles[x][y];
        this.renderTile(tile);
      }
    }
  }

  /**
   * 渲染单个地块
   */
  private renderTile(tile: MapTile): void {
    const tileX = this.MAP_OFFSET_X + tile.x * this.TILE_SIZE;
    const tileY = this.MAP_OFFSET_Y + tile.y * this.TILE_SIZE;

    // 地形颜色
    const terrainColors = {
      plain: 0x90EE90,
      mountain: 0x8B4513,
      forest: 0x228B22,
      river: 0x4169E1
    };

    // 创建地块
    const tileRect = this.add.rectangle(
      tileX + this.TILE_SIZE / 2,
      tileY + this.TILE_SIZE / 2,
      this.TILE_SIZE - 2,
      this.TILE_SIZE - 2,
      terrainColors[tile.terrain]
    );

    // 如果有所有者，添加边框
    if (tile.owner) {
      const player = this.players.get(tile.owner);
      if (player) {
        tileRect.setStrokeStyle(3, player.color);
      }
    } else {
      tileRect.setStrokeStyle(1, 0x666666);
    }

    // 添加地形图标
    const terrainIcons = {
      plain: '🌾',
      mountain: '🏔️',
      forest: '🌲',
      river: '🌊'
    };

    const icon = this.add.text(
      tileX + this.TILE_SIZE / 2,
      tileY + this.TILE_SIZE / 2 - 10,
      terrainIcons[tile.terrain],
      { fontSize: '16px' }
    ).setOrigin(0.5);

    // 如果有军队，显示军队
    if (tile.army) {
      const armyIcon = this.add.text(
        tileX + this.TILE_SIZE / 2,
        tileY + this.TILE_SIZE / 2 + 10,
        '⚔️',
        { fontSize: '20px' }
      ).setOrigin(0.5);

      // 军队强度
      const strengthText = this.add.text(
        tileX + this.TILE_SIZE - 5,
        tileY + 5,
        tile.army.strength.toString(),
        { fontSize: '12px', color: '#ffffff', fontStyle: 'bold' }
      ).setOrigin(1, 0);

      // 设置军队交互
      armyIcon.setInteractive({ useHandCursor: true });
      armyIcon.on('pointerdown', () => {
        this.selectArmy(tile.army!);
      });
    }

    // 设置地块交互
    tileRect.setInteractive();
    tileRect.on('pointerdown', () => {
      this.onTileClick(tile);
    });

    this.mapContainer.add([tileRect, icon]);
  }

  /**
   * 选择军队
   */
  private selectArmy(army: Army): void {
    if (army.ownerId !== this.currentPlayer) {
      this.showMessage('不能选择敌方军队！', 0xe74c3c);
      return;
    }

    this.selectedArmy = army;
    this.showMessage(`选择了军队: ${army.cards.map(c => c.name).join(', ')}`, 0x3498db);
    console.log('选择军队:', army);
  }

  /**
   * 处理地块点击
   */
  private onTileClick(tile: MapTile): void {
    if (!this.selectedArmy) {
      return;
    }

    if (this.gamePhase !== 'move') {
      this.showMessage('现在不是移动阶段！', 0xf39c12);
      return;
    }

    // 检查是否可以移动到该位置
    if (this.canMoveToTile(this.selectedArmy, tile)) {
      this.moveArmyToTile(this.selectedArmy, tile);
    } else {
      this.showMessage('无法移动到该位置！', 0xe74c3c);
    }
  }

  /**
   * 检查是否可以移动到地块
   */
  private canMoveToTile(army: Army, tile: MapTile): boolean {
    // 计算距离
    const distance = Math.abs(army.position.x - tile.x) + Math.abs(army.position.y - tile.y);
    
    // 每回合只能移动1格
    if (distance !== 1) {
      return false;
    }

    // 山脉无法通过
    if (tile.terrain === 'mountain') {
      return false;
    }

    return true;
  }

  /**
   * 移动军队到地块
   */
  private moveArmyToTile(army: Army, targetTile: MapTile): void {
    // 清除原位置
    const oldTile = this.mapTiles[army.position.x][army.position.y];
    oldTile.army = null;

    // 检查目标位置是否有敌军
    if (targetTile.army && targetTile.army.ownerId !== army.ownerId) {
      // 触发战斗！
      this.triggerCombat(army, targetTile.army, targetTile);
      return;
    }

    // 移动军队
    army.position = { x: targetTile.x, y: targetTile.y };
    targetTile.army = army;
    targetTile.owner = army.ownerId;

    // 取消选择
    this.selectedArmy = null;

    // 重新渲染地图
    this.renderMap();

    this.showMessage(`军队移动到 (${targetTile.x}, ${targetTile.y})`, 0x27ae60);
  }

  /**
   * 触发战斗
   */
  private triggerCombat(attackerArmy: Army, defenderArmy: Army, battleTile: MapTile): void {
    console.log('⚔️ 触发战斗！', attackerArmy, 'vs', defenderArmy);
    
    this.showMessage('战斗开始！进入自走棋模式...', 0xe74c3c);

    // 2秒后切换到战斗场景
    this.time.delayedCall(2000, () => {
      this.scene.start('RealCombatScene', {
        networkManager: this.networkManager,
        attackerArmy: attackerArmy,
        defenderArmy: defenderArmy,
        battleTile: battleTile,
        returnScene: 'RealStrategicMapScene',
        mapState: this.getMapState()
      });
    });
  }

  /**
   * 创建UI
   */
  private createUI(): void {
    // 顶部信息栏
    const topBar = this.add.rectangle(this.scale.width / 2, 30, this.scale.width, 60, 0x2c3e50, 0.9);
    
    // 回合信息
    const turnText = this.add.text(20, 20, `第${this.currentTurn}回合`, {
      fontSize: '18px',
      color: '#ffffff',
      fontStyle: 'bold'
    });

    // 阶段信息
    const phaseText = this.add.text(150, 20, `阶段: ${this.gamePhase}`, {
      fontSize: '16px',
      color: '#f39c12'
    });

    // 当前玩家
    const currentPlayerText = this.add.text(300, 20, `当前玩家: ${this.players.get(this.currentPlayer)?.name}`, {
      fontSize: '16px',
      color: '#3498db'
    });

    // 资源信息
    const player = this.players.get(this.currentPlayer);
    if (player) {
      const resourceText = this.add.text(20, 70, 
        `💰 ${player.resources.gold}  👥 ${player.resources.population}  🏰 ${player.territories}`, {
        fontSize: '16px',
        color: '#27ae60'
      });
      this.uiContainer.add(resourceText);
    }

    // 控制按钮
    this.createControlButtons();

    this.uiContainer.add([topBar, turnText, phaseText, currentPlayerText]);
  }

  /**
   * 创建控制按钮
   */
  private createControlButtons(): void {
    const buttonY = this.scale.height - 50;

    // 结束回合按钮
    const endTurnButton = this.createButton(this.scale.width - 120, buttonY, '结束回合', () => {
      this.endTurn();
    });

    // 返回大厅按钮
    const backButton = this.createButton(20, buttonY, '返回大厅', () => {
      this.scene.start('LobbyScene', { networkManager: this.networkManager });
    });

    this.uiContainer.add([endTurnButton, backButton]);
  }

  /**
   * 创建按钮
   */
  private createButton(x: number, y: number, text: string, callback: () => void): Phaser.GameObjects.Container {
    const button = this.add.container(x, y);
    
    const background = this.add.rectangle(0, 0, 100, 30, 0x3498db);
    background.setStrokeStyle(2, 0x2980b9);
    background.setInteractive({ useHandCursor: true });
    
    const buttonText = this.add.text(0, 0, text, {
      fontSize: '14px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    background.on('pointerdown', callback);
    
    background.on('pointerover', () => {
      background.setFillStyle(0x5dade2);
    });

    background.on('pointerout', () => {
      background.setFillStyle(0x3498db);
    });

    button.add([background, buttonText]);
    return button;
  }

  /**
   * 开始游戏循环
   */
  private startGameLoop(): void {
    this.gamePhase = 'move';
    this.showMessage('移动阶段开始！选择军队并移动', 0x3498db);

    // 回合计时器
    this.time.addEvent({
      delay: 1000,
      callback: () => {
        this.turnTimeLeft--;
        if (this.turnTimeLeft <= 0) {
          this.endTurn();
        }
      },
      loop: true
    });
  }

  /**
   * 结束回合
   */
  private endTurn(): void {
    this.currentTurn++;
    this.turnTimeLeft = 60;
    this.selectedArmy = null;
    
    // 切换玩家
    this.currentPlayer = this.currentPlayer === 'player1' ? 'player2' : 'player1';
    
    this.showMessage(`第${this.currentTurn}回合开始！`, 0x27ae60);
    
    // 重新创建UI以更新信息
    this.uiContainer.removeAll();
    this.createUI();
  }

  /**
   * 显示消息
   */
  private showMessage(text: string, color: number): void {
    const message = this.add.text(this.scale.width / 2, this.scale.height - 100, text, {
      fontSize: '16px',
      color: '#ffffff',
      backgroundColor: Phaser.Display.Color.IntegerToRGB(color).rgba,
      padding: { x: 15, y: 8 }
    }).setOrigin(0.5);

    this.time.delayedCall(3000, () => {
      message.destroy();
    });
  }

  /**
   * 获取地图状态
   */
  private getMapState(): any {
    return {
      tiles: this.mapTiles,
      players: Array.from(this.players.values()),
      currentTurn: this.currentTurn,
      currentPlayer: this.currentPlayer
    };
  }
}
