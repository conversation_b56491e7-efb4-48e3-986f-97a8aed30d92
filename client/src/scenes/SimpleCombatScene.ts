import Phaser from 'phaser';
import { NetworkManager } from '../network/NetworkManager';

/**
 * 简化的自走棋战斗场景
 * 用于测试战斗系统
 */
export class SimpleCombatScene extends Phaser.Scene {
  private networkManager!: NetworkManager;
  private cards: any[] = [];

  constructor() {
    super({ key: 'SimpleCombatScene' });
  }

  init(data: any) {
    this.networkManager = data.networkManager;
    this.cards = data.cards || [];
    console.log('⚔️ 简化战斗场景初始化', data);
  }

  create() {
    console.log('⚔️ 简化战斗场景创建');
    
    // 创建背景
    this.add.rectangle(this.scale.width / 2, this.scale.height / 2, this.scale.width, this.scale.height, 0x1a1a2e);
    
    // 标题
    const title = this.add.text(this.scale.width / 2, 50, '⚔️ 自走棋战斗', {
      fontSize: '32px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    // 阶段信息
    const phaseText = this.add.text(this.scale.width / 2, 100, '部署阶段 - 60秒', {
      fontSize: '20px',
      color: '#f39c12'
    }).setOrigin(0.5);

    // 创建战斗棋盘
    this.createBattleBoard();

    // 创建卡牌区域
    this.createCardArea();

    // 创建控制按钮
    this.createControlButtons();

    // 开始倒计时
    this.startDeploymentTimer();
  }

  /**
   * 创建战斗棋盘
   */
  private createBattleBoard(): void {
    const boardX = this.scale.width / 2;
    const boardY = this.scale.height / 2;
    const cellSize = 40;
    const boardWidth = 16;
    const boardHeight = 8;

    // 棋盘背景
    const boardBg = this.add.rectangle(boardX, boardY, boardWidth * cellSize + 20, boardHeight * cellSize + 20, 0x2c3e50, 0.8);
    boardBg.setStrokeStyle(3, 0x3498db);

    // 绘制网格
    for (let x = 0; x < boardWidth; x++) {
      for (let y = 0; y < boardHeight; y++) {
        const cellX = boardX - (boardWidth * cellSize) / 2 + x * cellSize + cellSize / 2;
        const cellY = boardY - (boardHeight * cellSize) / 2 + y * cellSize + cellSize / 2;
        
        // 区分我方和敌方区域
        let cellColor = 0x34495e;
        if (x < 4) {
          cellColor = 0x27ae60; // 我方区域（绿色）
        } else if (x >= 12) {
          cellColor = 0xe74c3c; // 敌方区域（红色）
        }
        
        const cell = this.add.rectangle(cellX, cellY, cellSize - 2, cellSize - 2, cellColor, 0.3);
        cell.setStrokeStyle(1, 0x7f8c8d);
        cell.setInteractive();
        
        cell.on('pointerdown', () => {
          this.onCellClick(x, y);
        });
      }
    }

    // 区域标识
    this.add.text(boardX - (boardWidth * cellSize) / 2 + 2 * cellSize, boardY - (boardHeight * cellSize) / 2 - 20, '我方部署区', {
      fontSize: '14px',
      color: '#27ae60',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    this.add.text(boardX + (boardWidth * cellSize) / 2 - 2 * cellSize, boardY - (boardHeight * cellSize) / 2 - 20, '敌方部署区', {
      fontSize: '14px',
      color: '#e74c3c',
      fontStyle: 'bold'
    }).setOrigin(0.5);
  }

  /**
   * 创建卡牌区域
   */
  private createCardArea(): void {
    const cardY = this.scale.height - 100;
    
    // 卡牌区域背景
    const cardAreaBg = this.add.rectangle(this.scale.width / 2, cardY, this.scale.width, 120, 0x2c3e50, 0.9);
    
    // 显示可用卡牌
    if (this.cards.length > 0) {
      this.cards.forEach((card, index) => {
        const cardX = 100 + index * 120;
        this.createCardDisplay(card, cardX, cardY);
      });
    } else {
      // 显示示例卡牌
      const testCards = [
        { heroName: '刘备', cost: 3, rarity: 'legendary' },
        { heroName: '关羽', cost: 4, rarity: 'epic' },
        { heroName: '张飞', cost: 4, rarity: 'epic' }
      ];
      
      testCards.forEach((card, index) => {
        const cardX = 100 + index * 120;
        this.createCardDisplay(card, cardX, cardY);
      });
    }
  }

  /**
   * 创建卡牌显示
   */
  private createCardDisplay(card: any, x: number, y: number): void {
    const cardContainer = this.add.container(x, y);
    
    // 卡牌背景
    const cardBg = this.add.rectangle(0, 0, 80, 100, this.getCardColor(card.rarity));
    cardBg.setStrokeStyle(2, 0xffffff);
    cardBg.setInteractive({ useHandCursor: true });
    
    // 英雄名称
    const heroName = this.add.text(0, -20, card.heroName, {
      fontSize: '12px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);
    
    // 费用
    const cost = this.add.text(-30, -40, card.cost.toString(), {
      fontSize: '14px',
      color: '#f39c12',
      fontStyle: 'bold'
    }).setOrigin(0.5);
    
    // 稀有度
    const rarity = this.add.text(0, 20, card.rarity, {
      fontSize: '10px',
      color: '#ecf0f1'
    }).setOrigin(0.5);

    cardContainer.add([cardBg, heroName, cost, rarity]);
    
    // 点击事件
    cardBg.on('pointerdown', () => {
      console.log(`选择卡牌: ${card.heroName}`);
      this.showMessage(`选择了 ${card.heroName}`, 0x3498db);
    });
  }

  /**
   * 获取卡牌颜色
   */
  private getCardColor(rarity: string): number {
    switch (rarity) {
      case 'common': return 0x95a5a6;
      case 'rare': return 0x3498db;
      case 'epic': return 0x9b59b6;
      case 'legendary': return 0xf39c12;
      default: return 0x7f8c8d;
    }
  }

  /**
   * 处理格子点击
   */
  private onCellClick(x: number, y: number): void {
    if (x < 4) { // 只能在我方区域部署
      console.log(`部署到位置: (${x}, ${y})`);
      this.showMessage(`部署到 (${x}, ${y})`, 0x27ae60);
    } else {
      this.showMessage('只能在我方区域部署！', 0xe74c3c);
    }
  }

  /**
   * 创建控制按钮
   */
  private createControlButtons(): void {
    const buttonY = this.scale.height - 40;
    
    // 确认部署按钮
    const confirmButton = this.createButton(this.scale.width - 150, buttonY, '确认部署', () => {
      console.log('确认部署');
      this.startAutoBattle();
    });

    // 返回按钮
    const backButton = this.createButton(100, buttonY, '返回地图', () => {
      console.log('返回战略地图');
      this.scene.start('SimpleStrategicMapScene', { networkManager: this.networkManager });
    });
  }

  /**
   * 创建按钮
   */
  private createButton(x: number, y: number, text: string, callback: () => void): Phaser.GameObjects.Container {
    const button = this.add.container(x, y);
    
    const background = this.add.rectangle(0, 0, 120, 30, 0x3498db);
    background.setStrokeStyle(2, 0x2980b9);
    background.setInteractive({ useHandCursor: true });
    
    const buttonText = this.add.text(0, 0, text, {
      fontSize: '14px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    background.on('pointerdown', callback);
    
    background.on('pointerover', () => {
      background.setFillStyle(0x5dade2);
    });

    background.on('pointerout', () => {
      background.setFillStyle(0x3498db);
    });

    button.add([background, buttonText]);
    return button;
  }

  /**
   * 开始部署倒计时
   */
  private startDeploymentTimer(): void {
    let timeLeft = 60;
    
    const timerText = this.add.text(this.scale.width - 20, 100, `剩余时间: ${timeLeft}s`, {
      fontSize: '16px',
      color: '#f39c12'
    }).setOrigin(1, 0);

    const timer = this.time.addEvent({
      delay: 1000,
      callback: () => {
        timeLeft--;
        timerText.setText(`剩余时间: ${timeLeft}s`);
        
        if (timeLeft <= 0) {
          timer.destroy();
          this.startAutoBattle();
        }
      },
      loop: true
    });
  }

  /**
   * 开始自动战斗
   */
  private startAutoBattle(): void {
    console.log('开始自动战斗');
    
    // 显示战斗开始信息
    const battleText = this.add.text(this.scale.width / 2, this.scale.height / 2, '自动战斗开始！', {
      fontSize: '32px',
      color: '#e74c3c',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    // 模拟战斗过程
    this.time.delayedCall(3000, () => {
      battleText.setText('战斗进行中...');
    });

    this.time.delayedCall(6000, () => {
      battleText.setText('战斗结束！');
      this.showBattleResult();
    });
  }

  /**
   * 显示战斗结果
   */
  private showBattleResult(): void {
    const isVictory = Math.random() > 0.5;
    const resultText = isVictory ? '胜利！' : '失败！';
    const resultColor = isVictory ? '#27ae60' : '#e74c3c';
    
    const result = this.add.text(this.scale.width / 2, this.scale.height / 2 + 100, resultText, {
      fontSize: '48px',
      color: resultColor,
      fontStyle: 'bold'
    }).setOrigin(0.5);

    // 3秒后返回战略地图
    this.time.delayedCall(3000, () => {
      this.scene.start('SimpleStrategicMapScene', { networkManager: this.networkManager });
    });
  }

  /**
   * 显示消息
   */
  private showMessage(text: string, color: number): void {
    const message = this.add.text(this.scale.width / 2, 150, text, {
      fontSize: '16px',
      color: '#ffffff',
      backgroundColor: Phaser.Display.Color.IntegerToRGB(color).rgba,
      padding: { x: 15, y: 8 }
    }).setOrigin(0.5);

    // 2秒后消失
    this.time.delayedCall(2000, () => {
      message.destroy();
    });
  }
}
