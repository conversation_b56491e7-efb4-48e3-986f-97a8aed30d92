import Phaser from 'phaser';
import { NetworkManager } from '../network/NetworkManager';

/**
 * 世界地图场景
 * 真正的多人在线SLG系统
 */

interface WorldTile {
  id: string;
  x: number;
  y: number;
  terrain: string;
  resources: { gold: number; population: number; special?: string };
  occupiedBy?: string;
  army?: any;
}

interface WorldPlayer {
  id: string;
  username: string;
  level: number;
  experience: number;
  totalGold: number;
  totalPopulation: number;
  territories: string[];
  isOnline: boolean;
}

export class WorldMapScene extends Phaser.Scene {
  private networkManager!: NetworkManager;
  private room: any;
  
  // 世界状态
  private worldTiles: Map<string, WorldTile> = new Map();
  private worldPlayers: Map<string, WorldPlayer> = new Map();
  private currentPlayer: string | null = null;
  private myPlayerId: string = '';
  private currentTurn: number = 1;
  private turnTimeLeft: number = 60;
  
  // 地图显示
  private mapContainer!: Phaser.GameObjects.Container;
  private uiContainer!: Phaser.GameObjects.Container;
  private selectedArmy: any = null;
  private cameraX: number = 0;
  private cameraY: number = 0;
  private readonly TILE_SIZE = 40;
  private readonly VIEW_RADIUS = 15;

  constructor() {
    super({ key: 'WorldMapScene' });
  }

  init(data: any) {
    this.networkManager = data.networkManager;
    this.room = data.room;
    this.myPlayerId = data.playerId || '';
    console.log('🌍 世界地图场景初始化', data);
  }

  create() {
    console.log('🌍 创建世界地图场景');
    
    // 创建容器
    this.mapContainer = this.add.container(0, 0);
    this.uiContainer = this.add.container(0, 0);
    
    // 设置背景
    this.add.rectangle(this.scale.width / 2, this.scale.height / 2, this.scale.width, this.scale.height, 0x1a1a2e);
    
    // 创建UI
    this.createUI();
    
    // 设置网络事件
    this.setupNetworkEvents();
    
    // 请求初始地图数据
    this.requestMapData();
    
    // 设置相机控制
    this.setupCameraControls();
  }

  /**
   * 设置网络事件
   */
  private setupNetworkEvents(): void {
    if (!this.room) return;

    // 世界状态更新
    this.room.onMessage('world_update', (data: any) => {
      this.updateWorldState(data);
    });

    // 军队移动
    this.room.onMessage('army_moved', (data: any) => {
      this.showMessage(`${data.message}`, 0x27ae60);
      this.requestMapData(); // 刷新地图
    });

    // 移动错误
    this.room.onMessage('move_error', (data: any) => {
      this.showMessage(data.message, 0xe74c3c);
    });

    // 回合结束
    this.room.onMessage('turn_ended', (data: any) => {
      this.showMessage(`轮到 ${this.getPlayerName(data.newCurrentPlayer)} 行动`, 0x3498db);
    });

    // 回合超时
    this.room.onMessage('turn_timeout', (data: any) => {
      this.showMessage(`${this.getPlayerName(data.playerId)} 回合超时`, 0xf39c12);
    });

    // 战斗开始
    this.room.onMessage('battle_start', (data: any) => {
      this.startBattle(data);
    });

    // 战斗结果
    this.room.onMessage('battle_result', (data: any) => {
      this.showBattleResult(data);
    });

    // 地图数据
    this.room.onMessage('map_data', (data: any) => {
      this.updateMapDisplay(data.tiles);
    });

    // 聊天消息
    this.room.onMessage('chat_message', (data: any) => {
      this.showChatMessage(data);
    });

    // 玩家加入/离开
    this.room.onMessage('player_joined', (data: any) => {
      this.showMessage(`${data.username} 加入了世界`, 0x27ae60);
    });

    this.room.onMessage('player_left', (data: any) => {
      this.showMessage(`${this.getPlayerName(data.playerId)} 离开了世界`, 0x95a5a6);
    });
  }

  /**
   * 更新世界状态
   */
  private updateWorldState(data: any): void {
    this.currentTurn = data.currentTurn;
    this.currentPlayer = data.currentPlayer;
    this.turnTimeLeft = data.turnTimeLeft;
    
    // 更新玩家数据
    this.worldPlayers.clear();
    data.players.forEach((player: WorldPlayer) => {
      this.worldPlayers.set(player.id, player);
    });

    // 更新UI
    this.updateUI();
  }

  /**
   * 请求地图数据
   */
  private requestMapData(): void {
    if (!this.room) return;
    
    this.room.send('request_map', {
      centerX: this.cameraX,
      centerY: this.cameraY,
      radius: this.VIEW_RADIUS
    });
  }

  /**
   * 更新地图显示
   */
  private updateMapDisplay(tiles: WorldTile[]): void {
    // 清除现有地图
    this.mapContainer.removeAll();
    this.worldTiles.clear();
    
    // 地形颜色
    const terrainColors = {
      plain: 0x90EE90,
      forest: 0x228B22,
      mountain: 0x8B4513,
      river: 0x4169E1,
      desert: 0xF4A460,
      swamp: 0x556B2F,
      crystal: 0x9370DB,
      ruins: 0x696969
    };

    // 地形图标
    const terrainIcons = {
      plain: '🌾',
      forest: '🌲',
      mountain: '🏔️',
      river: '🌊',
      desert: '🏜️',
      swamp: '🌿',
      crystal: '💎',
      ruins: '🏛️'
    };

    const centerX = this.scale.width / 2;
    const centerY = this.scale.height / 2;

    tiles.forEach(tile => {
      this.worldTiles.set(tile.id, tile);
      
      // 计算屏幕位置
      const screenX = centerX + (tile.x - this.cameraX) * this.TILE_SIZE;
      const screenY = centerY + (tile.y - this.cameraY) * this.TILE_SIZE;
      
      // 创建地块
      const tileRect = this.add.rectangle(
        screenX, screenY,
        this.TILE_SIZE - 2, this.TILE_SIZE - 2,
        terrainColors[tile.terrain] || 0x666666
      );

      // 边框颜色（显示控制权）
      if (tile.occupiedBy) {
        const player = this.worldPlayers.get(tile.occupiedBy);
        if (player) {
          const borderColor = tile.occupiedBy === this.myPlayerId ? 0x27ae60 : 0xe74c3c;
          tileRect.setStrokeStyle(2, borderColor);
        }
      } else {
        tileRect.setStrokeStyle(1, 0x666666);
      }

      // 地形图标
      const icon = this.add.text(screenX, screenY - 8, terrainIcons[tile.terrain] || '?', {
        fontSize: '12px'
      }).setOrigin(0.5);

      // 军队图标
      if (tile.army) {
        const armyIcon = this.add.text(screenX, screenY + 8, '⚔️', {
          fontSize: '14px'
        }).setOrigin(0.5);

        // 军队等级
        const levelText = this.add.text(screenX + 12, screenY - 12, tile.army.level.toString(), {
          fontSize: '10px',
          color: '#ffffff',
          fontStyle: 'bold'
        }).setOrigin(0.5);

        // 军队点击事件
        armyIcon.setInteractive({ useHandCursor: true });
        armyIcon.on('pointerdown', () => {
          this.selectArmy(tile);
        });
      }

      // 地块点击事件
      tileRect.setInteractive();
      tileRect.on('pointerdown', () => {
        this.onTileClick(tile);
      });

      this.mapContainer.add([tileRect, icon]);
    });
  }

  /**
   * 选择军队
   */
  private selectArmy(tile: WorldTile): void {
    if (!tile.army || tile.army.playerId !== this.myPlayerId) {
      this.showMessage('只能选择自己的军队！', 0xe74c3c);
      return;
    }

    if (this.currentPlayer !== this.myPlayerId) {
      this.showMessage('不是你的回合！', 0xf39c12);
      return;
    }

    this.selectedArmy = tile.army;
    this.showMessage(`选择了军队 (等级${tile.army.level})，移动点数: ${tile.army.movementPoints}`, 0x3498db);
  }

  /**
   * 地块点击
   */
  private onTileClick(tile: WorldTile): void {
    if (!this.selectedArmy) {
      return;
    }

    if (this.currentPlayer !== this.myPlayerId) {
      this.showMessage('不是你的回合！', 0xf39c12);
      return;
    }

    if (this.selectedArmy.movementPoints <= 0) {
      this.showMessage('移动点数不足！', 0xe74c3c);
      return;
    }

    // 发送移动命令
    this.room.send('move_army', {
      armyId: this.selectedArmy.id,
      targetTileId: tile.id
    });

    this.selectedArmy = null;
  }

  /**
   * 创建UI
   */
  private createUI(): void {
    // 顶部信息栏
    const topBar = this.add.rectangle(this.scale.width / 2, 30, this.scale.width, 60, 0x2c3e50, 0.9);
    
    // 回合信息
    const turnText = this.add.text(20, 20, `第${this.currentTurn}回合`, {
      fontSize: '16px',
      color: '#ffffff',
      fontStyle: 'bold'
    });

    // 当前玩家
    const currentPlayerText = this.add.text(150, 20, '等待中...', {
      fontSize: '14px',
      color: '#f39c12'
    });

    // 时间倒计时
    const timerText = this.add.text(this.scale.width - 20, 20, '60s', {
      fontSize: '14px',
      color: '#e74c3c'
    }).setOrigin(1, 0);

    // 玩家信息
    const playerInfo = this.add.text(20, 70, '', {
      fontSize: '14px',
      color: '#27ae60'
    });

    // 控制按钮
    this.createControlButtons();

    // 在线玩家列表
    this.createPlayerList();

    this.uiContainer.add([topBar, turnText, currentPlayerText, timerText, playerInfo]);

    // 保存UI元素引用
    (this as any).turnText = turnText;
    (this as any).currentPlayerText = currentPlayerText;
    (this as any).timerText = timerText;
    (this as any).playerInfo = playerInfo;
  }

  /**
   * 更新UI
   */
  private updateUI(): void {
    const turnText = (this as any).turnText;
    const currentPlayerText = (this as any).currentPlayerText;
    const timerText = (this as any).timerText;
    const playerInfo = (this as any).playerInfo;

    if (turnText) {
      turnText.setText(`第${this.currentTurn}回合`);
    }

    if (currentPlayerText) {
      const currentPlayerName = this.getPlayerName(this.currentPlayer);
      const isMyTurn = this.currentPlayer === this.myPlayerId;
      currentPlayerText.setText(isMyTurn ? '你的回合' : `${currentPlayerName}的回合`);
      currentPlayerText.setColor(isMyTurn ? '#27ae60' : '#f39c12');
    }

    if (timerText) {
      timerText.setText(`${this.turnTimeLeft}s`);
    }

    if (playerInfo) {
      const myPlayer = this.worldPlayers.get(this.myPlayerId);
      if (myPlayer) {
        playerInfo.setText(
          `等级: ${myPlayer.level} | 金币: ${myPlayer.totalGold} | 人口: ${myPlayer.totalPopulation} | 领土: ${myPlayer.territories.length}`
        );
      }
    }
  }

  /**
   * 创建控制按钮
   */
  private createControlButtons(): void {
    const buttonY = this.scale.height - 50;

    // 结束回合按钮
    const endTurnButton = this.createButton(this.scale.width - 120, buttonY, '结束回合', () => {
      if (this.currentPlayer === this.myPlayerId) {
        this.room.send('end_turn');
      } else {
        this.showMessage('不是你的回合！', 0xe74c3c);
      }
    });

    // 返回大厅按钮
    const backButton = this.createButton(20, buttonY, '返回大厅', () => {
      this.scene.start('LobbyScene', { networkManager: this.networkManager });
    });

    this.uiContainer.add([endTurnButton, backButton]);
  }

  /**
   * 创建玩家列表
   */
  private createPlayerList(): void {
    const listX = this.scale.width - 200;
    const listY = 100;

    const listBg = this.add.rectangle(listX, listY, 180, 200, 0x2c3e50, 0.8);
    listBg.setStrokeStyle(2, 0x34495e);

    const listTitle = this.add.text(listX, listY - 80, '在线玩家', {
      fontSize: '14px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    this.uiContainer.add([listBg, listTitle]);

    // 玩家列表会在updatePlayerList中动态更新
    this.updatePlayerList();
  }

  /**
   * 更新玩家列表
   */
  private updatePlayerList(): void {
    // 这里可以实现动态更新在线玩家列表
    // 简化实现，显示玩家数量
    const onlineCount = Array.from(this.worldPlayers.values()).filter(p => p.isOnline).length;
    console.log(`👥 在线玩家: ${onlineCount}`);
  }

  /**
   * 设置相机控制
   */
  private setupCameraControls(): void {
    // WASD键移动相机
    const cursors = this.input.keyboard?.createCursorKeys();
    const wasd = this.input.keyboard?.addKeys('W,S,A,D');

    // 每帧更新相机位置
    this.time.addEvent({
      delay: 16, // 60 FPS
      callback: () => {
        let moved = false;
        
        if (cursors?.left.isDown || wasd?.A.isDown) {
          this.cameraX--;
          moved = true;
        }
        if (cursors?.right.isDown || wasd?.D.isDown) {
          this.cameraX++;
          moved = true;
        }
        if (cursors?.up.isDown || wasd?.W.isDown) {
          this.cameraY--;
          moved = true;
        }
        if (cursors?.down.isDown || wasd?.S.isDown) {
          this.cameraY++;
          moved = true;
        }

        if (moved) {
          this.requestMapData();
        }
      },
      loop: true
    });
  }

  /**
   * 开始战斗
   */
  private startBattle(data: any): void {
    this.showMessage('进入战斗！', 0xe74c3c);
    
    // 切换到战斗场景
    this.scene.start('BattleScene', {
      networkManager: this.networkManager,
      room: this.room,
      battleData: data
    });
  }

  /**
   * 显示战斗结果
   */
  private showBattleResult(data: any): void {
    const isWinner = data.winner === this.myPlayerId;
    const message = isWinner ? '战斗胜利！' : '战斗失败！';
    const color = isWinner ? 0x27ae60 : 0xe74c3c;
    
    this.showMessage(message, color);
    this.requestMapData(); // 刷新地图
  }

  /**
   * 显示聊天消息
   */
  private showChatMessage(data: any): void {
    console.log(`💬 ${data.playerName}: ${data.message}`);
    // 这里可以实现聊天UI
  }

  /**
   * 创建按钮
   */
  private createButton(x: number, y: number, text: string, callback: () => void): Phaser.GameObjects.Container {
    const button = this.add.container(x, y);
    
    const background = this.add.rectangle(0, 0, 100, 30, 0x3498db);
    background.setStrokeStyle(2, 0x2980b9);
    background.setInteractive({ useHandCursor: true });
    
    const buttonText = this.add.text(0, 0, text, {
      fontSize: '12px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    background.on('pointerdown', callback);
    
    background.on('pointerover', () => {
      background.setFillStyle(0x5dade2);
    });

    background.on('pointerout', () => {
      background.setFillStyle(0x3498db);
    });

    button.add([background, buttonText]);
    return button;
  }

  /**
   * 显示消息
   */
  private showMessage(text: string, color: number): void {
    const message = this.add.text(this.scale.width / 2, this.scale.height - 100, text, {
      fontSize: '16px',
      color: '#ffffff',
      backgroundColor: Phaser.Display.Color.IntegerToRGB(color).rgba,
      padding: { x: 15, y: 8 }
    }).setOrigin(0.5);

    this.time.delayedCall(3000, () => {
      message.destroy();
    });
  }

  /**
   * 获取玩家名称
   */
  private getPlayerName(playerId: string | null): string {
    if (!playerId) return '未知';
    const player = this.worldPlayers.get(playerId);
    return player?.username || '未知玩家';
  }
}
