import Phaser from 'phaser';
import { NetworkManager } from '../network/NetworkManager';

/**
 * 真实的自走棋战斗场景
 * 实现真正的自走棋战斗系统
 */

interface CombatUnit {
  id: string;
  name: string;
  attack: number;
  health: number;
  maxHealth: number;
  position: { x: number; y: number };
  ownerId: string;
  isAlive: boolean;
  target: CombatUnit | null;
  lastActionTime: number;
}

interface BattleResult {
  winner: string;
  survivors: CombatUnit[];
  duration: number;
}

export class RealCombatScene extends Phaser.Scene {
  private networkManager!: NetworkManager;
  private attackerArmy: any;
  private defenderArmy: any;
  private battleTile: any;
  private returnScene: string = '';
  private mapState: any;

  private combatUnits: CombatUnit[] = [];
  private battlePhase: 'deploy' | 'combat' | 'result' = 'deploy';
  private deployTimeLeft = 30;
  private selectedCard: any = null;
  private deployedUnits: Map<string, CombatUnit> = new Map();

  // 战斗棋盘参数
  private readonly BOARD_WIDTH = 8;
  private readonly BOARD_HEIGHT = 6;
  private readonly CELL_SIZE = 60;
  private readonly BOARD_OFFSET_X = 200;
  private readonly BOARD_OFFSET_Y = 150;

  constructor() {
    super({ key: 'RealCombatScene' });
  }

  init(data: any) {
    this.networkManager = data.networkManager;
    this.attackerArmy = data.attackerArmy;
    this.defenderArmy = data.defenderArmy;
    this.battleTile = data.battleTile;
    this.returnScene = data.returnScene;
    this.mapState = data.mapState;
    
    console.log('⚔️ 真实战斗场景初始化', data);
  }

  create() {
    console.log('⚔️ 创建真实战斗场景');
    
    // 创建背景
    this.add.rectangle(this.scale.width / 2, this.scale.height / 2, this.scale.width, this.scale.height, 0x1a1a2e);
    
    // 创建战斗棋盘
    this.createBattleBoard();
    
    // 创建UI
    this.createUI();
    
    // 开始部署阶段
    this.startDeploymentPhase();
  }

  /**
   * 创建战斗棋盘
   */
  private createBattleBoard(): void {
    // 棋盘背景
    const boardBg = this.add.rectangle(
      this.BOARD_OFFSET_X + (this.BOARD_WIDTH * this.CELL_SIZE) / 2,
      this.BOARD_OFFSET_Y + (this.BOARD_HEIGHT * this.CELL_SIZE) / 2,
      this.BOARD_WIDTH * this.CELL_SIZE + 10,
      this.BOARD_HEIGHT * this.CELL_SIZE + 10,
      0x2c3e50,
      0.8
    );
    boardBg.setStrokeStyle(3, 0x3498db);

    // 绘制网格
    for (let x = 0; x < this.BOARD_WIDTH; x++) {
      for (let y = 0; y < this.BOARD_HEIGHT; y++) {
        const cellX = this.BOARD_OFFSET_X + x * this.CELL_SIZE;
        const cellY = this.BOARD_OFFSET_Y + y * this.CELL_SIZE;
        
        // 区分部署区域
        let cellColor = 0x34495e;
        if (x < 2) {
          cellColor = 0x27ae60; // 攻击方区域（绿色）
        } else if (x >= this.BOARD_WIDTH - 2) {
          cellColor = 0xe74c3c; // 防守方区域（红色）
        }
        
        const cell = this.add.rectangle(
          cellX + this.CELL_SIZE / 2,
          cellY + this.CELL_SIZE / 2,
          this.CELL_SIZE - 2,
          this.CELL_SIZE - 2,
          cellColor,
          0.3
        );
        cell.setStrokeStyle(1, 0x7f8c8d);
        cell.setInteractive();
        
        cell.on('pointerdown', () => {
          this.onCellClick(x, y);
        });
      }
    }

    // 区域标识
    this.add.text(this.BOARD_OFFSET_X + this.CELL_SIZE, this.BOARD_OFFSET_Y - 20, '攻击方', {
      fontSize: '16px',
      color: '#27ae60',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    this.add.text(this.BOARD_OFFSET_X + (this.BOARD_WIDTH - 1) * this.CELL_SIZE, this.BOARD_OFFSET_Y - 20, '防守方', {
      fontSize: '16px',
      color: '#e74c3c',
      fontStyle: 'bold'
    }).setOrigin(0.5);
  }

  /**
   * 创建UI
   */
  private createUI(): void {
    // 顶部信息栏
    const topBar = this.add.rectangle(this.scale.width / 2, 30, this.scale.width, 60, 0x2c3e50, 0.9);
    
    // 战斗信息
    const battleInfo = this.add.text(20, 20, 
      `⚔️ ${this.attackerArmy.cards.map(c => c.name).join(',')} VS ${this.defenderArmy.cards.map(c => c.name).join(',')}`, {
      fontSize: '16px',
      color: '#ffffff',
      fontStyle: 'bold'
    });

    // 阶段信息
    const phaseText = this.add.text(this.scale.width / 2, 20, '部署阶段', {
      fontSize: '18px',
      color: '#f39c12',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    // 倒计时
    const timerText = this.add.text(this.scale.width - 20, 20, `${this.deployTimeLeft}s`, {
      fontSize: '16px',
      color: '#e74c3c'
    }).setOrigin(1, 0);

    // 创建卡牌区域
    this.createCardArea();

    // 控制按钮
    this.createControlButtons();
  }

  /**
   * 创建卡牌区域
   */
  private createCardArea(): void {
    const cardY = this.scale.height - 100;
    
    // 卡牌区域背景
    const cardAreaBg = this.add.rectangle(this.scale.width / 2, cardY, this.scale.width, 120, 0x2c3e50, 0.9);
    
    // 显示攻击方卡牌（当前玩家）
    this.attackerArmy.cards.forEach((card: any, index: number) => {
      const cardX = 100 + index * 120;
      this.createCardDisplay(card, cardX, cardY, true);
    });

    // 显示防守方卡牌（已部署，不可选择）
    this.defenderArmy.cards.forEach((card: any, index: number) => {
      const cardX = this.scale.width - 100 - index * 120;
      this.createCardDisplay(card, cardX, cardY, false);
    });
  }

  /**
   * 创建卡牌显示
   */
  private createCardDisplay(card: any, x: number, y: number, isSelectable: boolean): void {
    const cardContainer = this.add.container(x, y);
    
    // 卡牌背景
    const cardBg = this.add.rectangle(0, 0, 80, 100, isSelectable ? 0x3498db : 0x7f8c8d);
    cardBg.setStrokeStyle(2, 0xffffff);
    
    if (isSelectable) {
      cardBg.setInteractive({ useHandCursor: true });
    }
    
    // 英雄名称
    const heroName = this.add.text(0, -20, card.name, {
      fontSize: '12px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);
    
    // 攻击力/生命值
    const stats = this.add.text(0, 10, `${card.attack}/${card.health}`, {
      fontSize: '14px',
      color: '#ffffff'
    }).setOrigin(0.5);

    cardContainer.add([cardBg, heroName, stats]);
    
    // 点击事件（仅攻击方可选择）
    if (isSelectable) {
      cardBg.on('pointerdown', () => {
        this.selectCard(card);
      });
    }
  }

  /**
   * 选择卡牌
   */
  private selectCard(card: any): void {
    this.selectedCard = card;
    this.showMessage(`选择了 ${card.name}`, 0x3498db);
  }

  /**
   * 处理格子点击
   */
  private onCellClick(x: number, y: number): void {
    if (this.battlePhase !== 'deploy') {
      return;
    }

    if (!this.selectedCard) {
      this.showMessage('请先选择一张卡牌！', 0xf39c12);
      return;
    }

    // 攻击方只能在左侧部署
    if (x >= 2) {
      this.showMessage('只能在己方区域部署！', 0xe74c3c);
      return;
    }

    // 检查位置是否已被占用
    const positionKey = `${x}_${y}`;
    if (this.deployedUnits.has(positionKey)) {
      this.showMessage('该位置已有单位！', 0xe74c3c);
      return;
    }

    // 部署单位
    this.deployUnit(this.selectedCard, x, y);
  }

  /**
   * 部署单位
   */
  private deployUnit(card: any, x: number, y: number): void {
    const unit: CombatUnit = {
      id: `unit_${Date.now()}`,
      name: card.name,
      attack: card.attack,
      health: card.health,
      maxHealth: card.health,
      position: { x, y },
      ownerId: this.attackerArmy.ownerId,
      isAlive: true,
      target: null,
      lastActionTime: 0
    };

    // 添加到战斗单位列表
    this.combatUnits.push(unit);
    
    // 记录部署位置
    const positionKey = `${x}_${y}`;
    this.deployedUnits.set(positionKey, unit);

    // 在棋盘上显示单位
    this.displayUnit(unit);

    // 从可选卡牌中移除
    const cardIndex = this.attackerArmy.cards.findIndex((c: any) => c === card);
    if (cardIndex !== -1) {
      this.attackerArmy.cards.splice(cardIndex, 1);
    }

    this.selectedCard = null;
    this.showMessage(`${card.name} 部署成功！`, 0x27ae60);

    // 重新创建卡牌区域
    this.children.removeAll();
    this.create();
  }

  /**
   * 在棋盘上显示单位
   */
  private displayUnit(unit: CombatUnit): void {
    const cellX = this.BOARD_OFFSET_X + unit.position.x * this.CELL_SIZE + this.CELL_SIZE / 2;
    const cellY = this.BOARD_OFFSET_Y + unit.position.y * this.CELL_SIZE + this.CELL_SIZE / 2;

    // 单位背景
    const unitBg = this.add.circle(cellX, cellY, this.CELL_SIZE / 2 - 5, 
      unit.ownerId === this.attackerArmy.ownerId ? 0x27ae60 : 0xe74c3c);
    unitBg.setStrokeStyle(2, 0xffffff);

    // 单位名称
    const unitName = this.add.text(cellX, cellY - 5, unit.name.charAt(0), {
      fontSize: '16px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    // 生命值条
    const healthBar = this.add.rectangle(cellX, cellY + 15, 30, 4, 0x27ae60);
    const healthBg = this.add.rectangle(cellX, cellY + 15, 30, 4, 0x2c3e50);
    healthBg.setStrokeStyle(1, 0x7f8c8d);
  }

  /**
   * 开始部署阶段
   */
  private startDeploymentPhase(): void {
    this.battlePhase = 'deploy';
    
    // 自动部署防守方单位
    this.autoDeployDefender();

    // 部署倒计时
    const timer = this.time.addEvent({
      delay: 1000,
      callback: () => {
        this.deployTimeLeft--;
        
        if (this.deployTimeLeft <= 0) {
          this.startCombatPhase();
          timer.destroy();
        }
      },
      loop: true
    });
  }

  /**
   * 自动部署防守方
   */
  private autoDeployDefender(): void {
    this.defenderArmy.cards.forEach((card: any, index: number) => {
      const x = this.BOARD_WIDTH - 1 - (index % 2);
      const y = Math.floor(index / 2) + 1;

      const unit: CombatUnit = {
        id: `defender_${index}`,
        name: card.name,
        attack: card.attack,
        health: card.health,
        maxHealth: card.health,
        position: { x, y },
        ownerId: this.defenderArmy.ownerId,
        isAlive: true,
        target: null,
        lastActionTime: 0
      };

      this.combatUnits.push(unit);
      this.displayUnit(unit);
    });
  }

  /**
   * 开始战斗阶段
   */
  private startCombatPhase(): void {
    this.battlePhase = 'combat';
    this.showMessage('自动战斗开始！', 0xe74c3c);

    // 开始战斗循环
    this.startCombatLoop();
  }

  /**
   * 战斗循环
   */
  private startCombatLoop(): void {
    const combatTimer = this.time.addEvent({
      delay: 1000, // 每秒更新一次
      callback: () => {
        this.updateCombat();
        
        // 检查战斗结束条件
        if (this.checkCombatEnd()) {
          combatTimer.destroy();
          this.endCombat();
        }
      },
      loop: true
    });
  }

  /**
   * 更新战斗
   */
  private updateCombat(): void {
    for (const unit of this.combatUnits) {
      if (!unit.isAlive) continue;

      // 寻找目标
      if (!unit.target || !unit.target.isAlive) {
        unit.target = this.findTarget(unit);
      }

      if (unit.target) {
        // 攻击目标
        this.attackTarget(unit, unit.target);
      }
    }
  }

  /**
   * 寻找攻击目标
   */
  private findTarget(unit: CombatUnit): CombatUnit | null {
    const enemies = this.combatUnits.filter(u => 
      u.isAlive && u.ownerId !== unit.ownerId
    );

    if (enemies.length === 0) return null;

    // 寻找最近的敌人
    let closestEnemy = enemies[0];
    let closestDistance = this.getDistance(unit.position, closestEnemy.position);

    for (const enemy of enemies) {
      const distance = this.getDistance(unit.position, enemy.position);
      if (distance < closestDistance) {
        closestDistance = distance;
        closestEnemy = enemy;
      }
    }

    return closestEnemy;
  }

  /**
   * 攻击目标
   */
  private attackTarget(attacker: CombatUnit, target: CombatUnit): void {
    const damage = attacker.attack;
    target.health -= damage;

    this.showMessage(`${attacker.name} 攻击 ${target.name} 造成 ${damage} 伤害`, 0xf39c12);

    if (target.health <= 0) {
      target.isAlive = false;
      this.showMessage(`${target.name} 阵亡！`, 0xe74c3c);
    }
  }

  /**
   * 计算距离
   */
  private getDistance(pos1: { x: number; y: number }, pos2: { x: number; y: number }): number {
    return Math.abs(pos1.x - pos2.x) + Math.abs(pos1.y - pos2.y);
  }

  /**
   * 检查战斗结束
   */
  private checkCombatEnd(): boolean {
    const attackerAlive = this.combatUnits.some(u => u.isAlive && u.ownerId === this.attackerArmy.ownerId);
    const defenderAlive = this.combatUnits.some(u => u.isAlive && u.ownerId === this.defenderArmy.ownerId);

    return !attackerAlive || !defenderAlive;
  }

  /**
   * 结束战斗
   */
  private endCombat(): void {
    this.battlePhase = 'result';

    const attackerAlive = this.combatUnits.some(u => u.isAlive && u.ownerId === this.attackerArmy.ownerId);
    const winner = attackerAlive ? this.attackerArmy.ownerId : this.defenderArmy.ownerId;

    this.showBattleResult(winner);
  }

  /**
   * 显示战斗结果
   */
  private showBattleResult(winner: string): void {
    const isPlayerWin = winner === this.attackerArmy.ownerId;
    const resultText = isPlayerWin ? '胜利！' : '失败！';
    const resultColor = isPlayerWin ? '#27ae60' : '#e74c3c';

    const result = this.add.text(this.scale.width / 2, this.scale.height / 2, resultText, {
      fontSize: '48px',
      color: resultColor,
      fontStyle: 'bold'
    }).setOrigin(0.5);

    // 3秒后返回战略地图
    this.time.delayedCall(3000, () => {
      this.returnToStrategicMap(winner);
    });
  }

  /**
   * 返回战略地图
   */
  private returnToStrategicMap(winner: string): void {
    // 更新地图状态
    const battleResult = {
      winner,
      battleTile: this.battleTile,
      attackerArmy: this.attackerArmy,
      defenderArmy: this.defenderArmy
    };

    this.scene.start(this.returnScene, {
      networkManager: this.networkManager,
      mapState: this.mapState,
      battleResult: battleResult
    });
  }

  /**
   * 创建控制按钮
   */
  private createControlButtons(): void {
    const buttonY = this.scale.height - 40;

    // 确认部署按钮
    const confirmButton = this.createButton(this.scale.width - 120, buttonY, '确认部署', () => {
      if (this.battlePhase === 'deploy') {
        this.startCombatPhase();
      }
    });

    // 返回按钮
    const backButton = this.createButton(20, buttonY, '返回地图', () => {
      this.scene.start(this.returnScene, {
        networkManager: this.networkManager,
        mapState: this.mapState
      });
    });
  }

  /**
   * 创建按钮
   */
  private createButton(x: number, y: number, text: string, callback: () => void): Phaser.GameObjects.Container {
    const button = this.add.container(x, y);
    
    const background = this.add.rectangle(0, 0, 100, 30, 0x3498db);
    background.setStrokeStyle(2, 0x2980b9);
    background.setInteractive({ useHandCursor: true });
    
    const buttonText = this.add.text(0, 0, text, {
      fontSize: '12px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    background.on('pointerdown', callback);
    
    background.on('pointerover', () => {
      background.setFillStyle(0x5dade2);
    });

    background.on('pointerout', () => {
      background.setFillStyle(0x3498db);
    });

    button.add([background, buttonText]);
    return button;
  }

  /**
   * 显示消息
   */
  private showMessage(text: string, color: number): void {
    const message = this.add.text(this.scale.width / 2, 100, text, {
      fontSize: '14px',
      color: '#ffffff',
      backgroundColor: Phaser.Display.Color.IntegerToRGB(color).rgba,
      padding: { x: 10, y: 5 }
    }).setOrigin(0.5);

    this.time.delayedCall(2000, () => {
      message.destroy();
    });
  }
}
