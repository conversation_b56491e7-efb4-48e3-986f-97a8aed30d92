import Phaser from 'phaser';
import { HeroCard, Position, GameState } from '@multiplayer-game/shared';
import { NetworkManager } from '../network/NetworkManager';

/**
 * 自走棋战斗场景
 * 显示战斗棋盘，处理卡牌部署和观看自动战斗
 */
export class CombatScene extends Phaser.Scene {
  private networkManager!: NetworkManager;
  private boardContainer!: Phaser.GameObjects.Container;
  private uiContainer!: Phaser.GameObjects.Container;
  private deploymentCards: HeroCard[] = [];
  private selectedCard: HeroCard | null = null;
  private deployedUnits: Map<string, Phaser.GameObjects.Container> = new Map();
  
  // 棋盘参数
  private readonly BOARD_WIDTH = 16;
  private readonly BOARD_HEIGHT = 8;
  private readonly CELL_SIZE = 40;
  private readonly BOARD_OFFSET_X = 100;
  private readonly BOARD_OFFSET_Y = 100;
  
  // 游戏状态
  private gameState: GameState = GameState.COMBAT_DEPLOY;
  private deploymentTimeLeft: number = 60;
  private isMyTurn: boolean = true;

  constructor() {
    super({ key: 'CombatScene' });
  }

  init(data: any) {
    this.networkManager = data.networkManager;
    this.deploymentCards = data.cards || [];
  }

  create() {
    console.log('战斗场景创建');
    
    // 创建容器
    this.boardContainer = this.add.container(0, 0);
    this.uiContainer = this.add.container(0, 0);
    
    // 创建棋盘
    this.createBoard();
    
    // 创建UI
    this.createUI();
    
    // 创建卡牌区域
    this.createCardArea();
    
    // 设置输入
    this.setupInput();
    
    // 监听网络事件
    this.setupNetworkEvents();
    
    // 开始部署倒计时
    this.startDeploymentTimer();
  }

  /**
   * 创建战斗棋盘
   */
  private createBoard(): void {
    // 绘制棋盘网格
    for (let x = 0; x < this.BOARD_WIDTH; x++) {
      for (let y = 0; y < this.BOARD_HEIGHT; y++) {
        const cellX = this.BOARD_OFFSET_X + x * this.CELL_SIZE;
        const cellY = this.BOARD_OFFSET_Y + y * this.CELL_SIZE;
        
        // 创建格子
        const cell = this.add.rectangle(
          cellX + this.CELL_SIZE / 2,
          cellY + this.CELL_SIZE / 2,
          this.CELL_SIZE - 2,
          this.CELL_SIZE - 2,
          this.getCellColor(x, y)
        );
        
        cell.setStrokeStyle(1, 0x666666);
        cell.setInteractive();
        
        // 设置点击事件
        cell.on('pointerdown', () => {
          this.onCellClick(x, y);
        });
        
        cell.on('pointerover', () => {
          if (this.selectedCard && this.canDeployAt(x, y)) {
            cell.setFillStyle(0x00ff00, 0.5);
          }
        });
        
        cell.on('pointerout', () => {
          cell.setFillStyle(this.getCellColor(x, y));
        });
        
        this.boardContainer.add(cell);
      }
    }
    
    // 绘制部署区域标识
    this.drawDeploymentZones();
  }

  /**
   * 获取格子颜色
   */
  private getCellColor(x: number, y: number): number {
    // 玩家1部署区域（左侧）
    if (x < 4) {
      return 0x3498db;
    }
    // 玩家2部署区域（右侧）
    if (x >= this.BOARD_WIDTH - 4) {
      return 0xe74c3c;
    }
    // 中立区域
    return 0x2c3e50;
  }

  /**
   * 绘制部署区域标识
   */
  private drawDeploymentZones(): void {
    // 玩家1部署区域标识
    const player1Zone = this.add.text(
      this.BOARD_OFFSET_X + 2 * this.CELL_SIZE,
      this.BOARD_OFFSET_Y - 30,
      '我方部署区',
      { fontSize: '14px', color: '#3498db' }
    ).setOrigin(0.5);
    
    // 玩家2部署区域标识
    const player2Zone = this.add.text(
      this.BOARD_OFFSET_X + (this.BOARD_WIDTH - 2) * this.CELL_SIZE,
      this.BOARD_OFFSET_Y - 30,
      '敌方部署区',
      { fontSize: '14px', color: '#e74c3c' }
    ).setOrigin(0.5);
    
    this.uiContainer.add([player1Zone, player2Zone]);
  }

  /**
   * 创建UI界面
   */
  private createUI(): void {
    // 顶部信息栏
    const topBar = this.add.rectangle(this.scale.width / 2, 30, this.scale.width, 60, 0x2c3e50, 0.9);
    
    // 阶段信息
    const phaseText = this.add.text(20, 20, '部署阶段', {
      fontSize: '18px',
      color: '#ffffff',
      fontStyle: 'bold'
    });
    
    // 倒计时
    const timerText = this.add.text(this.scale.width - 20, 20, `剩余时间: ${this.deploymentTimeLeft}s`, {
      fontSize: '16px',
      color: '#f39c12'
    }).setOrigin(1, 0);
    
    // 确认部署按钮
    const confirmButton = this.add.rectangle(this.scale.width - 100, this.scale.height - 100, 120, 40, 0x27ae60);
    const confirmText = this.add.text(this.scale.width - 100, this.scale.height - 100, '确认部署', {
      fontSize: '14px',
      color: '#ffffff'
    }).setOrigin(0.5);
    
    confirmButton.setInteractive({ useHandCursor: true });
    confirmButton.on('pointerdown', () => {
      this.confirmDeployment();
    });
    
    this.uiContainer.add([topBar, phaseText, timerText, confirmButton, confirmText]);
  }

  /**
   * 创建卡牌区域
   */
  private createCardArea(): void {
    const cardAreaY = this.scale.height - 150;
    const cardWidth = 80;
    const cardHeight = 100;
    const cardSpacing = 90;
    const startX = (this.scale.width - (this.deploymentCards.length * cardSpacing)) / 2;
    
    // 背景
    const cardAreaBg = this.add.rectangle(
      this.scale.width / 2,
      cardAreaY + 50,
      this.scale.width,
      120,
      0x34495e,
      0.9
    );
    
    this.uiContainer.add(cardAreaBg);
    
    // 创建卡牌
    this.deploymentCards.forEach((card, index) => {
      const cardX = startX + index * cardSpacing;
      const cardContainer = this.createCardDisplay(card, cardX, cardAreaY, cardWidth, cardHeight);
      this.uiContainer.add(cardContainer);
    });
  }

  /**
   * 创建卡牌显示
   */
  private createCardDisplay(card: HeroCard, x: number, y: number, width: number, height: number): Phaser.GameObjects.Container {
    const container = this.add.container(x, y);
    
    // 卡牌背景
    const bg = this.add.rectangle(0, 0, width, height, this.getCardColor(card.rarity));
    bg.setStrokeStyle(2, 0xffffff);
    
    // 英雄头像（用文字代替）
    const avatar = this.add.text(0, -20, card.heroName.charAt(0), {
      fontSize: '24px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);
    
    // 卡牌名称
    const name = this.add.text(0, 10, card.heroName, {
      fontSize: '10px',
      color: '#ffffff'
    }).setOrigin(0.5);
    
    // 费用
    const cost = this.add.text(-width/2 + 10, -height/2 + 10, card.cost.toString(), {
      fontSize: '12px',
      color: '#f39c12',
      fontStyle: 'bold'
    }).setOrigin(0.5);
    
    // 攻击力/生命值
    const stats = this.add.text(0, 30, `${card.stats.attack}/${card.stats.health}`, {
      fontSize: '10px',
      color: '#ffffff'
    }).setOrigin(0.5);
    
    container.add([bg, avatar, name, cost, stats]);
    
    // 设置交互
    bg.setInteractive({ useHandCursor: true });
    bg.on('pointerdown', () => {
      this.selectCard(card);
    });
    
    return container;
  }

  /**
   * 获取卡牌颜色
   */
  private getCardColor(rarity: string): number {
    switch (rarity) {
      case 'common': return 0x95a5a6;
      case 'rare': return 0x3498db;
      case 'epic': return 0x9b59b6;
      case 'legendary': return 0xf39c12;
      default: return 0x7f8c8d;
    }
  }

  /**
   * 选择卡牌
   */
  private selectCard(card: HeroCard): void {
    this.selectedCard = card;
    console.log('选择卡牌:', card.heroName);
  }

  /**
   * 处理格子点击
   */
  private onCellClick(x: number, y: number): void {
    if (!this.selectedCard || this.gameState !== GameState.COMBAT_DEPLOY) {
      return;
    }
    
    if (this.canDeployAt(x, y)) {
      this.deployUnit(this.selectedCard, { x, y });
      this.selectedCard = null;
    }
  }

  /**
   * 检查是否可以在指定位置部署
   */
  private canDeployAt(x: number, y: number): boolean {
    // 只能在己方部署区域部署
    if (x >= 4) {
      return false;
    }
    
    // 检查位置是否已被占用
    const positionKey = `${x}_${y}`;
    return !this.deployedUnits.has(positionKey);
  }

  /**
   * 部署单位
   */
  private deployUnit(card: HeroCard, position: Position): void {
    const cellX = this.BOARD_OFFSET_X + position.x * this.CELL_SIZE + this.CELL_SIZE / 2;
    const cellY = this.BOARD_OFFSET_Y + position.y * this.CELL_SIZE + this.CELL_SIZE / 2;
    
    // 创建单位显示
    const unitContainer = this.add.container(cellX, cellY);
    
    // 单位背景
    const unitBg = this.add.circle(0, 0, this.CELL_SIZE / 2 - 2, this.getUnitColor(card.faction));
    unitBg.setStrokeStyle(2, 0xffffff);
    
    // 单位名称
    const unitName = this.add.text(0, 0, card.heroName.charAt(0), {
      fontSize: '16px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);
    
    unitContainer.add([unitBg, unitName]);
    this.boardContainer.add(unitContainer);
    
    // 记录部署
    const positionKey = `${position.x}_${position.y}`;
    this.deployedUnits.set(positionKey, unitContainer);
    
    // 从可部署卡牌中移除
    const cardIndex = this.deploymentCards.findIndex(c => c.id === card.id);
    if (cardIndex !== -1) {
      this.deploymentCards.splice(cardIndex, 1);
    }
    
    console.log(`部署 ${card.heroName} 到位置 (${position.x}, ${position.y})`);
  }

  /**
   * 获取单位颜色
   */
  private getUnitColor(faction: string): number {
    switch (faction) {
      case 'shu': return 0x27ae60;
      case 'wei': return 0x3498db;
      case 'wu': return 0xe74c3c;
      case 'qun': return 0xf39c12;
      default: return 0x95a5a6;
    }
  }

  /**
   * 确认部署
   */
  private confirmDeployment(): void {
    // 发送部署信息到服务器
    const deploymentData = Array.from(this.deployedUnits.entries()).map(([positionKey, container]) => {
      const [x, y] = positionKey.split('_').map(Number);
      return { position: { x, y }, cardId: 'temp' }; // 需要实际的卡牌ID
    });
    
    this.networkManager.sendMessage('confirm_deployment', deploymentData);
    
    // 切换到观战模式
    this.gameState = GameState.COMBAT_AUTO;
    this.isMyTurn = false;
  }

  /**
   * 设置输入控制
   */
  private setupInput(): void {
    // ESC键取消选择
    this.input.keyboard?.on('keydown-ESC', () => {
      this.selectedCard = null;
    });
  }

  /**
   * 设置网络事件监听
   */
  private setupNetworkEvents(): void {
    this.networkManager.onMessage('combat_start', (data) => {
      this.onCombatStart(data);
    });
    
    this.networkManager.onMessage('combat_update', (data) => {
      this.onCombatUpdate(data);
    });
    
    this.networkManager.onMessage('combat_end', (data) => {
      this.onCombatEnd(data);
    });
  }

  /**
   * 战斗开始
   */
  private onCombatStart(data: any): void {
    console.log('战斗开始:', data);
    this.gameState = GameState.COMBAT_AUTO;
    
    // 隐藏部署UI
    this.hideDeploymentUI();
    
    // 显示战斗UI
    this.showCombatUI();
  }

  /**
   * 战斗更新
   */
  private onCombatUpdate(data: any): void {
    // 更新单位位置和状态
    this.updateUnitsDisplay(data.units);
  }

  /**
   * 战斗结束
   */
  private onCombatEnd(data: any): void {
    console.log('战斗结束:', data);
    this.gameState = GameState.COMBAT_RESULT;
    
    // 显示战斗结果
    this.showCombatResult(data);
  }

  /**
   * 隐藏部署UI
   */
  private hideDeploymentUI(): void {
    // 隐藏卡牌区域和部署按钮
    // 实现UI隐藏逻辑
  }

  /**
   * 显示战斗UI
   */
  private showCombatUI(): void {
    // 显示战斗进度和信息
    // 实现战斗UI显示逻辑
  }

  /**
   * 更新单位显示
   */
  private updateUnitsDisplay(units: any[]): void {
    // 更新棋盘上单位的位置和状态
    // 实现单位动画和状态更新
  }

  /**
   * 显示战斗结果
   */
  private showCombatResult(result: any): void {
    // 显示胜负结果和奖励
    const resultText = result.winner ? '胜利!' : '失败!';
    const resultColor = result.winner ? '#27ae60' : '#e74c3c';
    
    const resultDisplay = this.add.text(this.scale.width / 2, this.scale.height / 2, resultText, {
      fontSize: '48px',
      color: resultColor,
      fontStyle: 'bold'
    }).setOrigin(0.5);
    
    this.uiContainer.add(resultDisplay);
    
    // 3秒后返回战略地图
    setTimeout(() => {
      this.scene.start('StrategicMapScene', { networkManager: this.networkManager });
    }, 3000);
  }

  /**
   * 开始部署倒计时
   */
  private startDeploymentTimer(): void {
    const timer = this.time.addEvent({
      delay: 1000,
      callback: () => {
        this.deploymentTimeLeft--;
        
        if (this.deploymentTimeLeft <= 0) {
          this.confirmDeployment();
          timer.destroy();
        }
      },
      loop: true
    });
  }
}
