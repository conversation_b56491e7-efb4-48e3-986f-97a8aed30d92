import Phaser from 'phaser';
import { NetworkManager } from '../network/NetworkManager';

export class LoginScene extends Phaser.Scene {
  private networkManager!: NetworkManager;
  private loginForm!: Phaser.GameObjects.Container;
  private registerForm!: Phaser.GameObjects.Container;
  private isLoginMode = true;
  private loadingText!: Phaser.GameObjects.Text;
  private errorText!: Phaser.GameObjects.Text;

  constructor() {
    super({ key: 'LoginScene' });
  }

  create() {
    this.networkManager = this.registry.get('networkManager');
    
    // 检查自动登录
    if (this.networkManager.autoLogin()) {
      console.log('自动登录成功');
      this.scene.start('LobbyScene');
      return;
    }

    this.createBackground();
    this.createTitle();
    this.createLoginForm();
    this.createRegisterForm();
    this.createToggleButton();
    this.createStatusTexts();

    // 默认显示登录表单
    this.showLoginForm();
  }

  private createBackground() {
    // 创建渐变背景
    const graphics = this.add.graphics();
    graphics.fillGradientStyle(0x1a1a2e, 0x16213e, 0x0f3460, 0x533483, 1);
    graphics.fillRect(0, 0, this.scale.width, this.scale.height);
  }

  private createTitle() {
    const title = this.add.text(this.scale.width / 2, 150, '多人在线游戏', {
      fontSize: '48px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    const subtitle = this.add.text(this.scale.width / 2, 200, '基于 Phaser3 & Colyseus', {
      fontSize: '18px',
      color: '#cccccc'
    }).setOrigin(0.5);
  }

  private createLoginForm() {
    this.loginForm = this.add.container(this.scale.width / 2, this.scale.height / 2);

    // 用户名输入框
    const usernameLabel = this.add.text(0, -80, '用户名:', {
      fontSize: '16px',
      color: '#ffffff'
    }).setOrigin(0.5);

    const usernameInput = this.createInputField(-100, -50, 200, 40, '请输入用户名');

    // 密码输入框
    const passwordLabel = this.add.text(0, -10, '密码:', {
      fontSize: '16px',
      color: '#ffffff'
    }).setOrigin(0.5);

    const passwordInput = this.createInputField(-100, 20, 200, 40, '请输入密码', true);

    // 登录按钮
    const loginButton = this.createButton(0, 80, '登录', () => {
      this.handleLogin(usernameInput.text, passwordInput.text);
    });

    this.loginForm.add([usernameLabel, usernameInput.background, usernameInput.textObject, passwordLabel, passwordInput.background, passwordInput.textObject, loginButton]);
  }

  private createRegisterForm() {
    this.registerForm = this.add.container(this.scale.width / 2, this.scale.height / 2);

    // 用户名输入框
    const usernameLabel = this.add.text(0, -80, '用户名:', {
      fontSize: '16px',
      color: '#ffffff'
    }).setOrigin(0.5);

    const usernameInput = this.createInputField(-100, -50, 200, 40, '请输入用户名');

    // 密码输入框
    const passwordLabel = this.add.text(0, -10, '密码:', {
      fontSize: '16px',
      color: '#ffffff'
    }).setOrigin(0.5);

    const passwordInput = this.createInputField(-100, 20, 200, 40, '请输入密码', true);

    // 注册按钮
    const registerButton = this.createButton(0, 80, '注册', () => {
      this.handleRegister(usernameInput.text, passwordInput.text);
    });

    this.registerForm.add([usernameLabel, usernameInput.background, usernameInput.textObject, passwordLabel, passwordInput.background, passwordInput.textObject, registerButton]);
    this.registerForm.setVisible(false);
  }

  private createToggleButton() {
    const toggleText = this.add.text(this.scale.width / 2, this.scale.height / 2 + 150,
      '没有账号？点击注册', {
      fontSize: '14px',
      color: '#4CAF50',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    toggleText.setInteractive({ useHandCursor: true });
    toggleText.on('pointerdown', () => {
      this.toggleMode();
      // 更新切换按钮文本
      if (this.isLoginMode) {
        toggleText.setText('没有账号？点击注册');
      } else {
        toggleText.setText('已有账号？点击登录');
      }
    });

    toggleText.on('pointerover', () => {
      toggleText.setColor('#66BB6A');
    });

    toggleText.on('pointerout', () => {
      toggleText.setColor('#4CAF50');
    });

    // 保存引用以便更新文本
    this.registry.set('toggleText', toggleText);
  }

  private createStatusTexts() {
    // 加载文本
    this.loadingText = this.add.text(this.scale.width / 2, this.scale.height / 2 + 200, '', {
      fontSize: '14px',
      color: '#f39c12',
      fontStyle: 'bold'
    }).setOrigin(0.5).setVisible(false);

    // 错误文本
    this.errorText = this.add.text(this.scale.width / 2, this.scale.height / 2 + 220, '', {
      fontSize: '14px',
      color: '#e74c3c',
      fontStyle: 'bold'
    }).setOrigin(0.5).setVisible(false);
  }

  private createInputField(x: number, y: number, width: number, height: number, placeholder: string, isPassword = false): any {
    const background = this.add.rectangle(x + width/2, y + height/2, width, height, 0x333333);
    background.setStrokeStyle(2, 0x555555);

    const text = this.add.text(x + 10, y + height/2, placeholder, {
      fontSize: '14px',
      color: '#999999'
    }).setOrigin(0, 0.5);

    // 创建一个隐藏的HTML输入框
    const inputElement = document.createElement('input');
    inputElement.type = isPassword ? 'password' : 'text';
    inputElement.placeholder = placeholder;
    inputElement.style.position = 'absolute';
    inputElement.style.left = '-9999px';
    inputElement.style.opacity = '0';
    document.body.appendChild(inputElement);

    const inputField = {
      text: '',
      placeholder: placeholder,
      background: background,
      textObject: text,
      isPassword: isPassword,
      htmlElement: inputElement
    };

    // 点击时聚焦到HTML输入框
    background.setInteractive({ useHandCursor: true });
    background.on('pointerdown', () => {
      // 创建一个模态对话框样式的输入
      this.showInputDialog(placeholder, isPassword, (value: string) => {
        if (value !== null && value !== undefined) {
          inputField.text = value;
          if (value.trim() === '') {
            text.setText(placeholder);
            text.setColor('#999999');
          } else {
            text.setText(isPassword ? '●'.repeat(value.length) : value);
            text.setColor('#ffffff');
          }
        }
      });
    });

    // 鼠标悬停效果
    background.on('pointerover', () => {
      background.setStrokeStyle(2, 0x4CAF50);
    });

    background.on('pointerout', () => {
      background.setStrokeStyle(2, 0x555555);
    });

    return inputField;
  }

  private showInputDialog(placeholder: string, isPassword: boolean, callback: (value: string) => void) {
    // 创建模态背景
    const modalBg = this.add.rectangle(this.scale.width / 2, this.scale.height / 2, this.scale.width, this.scale.height, 0x000000, 0.7);
    modalBg.setInteractive();

    // 创建对话框
    const dialog = this.add.rectangle(this.scale.width / 2, this.scale.height / 2, 400, 200, 0x2c3e50);
    dialog.setStrokeStyle(2, 0x34495e);

    // 标题
    const title = this.add.text(this.scale.width / 2, this.scale.height / 2 - 60, placeholder, {
      fontSize: '18px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    // 提示文本
    const hint = this.add.text(this.scale.width / 2, this.scale.height / 2 - 30, '请在下方输入框中输入内容', {
      fontSize: '14px',
      color: '#bdc3c7'
    }).setOrigin(0.5);

    // 创建HTML输入框
    const input = document.createElement('input');
    input.type = isPassword ? 'password' : 'text';
    input.placeholder = placeholder;
    input.style.position = 'fixed';
    input.style.left = '50%';
    input.style.top = '50%';
    input.style.transform = 'translate(-50%, -50%)';
    input.style.width = '300px';
    input.style.height = '40px';
    input.style.fontSize = '16px';
    input.style.padding = '10px';
    input.style.border = '2px solid #34495e';
    input.style.borderRadius = '5px';
    input.style.backgroundColor = '#333333';
    input.style.color = '#ffffff';
    input.style.zIndex = '10000';
    document.body.appendChild(input);

    // 确认按钮
    const confirmBtn = this.createButton(this.scale.width / 2 - 60, this.scale.height / 2 + 50, '确认', () => {
      const value = input.value;
      document.body.removeChild(input);
      modalBg.destroy();
      dialog.destroy();
      title.destroy();
      hint.destroy();
      confirmBtn.destroy();
      cancelBtn.destroy();
      callback(value);
    }, 100, 35);

    // 取消按钮
    const cancelBtn = this.createButton(this.scale.width / 2 + 60, this.scale.height / 2 + 50, '取消', () => {
      document.body.removeChild(input);
      modalBg.destroy();
      dialog.destroy();
      title.destroy();
      hint.destroy();
      confirmBtn.destroy();
      cancelBtn.destroy();
    }, 100, 35);

    // 自动聚焦并选中
    setTimeout(() => {
      input.focus();
      input.select();
    }, 100);

    // 回车确认
    input.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        confirmBtn.emit('pointerdown');
      } else if (e.key === 'Escape') {
        cancelBtn.emit('pointerdown');
      }
    });
  }

  private createButton(x: number, y: number, text: string, callback: () => void): Phaser.GameObjects.Container {
    const button = this.add.container(x, y);
    
    const background = this.add.rectangle(0, 0, 120, 40, 0x4CAF50);
    background.setStrokeStyle(2, 0x45a049);
    
    const buttonText = this.add.text(0, 0, text, {
      fontSize: '16px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    button.add([background, buttonText]);
    button.setSize(120, 40);
    button.setInteractive({ useHandCursor: true });

    button.on('pointerdown', callback);
    
    button.on('pointerover', () => {
      background.setFillStyle(0x66BB6A);
    });

    button.on('pointerout', () => {
      background.setFillStyle(0x4CAF50);
    });

    return button;
  }

  private showLoginForm() {
    this.loginForm.setVisible(true);
    this.registerForm.setVisible(false);
    this.isLoginMode = true;
  }

  private showRegisterForm() {
    this.loginForm.setVisible(false);
    this.registerForm.setVisible(true);
    this.isLoginMode = false;
  }

  private toggleMode() {
    if (this.isLoginMode) {
      this.showRegisterForm();
    } else {
      this.showLoginForm();
    }
  }

  private async handleLogin(username: string, password: string) {
    if (!username || !password) {
      this.showError('请输入用户名和密码');
      return;
    }

    this.showLoading('正在登录...');
    this.hideError();

    try {
      const result = await this.networkManager.login(username, password);
      if (result.success) {
        console.log('登录成功:', result.user);
        this.showLoading('登录成功，正在跳转...');

        // 延迟一下再跳转，让用户看到成功消息
        this.time.delayedCall(1000, () => {
          this.scene.start('LobbyScene');
        });
      } else {
        this.hideLoading();
        this.showError(result.message || '登录失败');
      }
    } catch (error) {
      console.error('登录错误:', error);
      this.hideLoading();
      this.showError('网络连接错误，请检查网络设置');
    }
  }

  private async handleRegister(username: string, password: string) {
    if (!username || !password) {
      this.showError('请输入用户名和密码');
      return;
    }

    // 简单的用户名验证
    if (username.length < 3) {
      this.showError('用户名至少需要3个字符');
      return;
    }

    if (password.length < 6) {
      this.showError('密码至少需要6个字符');
      return;
    }

    this.showLoading('正在注册...');
    this.hideError();

    try {
      const result = await this.networkManager.register(username, password);
      if (result.success) {
        console.log('注册成功:', result.user);
        this.showLoading('注册成功，正在跳转...');

        // 延迟一下再跳转，让用户看到成功消息
        this.time.delayedCall(1000, () => {
          this.scene.start('LobbyScene');
        });
      } else {
        this.hideLoading();
        this.showError(result.message || '注册失败');
      }
    } catch (error) {
      console.error('注册错误:', error);
      this.hideLoading();
      this.showError('网络连接错误，请检查网络设置');
    }
  }

  private showLoading(message: string) {
    this.loadingText.setText(message);
    this.loadingText.setVisible(true);
  }

  private hideLoading() {
    this.loadingText.setVisible(false);
  }

  private showError(message: string) {
    this.errorText.setText(message);
    this.errorText.setVisible(true);

    // 5秒后自动隐藏错误消息
    this.time.delayedCall(5000, () => {
      this.hideError();
    });
  }

  private hideError() {
    this.errorText.setVisible(false);
  }
}
