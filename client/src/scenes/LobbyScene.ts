import Phaser from 'phaser';
import { NetworkManager } from '../network/NetworkManager';

export class LobbyScene extends Phaser.Scene {
  private networkManager!: NetworkManager;
  private roomList: any[] = [];
  private roomListContainer!: Phaser.GameObjects.Container;
  private userInfoText!: Phaser.GameObjects.Text;
  private refreshButton!: Phaser.GameObjects.Container;
  private loadingText!: Phaser.GameObjects.Text;
  private errorText!: Phaser.GameObjects.Text;

  constructor() {
    super({ key: 'LobbyScene' });
  }

  create() {
    this.networkManager = this.registry.get('networkManager');
    
    // 检查登录状态
    if (!this.networkManager.isLoggedIn()) {
      this.scene.start('LoginScene');
      return;
    }

    this.createBackground();
    this.createHeader();
    this.createUserInfo();
    this.createActionButtons();
    this.createRoomList();
    this.createStatusTexts();

    // 初始加载房间列表
    this.refreshRoomList();
  }

  private createBackground() {
    const graphics = this.add.graphics();
    graphics.fillGradientStyle(0x1a1a2e, 0x16213e, 0x0f3460, 0x533483, 1);
    graphics.fillRect(0, 0, this.scale.width, this.scale.height);
  }

  private createHeader() {
    const title = this.add.text(this.scale.width / 2, 50, '游戏大厅', {
      fontSize: '36px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);
  }

  private createUserInfo() {
    const user = this.networkManager.getCurrentUser();
    this.userInfoText = this.add.text(20, 20, `欢迎, ${user?.username || '玩家'}`, {
      fontSize: '16px',
      color: '#ffffff'
    });

    // 登出按钮
    const logoutButton = this.createButton(this.scale.width - 80, 30, '登出', () => {
      this.handleLogout();
    }, 60, 30);
  }

  private createActionButtons() {
    const buttonY = 120;
    const buttonSpacing = 150;
    const startX = this.scale.width / 2 - buttonSpacing;

    // 创建房间按钮
    this.createButton(startX, buttonY, '创建房间', () => {
      this.handleCreateRoom();
    });

    // 快速匹配按钮
    this.createButton(startX + buttonSpacing, buttonY, '快速匹配', () => {
      this.handleQuickMatch();
    });

    // 刷新按钮
    this.refreshButton = this.createButton(startX + buttonSpacing * 2, buttonY, '刷新列表', () => {
      this.refreshRoomList();
    });
  }

  private createRoomList() {
    // 房间列表标题
    this.add.text(50, 180, '房间列表', {
      fontSize: '24px',
      color: '#ffffff',
      fontStyle: 'bold'
    });

    // 房间列表容器
    this.roomListContainer = this.add.container(0, 220);
    
    // 创建滚动区域背景
    const listBackground = this.add.rectangle(
      this.scale.width / 2, 
      this.scale.height / 2 + 50, 
      this.scale.width - 100, 
      this.scale.height - 300, 
      0x2c3e50, 
      0.8
    );
    listBackground.setStrokeStyle(2, 0x34495e);
  }

  private createButton(x: number, y: number, text: string, callback: () => void, width = 120, height = 40): Phaser.GameObjects.Container {
    const button = this.add.container(x, y);
    
    const background = this.add.rectangle(0, 0, width, height, 0x4CAF50);
    background.setStrokeStyle(2, 0x45a049);
    
    const buttonText = this.add.text(0, 0, text, {
      fontSize: '14px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    button.add([background, buttonText]);
    button.setSize(width, height);
    button.setInteractive({ useHandCursor: true });

    button.on('pointerdown', callback);
    
    button.on('pointerover', () => {
      background.setFillStyle(0x66BB6A);
    });

    button.on('pointerout', () => {
      background.setFillStyle(0x4CAF50);
    });

    return button;
  }

  private createRoomItem(room: any, index: number) {
    const itemHeight = 60;
    const y = index * (itemHeight + 10);
    
    const container = this.add.container(60, y);
    
    // 房间背景
    const background = this.add.rectangle(
      (this.scale.width - 120) / 2, 
      itemHeight / 2, 
      this.scale.width - 120, 
      itemHeight, 
      0x34495e
    );
    background.setStrokeStyle(1, 0x5a6c7d);

    // 房间信息
    const roomName = this.add.text(20, 15, room.name, {
      fontSize: '16px',
      color: '#ffffff',
      fontStyle: 'bold'
    });

    const roomInfo = this.add.text(20, 35, 
      `玩家: ${room.playerCount}/${room.maxPlayers} | 状态: ${this.getGameStateText(room.gameState)}`, {
      fontSize: '12px',
      color: '#bdc3c7'
    });

    // 加入按钮
    const joinButton = this.add.rectangle(
      this.scale.width - 200, 
      itemHeight / 2, 
      80, 
      30, 
      room.playerCount >= room.maxPlayers ? 0x95a5a6 : 0xe74c3c
    );
    joinButton.setStrokeStyle(1, 0xc0392b);

    const joinText = this.add.text(
      this.scale.width - 200, 
      itemHeight / 2, 
      room.playerCount >= room.maxPlayers ? '已满' : '加入', {
      fontSize: '12px',
      color: '#ffffff'
    }).setOrigin(0.5);

    container.add([background, roomName, roomInfo, joinButton, joinText]);

    // 如果房间未满，添加点击事件
    if (room.playerCount < room.maxPlayers) {
      joinButton.setInteractive({ useHandCursor: true });
      joinButton.on('pointerdown', () => {
        this.handleJoinRoom(room.id);
      });

      joinButton.on('pointerover', () => {
        joinButton.setFillStyle(0xc0392b);
      });

      joinButton.on('pointerout', () => {
        joinButton.setFillStyle(0xe74c3c);
      });
    }

    this.roomListContainer.add(container);
  }

  private getGameStateText(gameState: string): string {
    switch (gameState) {
      case 'waiting': return '等待中';
      case 'starting': return '即将开始';
      case 'playing': return '游戏中';
      case 'finished': return '已结束';
      default: return '未知';
    }
  }

  private async refreshRoomList() {
    try {
      const response = await fetch('http://localhost:2567/rooms/list');
      const result = await response.json();
      
      if (result.success) {
        this.roomList = result.data;
        this.updateRoomListDisplay();
      } else {
        console.error('获取房间列表失败:', result.message);
      }
    } catch (error) {
      console.error('刷新房间列表错误:', error);
    }
  }

  private updateRoomListDisplay() {
    // 清除现有房间项
    this.roomListContainer.removeAll(true);
    
    if (this.roomList.length === 0) {
      const noRoomsText = this.add.text(
        this.scale.width / 2, 
        this.scale.height / 2, 
        '暂无可用房间\n点击"创建房间"开始游戏', {
        fontSize: '18px',
        color: '#7f8c8d',
        align: 'center'
      }).setOrigin(0.5);
      
      this.roomListContainer.add(noRoomsText);
    } else {
      this.roomList.forEach((room, index) => {
        this.createRoomItem(room, index);
      });
    }
  }

  private handleCreateRoom() {
    const roomName = prompt('请输入房间名称:') || `${this.networkManager.getCurrentUser()?.username}的房间`;
    
    if (roomName) {
      this.createRoom(roomName);
    }
  }

  private async createRoom(roomName: string) {
    try {
      this.showLoadingMessage('正在创建房间...');

      const room = await this.networkManager.joinRoom('game_room', {
        name: roomName,
        create: true
      });

      this.hideLoadingMessage();

      if (room) {
        console.log('创建房间成功:', room.id);
        this.scene.start('GameScene', { room });
      } else {
        this.showErrorMessage('创建房间失败，请稍后重试');
      }
    } catch (error) {
      console.error('创建房间错误:', error);
      this.hideLoadingMessage();
      this.showErrorMessage('创建房间失败：' + (error as Error).message);
    }
  }

  private async handleJoinRoom(roomId: string) {
    try {
      // 显示加载状态
      this.showLoadingMessage('正在加入房间...');

      const room = await this.networkManager.joinSpecificRoom(roomId);

      if (room) {
        console.log('加入房间成功:', room.id);
        this.hideLoadingMessage();
        this.scene.start('GameScene', { room });
      } else {
        this.hideLoadingMessage();
        this.showErrorMessage('加入房间失败，房间可能已满或不存在');
      }
    } catch (error) {
      console.error('加入房间错误:', error);
      this.hideLoadingMessage();
      this.showErrorMessage('加入房间失败：' + (error as Error).message);
    }
  }

  private async handleQuickMatch() {
    try {
      this.showLoadingMessage('正在寻找匹配...');

      // 首先尝试找到可用房间
      const response = await fetch('http://localhost:2567/rooms/available');
      const result = await response.json();

      if (result.success && result.data.length > 0) {
        // 加入第一个可用房间
        const availableRoom = result.data[0];
        this.hideLoadingMessage();
        this.handleJoinRoom(availableRoom.id);
      } else {
        // 没有可用房间，创建新房间
        this.hideLoadingMessage();
        const roomName = `${this.networkManager.getCurrentUser()?.username}的快速匹配房间`;
        this.createRoom(roomName);
      }
    } catch (error) {
      console.error('快速匹配错误:', error);
      this.hideLoadingMessage();
      this.showErrorMessage('快速匹配失败，正在创建新房间...');

      // 作为备选方案，创建新房间
      setTimeout(() => {
        const roomName = `${this.networkManager.getCurrentUser()?.username}的房间`;
        this.createRoom(roomName);
      }, 1000);
    }
  }

  private handleLogout() {
    this.networkManager.logout();
    this.scene.start('LoginScene');
  }

  private createStatusTexts() {
    // 加载文本
    this.loadingText = this.add.text(this.scale.width / 2, this.scale.height / 2, '', {
      fontSize: '16px',
      color: '#f39c12',
      fontStyle: 'bold',
      backgroundColor: '#2c3e50',
      padding: { x: 15, y: 10 }
    }).setOrigin(0.5).setVisible(false);

    // 错误文本
    this.errorText = this.add.text(this.scale.width / 2, this.scale.height / 2 + 50, '', {
      fontSize: '14px',
      color: '#e74c3c',
      fontStyle: 'bold',
      backgroundColor: '#2c3e50',
      padding: { x: 15, y: 10 }
    }).setOrigin(0.5).setVisible(false);
  }

  private showLoadingMessage(message: string) {
    this.loadingText.setText(message);
    this.loadingText.setVisible(true);
  }

  private hideLoadingMessage() {
    this.loadingText.setVisible(false);
  }

  private showErrorMessage(message: string) {
    this.errorText.setText(message);
    this.errorText.setVisible(true);

    // 5秒后自动隐藏
    this.time.delayedCall(5000, () => {
      this.errorText.setVisible(false);
    });
  }
}
