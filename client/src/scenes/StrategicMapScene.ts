import Phaser from 'phaser';
import { GameState, HexTile, Position, TerrainType, WeatherType, Player, Army } from '@multiplayer-game/shared';
import { NetworkManager } from '../network/NetworkManager';

/**
 * 战略地图场景
 * 显示六边形地图，处理玩家的战略层操作
 */
export class StrategicMapScene extends Phaser.Scene {
  private networkManager!: NetworkManager;
  private hexTiles: Map<string, Phaser.GameObjects.Container> = new Map();
  private selectedArmy: Army | null = null;
  private mapContainer!: Phaser.GameObjects.Container;
  private uiContainer!: Phaser.GameObjects.Container;
  
  // 地图参数
  private readonly HEX_SIZE = 40;
  private readonly HEX_WIDTH = this.HEX_SIZE * 2;
  private readonly HEX_HEIGHT = this.HEX_SIZE * Math.sqrt(3);
  
  // UI元素
  private turnText!: Phaser.GameObjects.Text;
  private weatherText!: Phaser.GameObjects.Text;
  private resourcesText!: Phaser.GameObjects.Text;
  private phaseText!: Phaser.GameObjects.Text;

  constructor() {
    super({ key: 'StrategicMapScene' });
  }

  init(data: any) {
    this.networkManager = data.networkManager;
  }

  create() {
    console.log('战略地图场景创建');
    
    // 创建容器
    this.mapContainer = this.add.container(0, 0);
    this.uiContainer = this.add.container(0, 0);
    
    // 创建UI
    this.createUI();
    
    // 设置相机
    this.setupCamera();
    
    // 设置输入
    this.setupInput();
    
    // 监听网络事件
    this.setupNetworkEvents();
    
    // 请求游戏状态
    this.networkManager.sendMessage('get_game_state');
  }

  /**
   * 创建UI界面
   */
  private createUI(): void {
    // 顶部信息栏
    const topBar = this.add.rectangle(this.scale.width / 2, 30, this.scale.width, 60, 0x2c3e50, 0.9);
    
    // 回合信息
    this.turnText = this.add.text(20, 20, '回合: 1', {
      fontSize: '16px',
      color: '#ffffff'
    });
    
    // 阶段信息
    this.phaseText = this.add.text(150, 20, '阶段: 资源收集', {
      fontSize: '16px',
      color: '#ffffff'
    });
    
    // 天气信息
    this.weatherText = this.add.text(300, 20, '天气: 晴天', {
      fontSize: '16px',
      color: '#ffffff'
    });
    
    // 资源信息
    this.resourcesText = this.add.text(this.scale.width - 200, 20, '金币: 50 | 人口: 100', {
      fontSize: '16px',
      color: '#ffffff'
    }).setOrigin(1, 0);
    
    // 底部控制栏
    const bottomBar = this.add.rectangle(this.scale.width / 2, this.scale.height - 30, this.scale.width, 60, 0x34495e, 0.9);
    
    // 结束回合按钮
    const endTurnButton = this.add.rectangle(this.scale.width - 100, this.scale.height - 30, 120, 40, 0xe74c3c);
    const endTurnText = this.add.text(this.scale.width - 100, this.scale.height - 30, '结束回合', {
      fontSize: '14px',
      color: '#ffffff'
    }).setOrigin(0.5);
    
    endTurnButton.setInteractive({ useHandCursor: true });
    endTurnButton.on('pointerdown', () => {
      this.endTurn();
    });
    
    // 添加到UI容器
    this.uiContainer.add([topBar, this.turnText, this.phaseText, this.weatherText, this.resourcesText, bottomBar, endTurnButton, endTurnText]);
  }

  /**
   * 设置相机
   */
  private setupCamera(): void {
    // 设置相机边界
    this.cameras.main.setBounds(-500, -500, 2000, 1500);
    
    // 启用相机拖拽
    this.cameras.main.setZoom(1);
  }

  /**
   * 设置输入控制
   */
  private setupInput(): void {
    // 鼠标拖拽地图
    this.input.on('pointerdown', (pointer: Phaser.Input.Pointer) => {
      if (pointer.rightButtonDown()) {
        this.cameras.main.startFollow(pointer, false, 0.1, 0.1);
      }
    });
    
    this.input.on('pointerup', (pointer: Phaser.Input.Pointer) => {
      if (pointer.rightButtonReleased()) {
        this.cameras.main.stopFollow();
      }
    });
    
    // 鼠标滚轮缩放
    this.input.on('wheel', (pointer: Phaser.Input.Pointer, gameObjects: any, deltaX: number, deltaY: number) => {
      const zoom = this.cameras.main.zoom;
      const newZoom = Phaser.Math.Clamp(zoom - deltaY * 0.001, 0.5, 2);
      this.cameras.main.setZoom(newZoom);
    });
  }

  /**
   * 设置网络事件监听
   */
  private setupNetworkEvents(): void {
    // 监听游戏状态更新
    this.networkManager.onMessage('game_state_update', (data) => {
      this.updateGameState(data);
    });
    
    // 监听地图更新
    this.networkManager.onMessage('map_update', (data) => {
      this.updateMap(data.tiles);
    });
    
    // 监听回合开始
    this.networkManager.onMessage('turn_start', (data) => {
      this.onTurnStart(data);
    });
  }

  /**
   * 更新游戏状态
   */
  private updateGameState(gameState: any): void {
    // 更新UI
    this.turnText.setText(`回合: ${gameState.currentTurn}`);
    this.phaseText.setText(`阶段: ${this.getPhaseText(gameState.strategicPhase)}`);
    this.weatherText.setText(`天气: ${this.getWeatherText(gameState.weather.current)}`);
    
    // 更新玩家资源
    const currentPlayer = gameState.players.find((p: Player) => p.id === this.networkManager.getCurrentUser()?.id);
    if (currentPlayer) {
      this.resourcesText.setText(`金币: ${currentPlayer.resources.gold} | 人口: ${currentPlayer.resources.population}`);
    }
    
    // 更新地图
    if (gameState.map) {
      this.updateMap(gameState.map);
    }
  }

  /**
   * 更新地图显示
   */
  private updateMap(tiles: HexTile[]): void {
    // 清除现有地图
    this.mapContainer.removeAll(true);
    this.hexTiles.clear();
    
    // 绘制地块
    for (const tile of tiles) {
      this.createHexTile(tile);
    }
  }

  /**
   * 创建六边形地块
   */
  private createHexTile(tile: HexTile): void {
    const pixelPos = this.hexToPixel(tile.position);
    const container = this.add.container(pixelPos.x, pixelPos.y);
    
    // 地形颜色
    const terrainColor = this.getTerrainColor(tile.terrainType);
    
    // 绘制六边形
    const hex = this.add.polygon(0, 0, this.getHexPoints(), terrainColor);
    hex.setStrokeStyle(2, 0x34495e);
    
    // 地形图标
    const terrainIcon = this.add.text(0, -10, this.getTerrainIcon(tile.terrainType), {
      fontSize: '20px'
    }).setOrigin(0.5);
    
    // 资源信息
    const resourceText = this.add.text(0, 10, `${tile.resources.gold}G ${tile.resources.population}P`, {
      fontSize: '10px',
      color: '#ffffff'
    }).setOrigin(0.5);
    
    // 所有者标识
    if (tile.ownerId) {
      const ownerCircle = this.add.circle(-15, -15, 8, 0xe74c3c);
      container.add(ownerCircle);
    }
    
    // 军队标识
    if (tile.units && tile.units.length > 0) {
      const armyIcon = this.add.text(15, -15, '⚔️', {
        fontSize: '16px'
      }).setOrigin(0.5);
      container.add(armyIcon);
    }
    
    container.add([hex, terrainIcon, resourceText]);
    
    // 设置交互
    hex.setInteractive();
    hex.on('pointerdown', () => {
      this.onHexClick(tile);
    });
    
    hex.on('pointerover', () => {
      hex.setStrokeStyle(3, 0xf39c12);
      this.showTileInfo(tile);
    });
    
    hex.on('pointerout', () => {
      hex.setStrokeStyle(2, 0x34495e);
      this.hideTileInfo();
    });
    
    this.mapContainer.add(container);
    this.hexTiles.set(tile.id, container);
  }

  /**
   * 六边形坐标转像素坐标
   */
  private hexToPixel(hexPos: Position): Position {
    const x = this.HEX_SIZE * (3/2 * hexPos.x);
    const y = this.HEX_SIZE * (Math.sqrt(3)/2 * hexPos.x + Math.sqrt(3) * hexPos.y);
    return { x: x + 400, y: y + 300 }; // 偏移到屏幕中心
  }

  /**
   * 获取六边形顶点
   */
  private getHexPoints(): number[] {
    const points: number[] = [];
    for (let i = 0; i < 6; i++) {
      const angle = (Math.PI / 3) * i;
      const x = this.HEX_SIZE * Math.cos(angle);
      const y = this.HEX_SIZE * Math.sin(angle);
      points.push(x, y);
    }
    return points;
  }

  /**
   * 获取地形颜色
   */
  private getTerrainColor(terrain: TerrainType): number {
    switch (terrain) {
      case TerrainType.PLAIN: return 0x90EE90;
      case TerrainType.MOUNTAIN: return 0x8B4513;
      case TerrainType.FOREST: return 0x228B22;
      case TerrainType.RIVER: return 0x4169E1;
      case TerrainType.DESERT: return 0xF4A460;
      case TerrainType.CRYSTAL: return 0x9370DB;
      case TerrainType.SCORCHED: return 0x2F4F4F;
      default: return 0x808080;
    }
  }

  /**
   * 获取地形图标
   */
  private getTerrainIcon(terrain: TerrainType): string {
    switch (terrain) {
      case TerrainType.PLAIN: return '🌾';
      case TerrainType.MOUNTAIN: return '⛰️';
      case TerrainType.FOREST: return '🌲';
      case TerrainType.RIVER: return '🌊';
      case TerrainType.DESERT: return '🏜️';
      case TerrainType.CRYSTAL: return '💎';
      case TerrainType.SCORCHED: return '🔥';
      default: return '❓';
    }
  }

  /**
   * 处理地块点击
   */
  private onHexClick(tile: HexTile): void {
    console.log('点击地块:', tile);
    
    if (this.selectedArmy) {
      // 尝试移动军队
      this.moveArmy(this.selectedArmy, tile.position);
    } else {
      // 选择地块上的军队
      if (tile.units && tile.units.length > 0) {
        const currentPlayer = this.networkManager.getCurrentUser();
        const playerArmy = tile.units.find(army => army.ownerId === currentPlayer?.id);
        if (playerArmy) {
          this.selectArmy(playerArmy);
        }
      }
    }
  }

  /**
   * 选择军队
   */
  private selectArmy(army: Army): void {
    this.selectedArmy = army;
    console.log('选择军队:', army.name);
    
    // 高亮显示可移动的地块
    this.highlightMovableHexes(army.position);
  }

  /**
   * 高亮可移动地块
   */
  private highlightMovableHexes(position: Position): void {
    // TODO: 实现高亮逻辑
  }

  /**
   * 移动军队
   */
  private moveArmy(army: Army, targetPosition: Position): void {
    this.networkManager.sendMessage('move_army', {
      armyId: army.id,
      targetPosition
    });
    
    this.selectedArmy = null;
  }

  /**
   * 显示地块信息
   */
  private showTileInfo(tile: HexTile): void {
    // TODO: 显示详细的地块信息面板
  }

  /**
   * 隐藏地块信息
   */
  private hideTileInfo(): void {
    // TODO: 隐藏信息面板
  }

  /**
   * 结束回合
   */
  private endTurn(): void {
    this.networkManager.sendMessage('end_turn');
  }

  /**
   * 回合开始处理
   */
  private onTurnStart(data: any): void {
    console.log('新回合开始:', data);
  }

  /**
   * 获取阶段文本
   */
  private getPhaseText(phase: string): string {
    switch (phase) {
      case 'resource_collection': return '资源收集';
      case 'player_action': return '玩家行动';
      case 'event_processing': return '事件处理';
      case 'turn_end': return '回合结束';
      default: return phase;
    }
  }

  /**
   * 获取天气文本
   */
  private getWeatherText(weather: WeatherType): string {
    switch (weather) {
      case WeatherType.CLEAR: return '晴天';
      case WeatherType.RAIN: return '暴雨';
      case WeatherType.SANDSTORM: return '沙暴';
      case WeatherType.AURORA: return '极光';
      case WeatherType.WINTER: return '严冬';
      case WeatherType.THUNDER: return '雷暴';
      default: return weather;
    }
  }
}
