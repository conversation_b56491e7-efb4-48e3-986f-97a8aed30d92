/**
 * <AUTHOR> <<EMAIL>>
 * @copyright    2013-2025 Phaser Studio Inc.
 * @license      {@link https://opensource.org/licenses/MIT|MIT License}
 */

/**
 * @namespace Phaser.GameObjects.Components
 */

module.exports = {

    Alpha: require('./Alpha'),
    AlphaSingle: require('./AlphaSingle'),
    BlendMode: require('./BlendMode'),
    ComputedSize: require('./ComputedSize'),
    Crop: require('./Crop'),
    Depth: require('./Depth'),
    Flip: require('./Flip'),
    FX: require('./FX'),
    GetBounds: require('./GetBounds'),
    Mask: require('./Mask'),
    Origin: require('./Origin'),
    PathFollower: require('./PathFollower'),
    Pipeline: require('./Pipeline'),
    PostPipeline: require('./PostPipeline'),
    ScrollFactor: require('./ScrollFactor'),
    Size: require('./Size'),
    Texture: require('./Texture'),
    TextureCrop: require('./TextureCrop'),
    Tint: require('./Tint'),
    ToJSON: require('./ToJSON'),
    Transform: require('./Transform'),
    TransformMatrix: require('./TransformMatrix'),
    Visible: require('./Visible')

};
