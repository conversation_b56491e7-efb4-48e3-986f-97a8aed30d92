/**
 * <AUTHOR> <<EMAIL>>
 * @copyright    2013-2025 Phaser Studio Inc.
 * @license      {@link https://opensource.org/licenses/MIT|MIT License}
 */

/**
 * The Game Object Destroy Event.
 *
 * This event is dispatched when a Game Object instance is being destroyed.
 *
 * Listen for it on a Game Object instance using `GameObject.on('destroy', listener)`.
 *
 * @event Phaser.GameObjects.Events#DESTROY
 * @type {string}
 * @since 3.0.0
 *
 * @param {Phaser.GameObjects.GameObject} gameObject - The Game Object which is being destroyed.
 * @param {boolean} fromScene - `True` if this Game Object is being destroyed by the Scene, `false` if not.
 */
module.exports = 'destroy';
