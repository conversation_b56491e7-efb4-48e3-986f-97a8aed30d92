/**
 * <AUTHOR> <<EMAIL>>
 * @copyright    2013-2025 Phaser Studio Inc.
 * @license      {@link https://opensource.org/licenses/MIT|MIT License}
 */

var RETRO_FONT_CONST = {

    /**
     * Text Set 1 =  !\"#$%&'()*+,-./**********:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_`abcdefghijklmnopqrstuvwxyz{|}~
     * 
     * @name Phaser.GameObjects.RetroFont.TEXT_SET1
     * @type {string}
     * @since 3.6.0
     */
    TEXT_SET1: ' !"#$%&\'()*+,-./**********:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_`abcdefghijklmnopqrstuvwxyz{|}~',

    /**
     * Text Set 2 =  !"#$%&'()*+,-./**********:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ
     * 
     * @name Phaser.GameObjects.RetroFont.TEXT_SET2
     * @type {string}
     * @since 3.6.0
     */
    TEXT_SET2: ' !"#$%&\'()*+,-./**********:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ',

    /**
     * Text Set 3 = ABCDEFGHIJKLMNOPQRSTUVWXYZ********** 
     * 
     * @name Phaser.GameObjects.RetroFont.TEXT_SET3
     * @type {string}
     * @since 3.6.0
     */
    TEXT_SET3: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ********** ',

    /**
     * Text Set 4 = ABCDEFGHIJKLMNOPQRSTUVWXYZ **********
     * 
     * @name Phaser.GameObjects.RetroFont.TEXT_SET4
     * @type {string}
     * @since 3.6.0
     */
    TEXT_SET4: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ **********',

    /**
     * Text Set 5 = ABCDEFGHIJKLMNOPQRSTUVWXYZ.,/() '!?-*:**********
     * 
     * @name Phaser.GameObjects.RetroFont.TEXT_SET5
     * @type {string}
     * @since 3.6.0
     */
    TEXT_SET5: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ.,/() \'!?-*:**********',

    /**
     * Text Set 6 = ABCDEFGHIJKLMNOPQRSTUVWXYZ!?:;**********"(),-.' 
     * 
     * @name Phaser.GameObjects.RetroFont.TEXT_SET6
     * @type {string}
     * @since 3.6.0
     */
    TEXT_SET6: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ!?:;**********"(),-.\' ',

    /**
     * Text Set 7 = AGMSY+:4BHNTZ!;5CIOU.?06DJPV,(17EKQW")28FLRX-'39
     * 
     * @name Phaser.GameObjects.RetroFont.TEXT_SET7
     * @type {string}
     * @since 3.6.0
     */
    TEXT_SET7: 'AGMSY+:4BHNTZ!;5CIOU.?06DJPV,(17EKQW")28FLRX-\'39',

    /**
     * Text Set 8 = ********** .ABCDEFGHIJKLMNOPQRSTUVWXYZ
     * 
     * @name Phaser.GameObjects.RetroFont.TEXT_SET8
     * @type {string}
     * @since 3.6.0
     */
    TEXT_SET8: '********** .ABCDEFGHIJKLMNOPQRSTUVWXYZ',

    /**
     * Text Set 9 = ABCDEFGHIJKLMNOPQRSTUVWXYZ()-**********.:,'"?!
     * 
     * @name Phaser.GameObjects.RetroFont.TEXT_SET9
     * @type {string}
     * @since 3.6.0
     */
    TEXT_SET9: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ()-**********.:,\'"?!',

    /**
     * Text Set 10 = ABCDEFGHIJKLMNOPQRSTUVWXYZ
     * 
     * @name Phaser.GameObjects.RetroFont.TEXT_SET10
     * @type {string}
     * @since 3.6.0
     */
    TEXT_SET10: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',

    /**
     * Text Set 11 = ABCDEFGHIJKLMNOPQRSTUVWXYZ.,"-+!?()':;**********
     * 
     * @name Phaser.GameObjects.RetroFont.TEXT_SET11
     * @since 3.6.0
     * @type {string}
     */
    TEXT_SET11: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ.,"-+!?()\':;**********'

};

module.exports = RETRO_FONT_CONST;
