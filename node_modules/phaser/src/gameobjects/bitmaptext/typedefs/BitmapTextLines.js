/**
 * Details about the line data in the `BitmapTextSize` object.
 *
 * @typedef {object} Phaser.Types.GameObjects.BitmapText.BitmapTextLines
 * @since 3.50.0
 *
 * @property {number} shortest - The width of the shortest line of text.
 * @property {number} longest - The width of the longest line of text.
 * @property {number} height - The height of a line of text.
 * @property {number[]} lengths - An array where each entry contains the length of that line of text.
 */
