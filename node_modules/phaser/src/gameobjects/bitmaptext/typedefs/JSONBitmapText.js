/**
 * @typedef {object} Phaser.Types.GameObjects.BitmapText.JSONBitmapText
 * @extends {Phaser.Types.GameObjects.JSONGameObject}
 * @since 3.0.0
 *
 * @property {string} font - The name of the font.
 * @property {string} text - The text that this Bitmap Text displays.
 * @property {number} fontSize - The size of the font.
 * @property {number} letterSpacing - Adds / Removes spacing between characters.
 * @property {number} lineSpacing - Adds / Removes spacing between lines in multi-line text.
 * @property {number} align - The alignment of the text in a multi-line BitmapText object.
 */
