/**
 * Details about a single world entry in the `BitmapTextSize` object words array.
 *
 * @typedef {object} Phaser.Types.GameObjects.BitmapText.BitmapTextWord
 * @since 3.50.0
 *
 * @property {number} x - The x position of the word in the BitmapText.
 * @property {number} y - The y position of the word in the BitmapText.
 * @property {number} w - The width of the word.
 * @property {number} h - The height of the word.
 * @property {number} i - The index of the first character of this word within the entire string. Note: this index factors in spaces, quotes, carriage-returns, etc.
 * @property {string} word - The word.
 */
