/**
 * The position and size of the Bitmap Text in local space, taking just the font size into account.
 *
 * @typedef {object} Phaser.Types.GameObjects.BitmapText.LocalBitmapTextSize
 * @since 3.0.0
 *
 * @property {number} x - The x position of the BitmapText.
 * @property {number} y - The y position of the BitmapText.
 * @property {number} width - The width of the BitmapText.
 * @property {number} height - The height of the BitmapText.
 */
