/**
 * Bitmap Font data that can be used by a BitmapText Game Object.
 *
 * @typedef {object} Phaser.Types.GameObjects.BitmapText.BitmapFontData
 * @since 3.0.0
 *
 * @property {string} font - The name of the font.
 * @property {number} size - The size of the font.
 * @property {number} lineHeight - The line height of the font.
 * @property {boolean} retroFont - Whether this font is a retro font (monospace).
 * @property {Object.<number, Phaser.Types.GameObjects.BitmapText.BitmapFontCharacterData>} chars - The character data of the font, keyed by character code. Each character datum includes a position, size, offset and more.
 */
