/**
 * @typedef {object} Phaser.Types.GameObjects.Graphics.RoundedRectRadius
 * @since 3.11.0
 *
 * @property {number} [tl=20] - Top left corner radius. Draw concave arc if this radius is negative.
 * @property {number} [tr=20] - Top right corner radius. Draw concave arc if this radius is negative.
 * @property {number} [br=20] - Bottom right corner radius. Draw concave arc if this radius is negative.
 * @property {number} [bl=20] - Bottom left corner radius. Draw concave arc if this radius is negative.
 */
