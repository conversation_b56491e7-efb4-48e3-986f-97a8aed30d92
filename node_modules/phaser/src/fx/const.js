/**
 * <AUTHOR> <<EMAIL>>
 * @copyright    2013-2025 Phaser Studio Inc.
 * @license      {@link https://opensource.org/licenses/MIT|MIT License}
 */

var FX_CONST = {

    /**
     * The Glow FX.
     *
     * @name Phaser.FX.GLOW
     * @type {number}
     * @const
     * @since 3.60.0
     */
    GLOW: 4,

    /**
     * The Shadow FX.
     *
     * @name Phaser.FX.SHADOW
     * @type {number}
     * @const
     * @since 3.60.0
     */
    SHADOW: 5,

    /**
     * The Pixelate FX.
     *
     * @name Phaser.FX.PIXELATE
     * @type {number}
     * @const
     * @since 3.60.0
     */
    PIXELATE: 6,

    /**
     * The Vignette FX.
     *
     * @name Phaser.FX.VIGNETTE
     * @type {number}
     * @const
     * @since 3.60.0
     */
    VIGNETTE: 7,

    /**
     * The Shine FX.
     *
     * @name Phaser.FX.SHINE
     * @type {number}
     * @const
     * @since 3.60.0
     */
    SHINE: 8,

    /**
     * The Blur FX.
     *
     * @name Phaser.FX.BLUR
     * @type {number}
     * @const
     * @since 3.60.0
     */
    BLUR: 9, // uses 3 shaders, slots 9, 10 and 11

    /**
     * The Gradient FX.
     *
     * @name Phaser.FX.GRADIENT
     * @type {number}
     * @const
     * @since 3.60.0
     */
    GRADIENT: 12,

    /**
     * The Bloom FX.
     *
     * @name Phaser.FX.BLOOM
     * @type {number}
     * @const
     * @since 3.60.0
     */
    BLOOM: 13,

    /**
     * The Color Matrix FX.
     *
     * @name Phaser.FX.COLOR_MATRIX
     * @type {number}
     * @const
     * @since 3.60.0
     */
    COLOR_MATRIX: 14,

    /**
     * The Circle FX.
     *
     * @name Phaser.FX.CIRCLE
     * @type {number}
     * @const
     * @since 3.60.0
     */
    CIRCLE: 15,

    /**
     * The Barrel FX.
     *
     * @name Phaser.FX.BARREL
     * @type {number}
     * @const
     * @since 3.60.0
     */
    BARREL: 16,

    /**
     * The Displacement FX.
     *
     * @name Phaser.FX.DISPLACEMENT
     * @type {number}
     * @const
     * @since 3.60.0
     */
    DISPLACEMENT: 17,

    /**
     * The Wipe FX.
     *
     * @name Phaser.FX.WIPE
     * @type {number}
     * @const
     * @since 3.60.0
     */
    WIPE: 18,

    /**
     * The Bokeh and Tilt Shift FX.
     *
     * @name Phaser.FX.BOKEH
     * @type {number}
     * @const
     * @since 3.60.0
     */
    BOKEH: 19

};

module.exports = FX_CONST;
