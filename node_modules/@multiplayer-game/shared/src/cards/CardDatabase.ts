import { 
  HeroCard, 
  SpellCard, 
  StrategicCard, 
  CardType, 
  CardRarity, 
  Faction, 
  Element, 
  UnitType, 
  UnitRole,
  SkillType,
  SpellSchool,
  GAME_CONFIG
} from '../types';

/**
 * 卡牌数据库
 * 包含所有游戏中的卡牌定义
 */

// 英雄卡数据库
export const HERO_CARDS: HeroCard[] = [
  // 蜀国英雄
  {
    id: 'hero_liu_bei',
    name: '刘备',
    type: CardType.HERO,
    rarity: CardRarity.LEGENDARY,
    cost: 5,
    description: '蜀汉昭烈帝，仁德之君',
    heroName: '刘备',
    faction: Faction.SHU,
    unitType: UnitType.INFANTRY,
    element: Element.EARTH,
    role: UnitRole.TANK,
    stats: {
      health: 120,
      energy: 100,
      attack: 80,
      armor: 25,
      strategy: 90,
      magicResist: 20,
      speed: 70
    },
    skill: {
      id: 'liu_bei_benevolence',
      name: '仁德',
      type: SkillType.ACTIVE,
      description: '为所有友军恢复生命值，并提供护甲加成',
      cooldown: 3,
      energyCost: 40,
      effects: [
        {
          type: 'heal',
          value: 30,
          target: 'ally'
        },
        {
          type: 'buff',
          value: 15,
          duration: 3,
          target: 'ally'
        }
      ]
    },
    synergies: ['君主', '仁德', '蜀汉']
  },
  {
    id: 'hero_guan_yu',
    name: '关羽',
    type: CardType.HERO,
    rarity: CardRarity.EPIC,
    cost: 4,
    description: '武圣关云长，义薄云天',
    heroName: '关羽',
    faction: Faction.SHU,
    unitType: UnitType.CAVALRY,
    element: Element.FIRE,
    role: UnitRole.WARRIOR,
    stats: {
      health: 100,
      energy: 80,
      attack: 110,
      armor: 15,
      strategy: 60,
      magicResist: 10,
      speed: 85
    },
    skill: {
      id: 'guan_yu_charge',
      name: '单骑千里',
      type: SkillType.ACTIVE,
      description: '冲锋攻击敌方后排，造成大量伤害',
      cooldown: 4,
      energyCost: 50,
      effects: [
        {
          type: 'damage',
          value: 150,
          target: 'enemy',
          range: 3
        }
      ]
    },
    synergies: ['猛将', '义士', '蜀汉']
  },
  {
    id: 'hero_zhang_fei',
    name: '张飞',
    type: CardType.HERO,
    rarity: CardRarity.RARE,
    cost: 3,
    description: '燕人张翼德，勇猛无双',
    heroName: '张飞',
    faction: Faction.SHU,
    unitType: UnitType.INFANTRY,
    element: Element.THUNDER,
    role: UnitRole.TANK,
    stats: {
      health: 130,
      energy: 70,
      attack: 95,
      armor: 30,
      strategy: 40,
      magicResist: 15,
      speed: 60
    },
    skill: {
      id: 'zhang_fei_roar',
      name: '咆哮',
      type: SkillType.ACTIVE,
      description: '嘲讽敌军并提升自身攻击力',
      cooldown: 3,
      energyCost: 30,
      effects: [
        {
          type: 'control',
          value: 2,
          duration: 2,
          target: 'enemy'
        },
        {
          type: 'buff',
          value: 25,
          duration: 3,
          target: 'self'
        }
      ]
    },
    synergies: ['猛将', '守护', '蜀汉']
  },
  
  // 魏国英雄
  {
    id: 'hero_cao_cao',
    name: '曹操',
    type: CardType.HERO,
    rarity: CardRarity.LEGENDARY,
    cost: 5,
    description: '魏武帝，治世之能臣，乱世之奸雄',
    heroName: '曹操',
    faction: Faction.WEI,
    unitType: UnitType.CHARIOT,
    element: Element.METAL,
    role: UnitRole.CONTROL,
    stats: {
      health: 110,
      energy: 120,
      attack: 85,
      armor: 20,
      strategy: 100,
      magicResist: 25,
      speed: 75
    },
    skill: {
      id: 'cao_cao_ambition',
      name: '奸雄',
      type: SkillType.PASSIVE,
      description: '每当敌军死亡时，获得能量和攻击力加成',
      effects: [
        {
          type: 'buff',
          value: 10,
          target: 'self'
        }
      ]
    },
    synergies: ['君主', '奸雄', '魏国']
  },
  {
    id: 'hero_xiahou_dun',
    name: '夏侯惇',
    type: CardType.HERO,
    rarity: CardRarity.RARE,
    cost: 3,
    description: '独眼将军，忠勇无双',
    heroName: '夏侯惇',
    faction: Faction.WEI,
    unitType: UnitType.CAVALRY,
    element: Element.FIRE,
    role: UnitRole.WARRIOR,
    stats: {
      health: 105,
      energy: 75,
      attack: 100,
      armor: 18,
      strategy: 55,
      magicResist: 12,
      speed: 80
    },
    skill: {
      id: 'xiahou_dun_fury',
      name: '拔矢啖睛',
      type: SkillType.PASSIVE,
      description: '受到伤害时反击，伤害越重反击越强',
      effects: [
        {
          type: 'damage',
          value: 80,
          target: 'enemy'
        }
      ]
    },
    synergies: ['猛将', '忠臣', '魏国']
  }
];

// 法术卡数据库
export const SPELL_CARDS: SpellCard[] = [
  {
    id: 'spell_fire_attack',
    name: '火攻',
    type: CardType.SPELL,
    rarity: CardRarity.COMMON,
    cost: 2,
    description: '对敌方区域造成火焰伤害',
    school: SpellSchool.MILITARY,
    energyCost: 30,
    effects: [
      {
        type: 'damage',
        value: 60,
        target: 'area',
        range: 2
      }
    ],
    targetType: 'area'
  },
  {
    id: 'spell_healing_spring',
    name: '甘露',
    type: CardType.SPELL,
    rarity: CardRarity.COMMON,
    cost: 2,
    description: '为友军恢复生命值',
    school: SpellSchool.MYSTIC,
    energyCost: 25,
    effects: [
      {
        type: 'heal',
        value: 50,
        target: 'ally'
      }
    ],
    targetType: 'unit'
  },
  {
    id: 'spell_lightning_storm',
    name: '雷暴',
    type: CardType.SPELL,
    rarity: CardRarity.EPIC,
    cost: 4,
    description: '召唤雷暴攻击所有敌军',
    school: SpellSchool.MYSTIC,
    energyCost: 60,
    effects: [
      {
        type: 'damage',
        value: 40,
        target: 'enemy'
      },
      {
        type: 'control',
        value: 1,
        duration: 1,
        target: 'enemy'
      }
    ],
    targetType: 'global'
  }
];

// 战略指令卡数据库
export const STRATEGIC_CARDS: StrategicCard[] = [
  {
    id: 'strategic_forced_march',
    name: '强行军',
    type: CardType.STRATEGIC,
    rarity: CardRarity.COMMON,
    cost: 2,
    description: '增加军队移动力，但降低士气',
    combatEffect: [
      {
        type: 'buff',
        value: 20,
        target: 'ally',
        duration: 1
      }
    ],
    globalEffect: {
      type: 'movement_block',
      description: '军队可以额外移动一格',
      value: 1,
      duration: 1
    }
  },
  {
    id: 'strategic_supply_line',
    name: '补给线',
    type: CardType.STRATEGIC,
    rarity: CardRarity.RARE,
    cost: 3,
    description: '建立补给线，持续恢复军队状态',
    combatEffect: [
      {
        type: 'heal',
        value: 15,
        target: 'ally',
        duration: 3
      }
    ],
    globalEffect: {
      type: 'resource_bonus',
      description: '每回合额外获得2金币',
      value: 2,
      duration: 5
    }
  }
];

/**
 * 卡牌管理器
 */
export class CardManager {
  private static instance: CardManager;
  private heroCards: Map<string, HeroCard> = new Map();
  private spellCards: Map<string, SpellCard> = new Map();
  private strategicCards: Map<string, StrategicCard> = new Map();

  private constructor() {
    this.initializeCards();
  }

  public static getInstance(): CardManager {
    if (!CardManager.instance) {
      CardManager.instance = new CardManager();
    }
    return CardManager.instance;
  }

  /**
   * 初始化卡牌数据
   */
  private initializeCards(): void {
    // 加载英雄卡
    for (const card of HERO_CARDS) {
      this.heroCards.set(card.id, card);
    }

    // 加载法术卡
    for (const card of SPELL_CARDS) {
      this.spellCards.set(card.id, card);
    }

    // 加载战略指令卡
    for (const card of STRATEGIC_CARDS) {
      this.strategicCards.set(card.id, card);
    }
  }

  /**
   * 获取卡牌
   */
  public getCard(cardId: string): HeroCard | SpellCard | StrategicCard | null {
    return this.heroCards.get(cardId) || 
           this.spellCards.get(cardId) || 
           this.strategicCards.get(cardId) || 
           null;
  }

  /**
   * 获取所有英雄卡
   */
  public getAllHeroCards(): HeroCard[] {
    return Array.from(this.heroCards.values());
  }

  /**
   * 根据阵营获取英雄卡
   */
  public getHeroCardsByFaction(faction: Faction): HeroCard[] {
    return this.getAllHeroCards().filter(card => card.faction === faction);
  }

  /**
   * 根据稀有度获取卡牌
   */
  public getCardsByRarity(rarity: CardRarity): (HeroCard | SpellCard | StrategicCard)[] {
    const cards: (HeroCard | SpellCard | StrategicCard)[] = [];
    
    cards.push(...Array.from(this.heroCards.values()).filter(card => card.rarity === rarity));
    cards.push(...Array.from(this.spellCards.values()).filter(card => card.rarity === rarity));
    cards.push(...Array.from(this.strategicCards.values()).filter(card => card.rarity === rarity));
    
    return cards;
  }

  /**
   * 计算卡牌购买价格
   */
  public getCardPrice(card: HeroCard | SpellCard | StrategicCard): number {
    const basePrice = card.cost;
    const rarityMultiplier = GAME_CONFIG.CARD_COST_MULTIPLIER[card.rarity];
    return Math.floor(basePrice * rarityMultiplier);
  }

  /**
   * 生成随机卡牌池
   */
  public generateCardPool(count: number = 5): (HeroCard | SpellCard | StrategicCard)[] {
    const allCards: (HeroCard | SpellCard | StrategicCard)[] = [
      ...Array.from(this.heroCards.values()),
      ...Array.from(this.spellCards.values()),
      ...Array.from(this.strategicCards.values())
    ];

    // 根据稀有度权重随机选择
    const pool: (HeroCard | SpellCard | StrategicCard)[] = [];
    
    for (let i = 0; i < count; i++) {
      const rarity = this.getRandomRarity();
      const cardsOfRarity = this.getCardsByRarity(rarity);
      
      if (cardsOfRarity.length > 0) {
        const randomCard = cardsOfRarity[Math.floor(Math.random() * cardsOfRarity.length)];
        pool.push(randomCard);
      }
    }

    return pool;
  }

  /**
   * 根据权重获取随机稀有度
   */
  private getRandomRarity(): CardRarity {
    const random = Math.random();
    
    if (random < 0.5) return CardRarity.COMMON;      // 50%
    if (random < 0.8) return CardRarity.RARE;        // 30%
    if (random < 0.95) return CardRarity.EPIC;       // 15%
    return CardRarity.LEGENDARY;                     // 5%
  }

  /**
   * 验证卡牌组合法性
   */
  public validateDeck(cards: (HeroCard | SpellCard | StrategicCard)[]): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // 检查背包容量
    const totalCost = cards.reduce((sum, card) => sum + card.cost, 0);
    if (totalCost > GAME_CONFIG.INITIAL_BACKPACK_CAPACITY) {
      errors.push(`背包容量超限：${totalCost}/${GAME_CONFIG.INITIAL_BACKPACK_CAPACITY}`);
    }

    // 检查卡牌数量限制
    const cardCounts = new Map<string, number>();
    for (const card of cards) {
      const count = cardCounts.get(card.id) || 0;
      cardCounts.set(card.id, count + 1);
      
      if (count >= 3) {
        errors.push(`卡牌 ${card.name} 数量超限（最多3张）`);
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}
