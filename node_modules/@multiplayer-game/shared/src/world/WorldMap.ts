/**
 * 世界地图系统
 * 固定的大地图，支持多人同时在线
 */

export interface WorldTile {
  id: string;
  x: number;
  y: number;
  terrain: TerrainType;
  resources: {
    gold: number;
    population: number;
    special?: string; // 特殊资源：水晶、遗迹等
  };
  building?: Building;
  occupiedBy?: string; // 玩家ID
  army?: WorldArmy;
  adjacentTiles: string[]; // 相邻地块ID列表
}

export interface WorldArmy {
  id: string;
  playerId: string;
  playerName: string;
  cards: HeroCard[];
  strength: number;
  movementPoints: number; // 剩余移动点数
  level: number;
  experience: number;
}

export interface WorldPlayer {
  id: string;
  username: string;
  level: number;
  experience: number;
  totalGold: number;
  totalPopulation: number;
  territories: string[]; // 控制的地块ID
  armies: WorldArmy[];
  isOnline: boolean;
  lastActionTime: number;
}

export enum TerrainType {
  PLAIN = 'plain',
  FOREST = 'forest',
  MOUNTAIN = 'mountain',
  RIVER = 'river',
  DESERT = 'desert',
  SWAMP = 'swamp',
  CRYSTAL = 'crystal',
  RUINS = 'ruins'
}

/**
 * 世界地图管理器
 * 管理固定的大地图和所有玩家状态
 */
export class WorldMapManager {
  private tiles: Map<string, WorldTile> = new Map();
  private players: Map<string, WorldPlayer> = new Map();
  private currentTurn: number = 1;
  private turnOrder: string[] = []; // 玩家回合顺序
  private currentPlayerIndex: number = 0;
  private turnTimeLimit: number = 60; // 每回合60秒
  private turnStartTime: number = 0;

  constructor() {
    this.generateWorldMap();
  }

  /**
   * 生成固定的世界地图
   */
  private generateWorldMap(): void {
    const MAP_WIDTH = 25;
    const MAP_HEIGHT = 20;
    
    console.log('🌍 生成世界地图...');

    // 生成地块
    for (let x = 0; x < MAP_WIDTH; x++) {
      for (let y = 0; y < MAP_HEIGHT; y++) {
        const tileId = `${x}_${y}`;
        
        // 根据位置决定地形（固定算法，确保每次生成相同）
        const terrain = this.getTerrainByPosition(x, y, MAP_WIDTH, MAP_HEIGHT);
        
        const tile: WorldTile = {
          id: tileId,
          x,
          y,
          terrain,
          resources: this.generateTileResources(terrain),
          adjacentTiles: this.calculateAdjacentTiles(x, y, MAP_WIDTH, MAP_HEIGHT),
        };

        this.tiles.set(tileId, tile);
      }
    }

    console.log(`✅ 世界地图生成完成: ${this.tiles.size} 个地块`);
  }

  /**
   * 根据位置确定地形（固定算法）
   */
  private getTerrainByPosition(x: number, y: number, width: number, height: number): TerrainType {
    // 使用固定的伪随机算法，确保地图每次都相同
    const seed = x * 1000 + y;
    const random = Math.sin(seed) * 10000;
    const value = random - Math.floor(random);

    // 边缘多山脉
    if (x === 0 || x === width - 1 || y === 0 || y === height - 1) {
      return value > 0.7 ? TerrainType.MOUNTAIN : TerrainType.FOREST;
    }

    // 中心区域多平原
    const centerDistance = Math.abs(x - width/2) + Math.abs(y - height/2);
    if (centerDistance < 5) {
      return value > 0.8 ? TerrainType.FOREST : TerrainType.PLAIN;
    }

    // 其他区域按概率分布
    if (value < 0.4) return TerrainType.PLAIN;
    if (value < 0.6) return TerrainType.FOREST;
    if (value < 0.75) return TerrainType.MOUNTAIN;
    if (value < 0.85) return TerrainType.RIVER;
    if (value < 0.95) return TerrainType.DESERT;
    if (value < 0.98) return TerrainType.SWAMP;
    if (value < 0.995) return TerrainType.CRYSTAL;
    return TerrainType.RUINS;
  }

  /**
   * 生成地块资源
   */
  private generateTileResources(terrain: TerrainType): WorldTile['resources'] {
    const baseResources = {
      [TerrainType.PLAIN]: { gold: 3, population: 5 },
      [TerrainType.FOREST]: { gold: 2, population: 3 },
      [TerrainType.MOUNTAIN]: { gold: 5, population: 1 },
      [TerrainType.RIVER]: { gold: 4, population: 4 },
      [TerrainType.DESERT]: { gold: 1, population: 1 },
      [TerrainType.SWAMP]: { gold: 1, population: 2 },
      [TerrainType.CRYSTAL]: { gold: 10, population: 1, special: 'crystal' },
      [TerrainType.RUINS]: { gold: 8, population: 2, special: 'ancient_knowledge' }
    };

    return baseResources[terrain];
  }

  /**
   * 计算相邻地块
   */
  private calculateAdjacentTiles(x: number, y: number, width: number, height: number): string[] {
    const adjacent: string[] = [];
    const directions = [
      [-1, 0], [1, 0], [0, -1], [0, 1], // 四方向
      [-1, -1], [-1, 1], [1, -1], [1, 1] // 八方向（可选）
    ];

    for (const [dx, dy] of directions) {
      const newX = x + dx;
      const newY = y + dy;
      
      if (newX >= 0 && newX < width && newY >= 0 && newY < height) {
        adjacent.push(`${newX}_${newY}`);
      }
    }

    return adjacent;
  }

  /**
   * 玩家加入游戏
   */
  public addPlayer(playerId: string, username: string): boolean {
    if (this.players.has(playerId)) {
      return false;
    }

    const player: WorldPlayer = {
      id: playerId,
      username,
      level: 1,
      experience: 0,
      totalGold: 100,
      totalPopulation: 50,
      territories: [],
      armies: [],
      isOnline: true,
      lastActionTime: Date.now()
    };

    // 为新玩家分配起始位置和军队
    this.assignStartingPosition(player);
    
    this.players.set(playerId, player);
    this.turnOrder.push(playerId);

    console.log(`👤 玩家 ${username} 加入世界，当前玩家数: ${this.players.size}`);
    return true;
  }

  /**
   * 分配起始位置
   */
  private assignStartingPosition(player: WorldPlayer): void {
    // 找到空闲的平原地块作为起始位置
    const availableTiles = Array.from(this.tiles.values()).filter(tile => 
      tile.terrain === TerrainType.PLAIN && 
      !tile.occupiedBy && 
      !tile.army
    );

    if (availableTiles.length === 0) {
      throw new Error('没有可用的起始位置');
    }

    // 选择一个起始位置
    const startTile = availableTiles[Math.floor(Math.random() * availableTiles.length)];
    
    // 创建起始军队
    const startingArmy: WorldArmy = {
      id: `army_${player.id}_1`,
      playerId: player.id,
      playerName: player.username,
      cards: this.generateStartingCards(),
      strength: 3,
      movementPoints: 2, // 每回合2点移动力
      level: 1,
      experience: 0
    };

    // 占领起始地块
    startTile.occupiedBy = player.id;
    startTile.army = startingArmy;
    
    player.territories.push(startTile.id);
    player.armies.push(startingArmy);

    console.log(`🏠 玩家 ${player.username} 起始位置: (${startTile.x}, ${startTile.y})`);
  }

  /**
   * 生成起始卡牌
   */
  private generateStartingCards(): HeroCard[] {
    // 简化的起始卡牌
    return [
      {
        id: 'starter_1',
        heroName: '新兵',
        cost: 1,
        rarity: 'common',
        faction: 'neutral',
        unitType: 'infantry',
        stats: { attack: 3, health: 5, armor: 1, speed: 2 },
        skills: []
      },
      {
        id: 'starter_2', 
        heroName: '弓手',
        cost: 2,
        rarity: 'common',
        faction: 'neutral',
        unitType: 'archer',
        stats: { attack: 4, health: 3, armor: 0, speed: 3 },
        skills: []
      }
    ];
  }

  /**
   * 移动军队
   */
  public moveArmy(playerId: string, armyId: string, targetTileId: string): {
    success: boolean;
    message: string;
    battleTriggered?: boolean;
    battleData?: any;
  } {
    const player = this.players.get(playerId);
    if (!player) {
      return { success: false, message: '玩家不存在' };
    }

    // 检查是否是当前玩家的回合
    if (this.getCurrentPlayer()?.id !== playerId) {
      return { success: false, message: '不是你的回合' };
    }

    const army = player.armies.find(a => a.id === armyId);
    if (!army) {
      return { success: false, message: '军队不存在' };
    }

    if (army.movementPoints <= 0) {
      return { success: false, message: '移动点数不足' };
    }

    const targetTile = this.tiles.get(targetTileId);
    if (!targetTile) {
      return { success: false, message: '目标地块不存在' };
    }

    // 找到军队当前位置
    const currentTile = Array.from(this.tiles.values()).find(tile => tile.army?.id === armyId);
    if (!currentTile) {
      return { success: false, message: '找不到军队当前位置' };
    }

    // 检查是否相邻
    if (!currentTile.adjacentTiles.includes(targetTileId)) {
      return { success: false, message: '只能移动到相邻地块' };
    }

    // 检查目标地块是否有敌军
    if (targetTile.army && targetTile.army.playerId !== playerId) {
      // 触发战斗！
      return {
        success: true,
        message: '遭遇敌军，进入战斗！',
        battleTriggered: true,
        battleData: {
          attacker: army,
          defender: targetTile.army,
          battleTile: targetTile
        }
      };
    }

    // 执行移动
    currentTile.army = null;
    if (currentTile.occupiedBy === playerId && player.armies.filter(a => a.id !== armyId).length === 0) {
      // 如果这是玩家在该地块的最后一支军队，失去控制权
      currentTile.occupiedBy = undefined;
      const territoryIndex = player.territories.indexOf(currentTile.id);
      if (territoryIndex !== -1) {
        player.territories.splice(territoryIndex, 1);
      }
    }

    targetTile.army = army;
    targetTile.occupiedBy = playerId;
    if (!player.territories.includes(targetTileId)) {
      player.territories.push(targetTileId);
    }

    // 消耗移动点数
    army.movementPoints--;

    return {
      success: true,
      message: `${army.playerName}的军队移动成功`
    };
  }

  /**
   * 获取当前回合玩家
   */
  public getCurrentPlayer(): WorldPlayer | null {
    if (this.turnOrder.length === 0) return null;
    const currentPlayerId = this.turnOrder[this.currentPlayerIndex];
    return this.players.get(currentPlayerId) || null;
  }

  /**
   * 结束当前玩家回合
   */
  public endTurn(playerId: string): boolean {
    const currentPlayer = this.getCurrentPlayer();
    if (!currentPlayer || currentPlayer.id !== playerId) {
      return false;
    }

    // 恢复所有军队的移动点数
    currentPlayer.armies.forEach(army => {
      army.movementPoints = 2; // 每回合恢复2点移动力
    });

    // 收集资源
    this.collectResources(currentPlayer);

    // 切换到下一个玩家
    this.currentPlayerIndex = (this.currentPlayerIndex + 1) % this.turnOrder.length;
    
    // 如果回到第一个玩家，增加回合数
    if (this.currentPlayerIndex === 0) {
      this.currentTurn++;
      console.log(`🔄 进入第 ${this.currentTurn} 回合`);
    }

    this.turnStartTime = Date.now();
    return true;
  }

  /**
   * 收集资源
   */
  private collectResources(player: WorldPlayer): void {
    let goldGain = 0;
    let populationGain = 0;

    for (const territoryId of player.territories) {
      const tile = this.tiles.get(territoryId);
      if (tile) {
        goldGain += tile.resources.gold;
        populationGain += tile.resources.population;
      }
    }

    player.totalGold += goldGain;
    player.totalPopulation += populationGain;

    console.log(`💰 ${player.username} 收集资源: +${goldGain}金币, +${populationGain}人口`);
  }

  /**
   * 获取世界状态
   */
  public getWorldState(): {
    tiles: WorldTile[];
    players: WorldPlayer[];
    currentTurn: number;
    currentPlayer: string | null;
    turnTimeLeft: number;
  } {
    const currentPlayer = this.getCurrentPlayer();
    const turnTimeLeft = Math.max(0, this.turnTimeLimit - Math.floor((Date.now() - this.turnStartTime) / 1000));

    return {
      tiles: Array.from(this.tiles.values()),
      players: Array.from(this.players.values()),
      currentTurn: this.currentTurn,
      currentPlayer: currentPlayer?.id || null,
      turnTimeLeft
    };
  }

  /**
   * 获取玩家可见的地图区域
   */
  public getVisibleMap(playerId: string, centerX: number, centerY: number, radius: number = 10): WorldTile[] {
    const visibleTiles: WorldTile[] = [];
    
    for (let x = centerX - radius; x <= centerX + radius; x++) {
      for (let y = centerY - radius; y <= centerY + radius; y++) {
        const tileId = `${x}_${y}`;
        const tile = this.tiles.get(tileId);
        if (tile) {
          visibleTiles.push(tile);
        }
      }
    }

    return visibleTiles;
  }
}
