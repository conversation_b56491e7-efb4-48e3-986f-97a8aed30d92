// 导出所有类型定义
export * from './types';

// 导出游戏系统
export * from './game/GameController';
export * from './game/StrategicGameManager';

// 导出地图系统
export * from './map/HexMap';

// 导出战斗系统
export * from './combat/AutoChessEngine';

// 导出身份系统
export * from './identity/IdentitySkills';

// 导出阵营系统
export * from './faction/FactionSystem';

// 导出卡牌系统
export * from './cards/CardDatabase';

// 导出剧本系统
export * from './scenario/ScenarioManager';

// 导出环境系统
export * from './environment/EnvironmentSystem';

// 导出经济系统
export * from './economy/EconomySystem';

// 导出常用的工具函数
export const generateId = (): string => {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
};

export const validateUsername = (username: string): boolean => {
  return username.length >= 3 && username.length <= 20 && /^[a-zA-Z0-9_]+$/.test(username);
};

export const validatePassword = (password: string): boolean => {
  return password.length >= 6;
};
