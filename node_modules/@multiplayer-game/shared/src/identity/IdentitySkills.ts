import { IdentitySkill, PlayerIdentity, SkillEffect } from '../types';

/**
 * 身份技能定义
 * 根据设计文档实现主公、忠臣、反贼、内奸的特殊技能
 */

// 主公技能
export const EMPEROR_SKILLS: IdentitySkill[] = [
  {
    id: 'emperor_collapse',
    name: '崩殂',
    type: 'locked',
    description: '当你死亡时，杀死你的角色明置身份牌。若其为反贼→立即获得10金币，并使你的所有棋子在本场战斗中攻击速度提升20%。若其为忠臣→你的所有棋子立即随机获得一个负面状态。',
    cooldown: 0
  },
  {
    id: 'emperor_legacy',
    name: '托孤',
    type: 'active',
    description: '当你死亡时，可指定一名身份未知的角色明置身份牌。若为忠臣→你所有棋子星级提升1星，并立即获得一次免费的商店刷新机会，身份替换为"丞相"。若为反贼→其获胜。若为内奸→该人获得你所有棋子总兵力20%的额外人口。',
    cooldown: 0
  }
];

// 忠臣技能
export const LOYALIST_SKILLS: IdentitySkill[] = [
  {
    id: 'loyalist_loyalty',
    name: '赤心',
    type: 'locked',
    description: '死亡时：若杀死你的是主公或丞相→其所有棋子本场战斗的基础攻击力降低15%。若是内奸→其获得5金币。',
    cooldown: 0
  },
  {
    id: 'loyalist_sacrifice',
    name: '舍身',
    type: 'active',
    description: '主公/丞相濒死时，你可以选择发动。发动时你立即死亡，主公/丞相所有棋子立即恢复20%最大兵力，并使所有棋子在下一场战斗中获得一个额外羁绊效果（随机）。',
    cooldown: 0,
    cost: { population: 100 } // 消耗生命
  }
];

// 反贼技能
export const REBEL_SKILLS: IdentitySkill[] = [
  {
    id: 'rebel_defeat',
    name: '败溃',
    type: 'locked',
    description: '死亡时，杀死你的角色立即获得30金币和人口。',
    cooldown: 0
  },
  {
    id: 'rebel_uprising',
    name: '起义',
    type: 'active',
    description: '回合可明置身份牌并立即刷新一次商店，且本回合内所有棋子购买费用降低1金币（最低1金）。',
    cooldown: 15,
    cost: { gold: 5 }
  }
];

// 内奸技能
export const TRAITOR_SKILLS: IdentitySkill[] = [
  {
    id: 'traitor_conspiracy',
    name: '潜谋',
    type: 'locked',
    description: '死亡时：若"夺权"未发动→杀死你的角色随机损失2点人口。若已发动"夺权"→杀死你的角色获得2金币。',
    cooldown: 0
  },
  {
    id: 'traitor_usurp',
    name: '夺权',
    type: 'active',
    description: '出牌阶段可明置身份牌：立即获得10点人口上限，并使你所有棋子立即获得1点能量。可令其他非锁定身份技于你接下来进行的一次自走棋战斗中失效。',
    cooldown: 20,
    cost: { gold: 10 }
  }
];

/**
 * 获取身份对应的技能列表
 */
export function getIdentitySkills(identity: PlayerIdentity): IdentitySkill[] {
  switch (identity) {
    case PlayerIdentity.EMPEROR:
      return [...EMPEROR_SKILLS];
    case PlayerIdentity.LOYALIST:
      return [...LOYALIST_SKILLS];
    case PlayerIdentity.REBEL:
      return [...REBEL_SKILLS];
    case PlayerIdentity.TRAITOR:
      return [...TRAITOR_SKILLS];
    default:
      return [];
  }
}

/**
 * 身份技能管理器
 */
export class IdentitySkillManager {
  private playerSkills: Map<string, IdentitySkill[]> = new Map();
  private skillCooldowns: Map<string, Record<string, number>> = new Map();

  /**
   * 初始化玩家技能
   */
  public initializePlayerSkills(playerId: string, identity: PlayerIdentity): void {
    const skills = getIdentitySkills(identity);
    this.playerSkills.set(playerId, skills);
    this.skillCooldowns.set(playerId, {});
  }

  /**
   * 获取玩家技能
   */
  public getPlayerSkills(playerId: string): IdentitySkill[] {
    return this.playerSkills.get(playerId) || [];
  }

  /**
   * 检查技能是否可用
   */
  public canUseSkill(playerId: string, skillId: string): boolean {
    const cooldowns = this.skillCooldowns.get(playerId);
    if (!cooldowns) return false;
    
    const remainingCooldown = cooldowns[skillId] || 0;
    return remainingCooldown <= 0;
  }

  /**
   * 使用技能
   */
  public useSkill(playerId: string, skillId: string): boolean {
    if (!this.canUseSkill(playerId, skillId)) {
      return false;
    }

    const skills = this.getPlayerSkills(playerId);
    const skill = skills.find(s => s.id === skillId);
    
    if (!skill || skill.type === 'locked') {
      return false;
    }

    // 设置冷却时间
    const cooldowns = this.skillCooldowns.get(playerId) || {};
    cooldowns[skillId] = skill.cooldown || 0;
    this.skillCooldowns.set(playerId, cooldowns);

    return true;
  }

  /**
   * 更新冷却时间（每回合调用）
   */
  public updateCooldowns(playerId: string): void {
    const cooldowns = this.skillCooldowns.get(playerId);
    if (!cooldowns) return;

    for (const skillId in cooldowns) {
      if (cooldowns[skillId] > 0) {
        cooldowns[skillId]--;
      }
    }
  }

  /**
   * 触发被动技能
   */
  public triggerPassiveSkill(playerId: string, trigger: 'death' | 'kill' | 'combat_start' | 'turn_start'): SkillEffect[] {
    const skills = this.getPlayerSkills(playerId);
    const effects: SkillEffect[] = [];

    for (const skill of skills) {
      if (skill.type === 'locked' || skill.type === 'passive') {
        // 根据技能ID和触发条件判断是否激活
        if (this.shouldTriggerSkill(skill.id, trigger)) {
          effects.push(...this.getSkillEffects(skill.id));
        }
      }
    }

    return effects;
  }

  /**
   * 判断技能是否应该触发
   */
  private shouldTriggerSkill(skillId: string, trigger: string): boolean {
    const deathTriggers = ['emperor_collapse', 'loyalist_loyalty', 'rebel_defeat', 'traitor_conspiracy'];
    
    switch (trigger) {
      case 'death':
        return deathTriggers.includes(skillId);
      default:
        return false;
    }
  }

  /**
   * 获取技能效果
   */
  private getSkillEffects(skillId: string): SkillEffect[] {
    switch (skillId) {
      case 'emperor_collapse':
        return [
          {
            type: 'buff',
            value: 20,
            duration: 1,
            target: 'ally'
          }
        ];
      case 'loyalist_loyalty':
        return [
          {
            type: 'debuff',
            value: -15,
            duration: 1,
            target: 'enemy'
          }
        ];
      case 'rebel_defeat':
        return [
          {
            type: 'buff',
            value: 30,
            target: 'enemy'
          }
        ];
      default:
        return [];
    }
  }

  /**
   * 检查胜利条件
   */
  public checkVictoryConditions(players: any[]): { winner: string | null; reason: string } {
    const alivePlayers = players.filter(p => p.isAlive);
    const aliveIdentities = alivePlayers.map(p => p.identity);

    // 主公获胜：所有反贼和内奸死亡
    const emperor = players.find(p => p.identity === PlayerIdentity.EMPEROR);
    if (emperor && emperor.isAlive) {
      const hasAliveRebels = aliveIdentities.includes(PlayerIdentity.REBEL);
      const hasAliveTraitors = aliveIdentities.includes(PlayerIdentity.TRAITOR);
      
      if (!hasAliveRebels && !hasAliveTraitors) {
        return { winner: emperor.id, reason: '主公统一天下' };
      }
    }

    // 反贼获胜：主公死亡
    if (!emperor || !emperor.isAlive) {
      const aliveRebels = alivePlayers.filter(p => p.identity === PlayerIdentity.REBEL);
      if (aliveRebels.length > 0) {
        return { winner: aliveRebels[0].id, reason: '反贼推翻暴政' };
      }
    }

    // 内奸获胜：只剩内奸和主公，且主公死亡
    if (alivePlayers.length === 1) {
      const lastPlayer = alivePlayers[0];
      if (lastPlayer.identity === PlayerIdentity.TRAITOR) {
        return { winner: lastPlayer.id, reason: '内奸篡夺皇位' };
      }
    }

    return { winner: null, reason: '' };
  }
}

/**
 * 身份分配器
 */
export class IdentityAssigner {
  /**
   * 为8人游戏分配身份
   */
  public static assignIdentities(playerIds: string[]): Map<string, PlayerIdentity> {
    if (playerIds.length !== 8) {
      throw new Error('身份分配需要8个玩家');
    }

    const identities: PlayerIdentity[] = [
      PlayerIdentity.EMPEROR,    // 1个主公
      PlayerIdentity.LOYALIST,   // 2个忠臣
      PlayerIdentity.LOYALIST,
      PlayerIdentity.REBEL,      // 4个反贼
      PlayerIdentity.REBEL,
      PlayerIdentity.REBEL,
      PlayerIdentity.REBEL,
      PlayerIdentity.TRAITOR     // 1个内奸
    ];

    // 随机打乱身份
    for (let i = identities.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [identities[i], identities[j]] = [identities[j], identities[i]];
    }

    // 分配给玩家
    const assignments = new Map<string, PlayerIdentity>();
    for (let i = 0; i < playerIds.length; i++) {
      assignments.set(playerIds[i], identities[i]);
    }

    return assignments;
  }

  /**
   * 检查身份分配是否平衡
   */
  public static validateAssignment(assignments: Map<string, PlayerIdentity>): boolean {
    const counts = new Map<PlayerIdentity, number>();
    
    for (const identity of assignments.values()) {
      counts.set(identity, (counts.get(identity) || 0) + 1);
    }

    return (
      counts.get(PlayerIdentity.EMPEROR) === 1 &&
      counts.get(PlayerIdentity.LOYALIST) === 2 &&
      counts.get(PlayerIdentity.REBEL) === 4 &&
      counts.get(PlayerIdentity.TRAITOR) === 1
    );
  }
}
