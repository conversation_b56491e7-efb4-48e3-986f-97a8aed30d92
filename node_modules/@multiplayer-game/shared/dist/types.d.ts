export interface User {
    id: string;
    username: string;
    isOnline: boolean;
    currentRoomId?: string;
    level?: number;
    experience?: number;
}
export interface LoginRequest {
    username: string;
    password: string;
}
export interface RegisterRequest {
    username: string;
    password: string;
}
export interface AuthResponse {
    success: boolean;
    user?: User;
    token?: string;
    message?: string;
}
export declare enum GameState {
    LOBBY = "lobby",// 大厅等待
    STRATEGIC = "strategic",// 战略地图模式
    COMBAT_INIT = "combat_init",// 战斗初始化
    COMBAT_DEPLOY = "combat_deploy",// 战斗部署阶段
    COMBAT_AUTO = "combat_auto",// 自动战斗阶段
    COMBAT_RESULT = "combat_result",// 战斗结算
    GAME_END = "game_end"
}
export declare enum StrategicPhase {
    RESOURCE_COLLECTION = "resource_collection",// 资源收集
    PLAYER_ACTION = "player_action",// 玩家行动
    EVENT_PROCESSING = "event_processing",// 事件处理
    TURN_END = "turn_end"
}
export declare enum PlayerIdentity {
    EMPEROR = "emperor",// 主公
    LOYALIST = "loyalist",// 忠臣
    REBEL = "rebel",// 反贼
    TRAITOR = "traitor"
}
export interface IdentitySkill {
    id: string;
    name: string;
    type: 'active' | 'passive' | 'locked';
    description: string;
    cooldown?: number;
    cost?: {
        gold?: number;
        population?: number;
    };
}
export declare enum Faction {
    SHU = "shu",// 蜀
    WEI = "wei",// 魏
    WU = "wu",// 吴
    QUN = "qun",// 群
    JIN = "jin",// 晋
    HAN = "han"
}
export declare enum Element {
    METAL = "metal",// 金
    WOOD = "wood",// 木
    WATER = "water",// 水
    FIRE = "fire",// 火
    EARTH = "earth",// 土
    THUNDER = "thunder"
}
export declare enum UnitType {
    INFANTRY = "infantry",// 步兵 (Shield 盾)
    CAVALRY = "cavalry",// 骑兵 (Sword 剑)
    ARCHER = "archer",// 弓兵 (Bow 弓)
    CHARIOT = "chariot",// 战车 (Spear 枪)
    MAGE = "mage"
}
export declare const UNIT_COUNTER_MAP: Record<UnitType, UnitType[]>;
export declare enum UnitRole {
    TANK = "tank",// 坦克
    WARRIOR = "warrior",// 战士
    MAGE = "mage",// 法师
    CONTROL = "control",// 控制
    ARCHER = "archer",// 射手
    SUPPORT = "support"
}
export declare enum CardType {
    HERO = "hero",// 英雄卡
    SPELL = "spell",// 法术卡
    STRATEGIC = "strategic"
}
export declare enum CardRarity {
    COMMON = "common",// 普通 (黄)
    RARE = "rare",// 精良 (玄)
    EPIC = "epic",// 传奇 (地)
    LEGENDARY = "legendary"
}
export interface BaseCard {
    id: string;
    name: string;
    type: CardType;
    rarity: CardRarity;
    cost: number;
    description: string;
    imageUrl?: string;
}
export interface HeroStats {
    health: number;
    energy: number;
    attack: number;
    armor: number;
    strategy: number;
    magicResist: number;
    speed: number;
}
export declare enum SkillType {
    ACTIVE = "active",// 主动技
    PASSIVE = "passive"
}
export interface Skill {
    id: string;
    name: string;
    type: SkillType;
    description: string;
    cooldown?: number;
    energyCost?: number;
    effects: SkillEffect[];
}
export interface SkillEffect {
    type: 'damage' | 'heal' | 'buff' | 'debuff' | 'control' | 'summon' | 'terrain';
    value?: number;
    duration?: number;
    target: 'self' | 'ally' | 'enemy' | 'all' | 'area' | 'archer' | 'mage' | 'infantry' | 'cavalry';
    range?: number;
}
export interface HeroCard extends BaseCard {
    type: CardType.HERO;
    heroName: string;
    faction: Faction;
    unitType: UnitType;
    element: Element;
    role: UnitRole;
    stats: HeroStats;
    skill: Skill;
    synergies: string[];
}
export declare enum SpellSchool {
    MILITARY = "military",// 兵家权谋
    MYSTIC = "mystic",// 玄门道法
    MECHANISM = "mechanism"
}
export interface SpellCard extends BaseCard {
    type: CardType.SPELL;
    school: SpellSchool;
    energyCost: number;
    effects: SkillEffect[];
    targetType: 'unit' | 'area' | 'global' | 'terrain';
}
export interface StrategicCard extends BaseCard {
    type: CardType.STRATEGIC;
    combatEffect: SkillEffect[];
    globalEffect: GlobalEffect;
    duration?: number;
}
export interface GlobalEffect {
    type: 'terrain_change' | 'resource_bonus' | 'movement_block' | 'weather_control' | 'building_create';
    description: string;
    value?: number;
    area?: Position[];
    duration: number;
}
export type Card = HeroCard | SpellCard | StrategicCard;
export declare enum TerrainType {
    PLAIN = "plain",// 平原
    MOUNTAIN = "mountain",// 山脉
    FOREST = "forest",// 森林
    RIVER = "river",// 河流
    DESERT = "desert",// 沙漠
    SWAMP = "swamp",// 沼泽
    CRYSTAL = "crystal",// 水晶矿脉
    SCORCHED = "scorched"
}
export declare enum WeatherType {
    CLEAR = "clear",// 晴天
    RAIN = "rain",// 暴雨
    SANDSTORM = "sandstorm",// 沙暴
    AURORA = "aurora",// 极光
    WINTER = "winter",// 严冬
    THUNDER = "thunder"
}
export interface Position {
    x: number;
    y: number;
}
export interface HexTile {
    id: string;
    position: Position;
    terrainType: TerrainType;
    ownerId?: string;
    resources: {
        gold: number;
        population: number;
    };
    buildings?: Building[];
    units?: Army[];
}
export interface Building {
    id: string;
    type: 'fortress' | 'tower' | 'mine' | 'farm' | 'temple' | 'market' | 'academy';
    level: number;
    effects: BuildingEffect[];
}
export interface BuildingEffect {
    type: 'resource_bonus' | 'defense_bonus' | 'unit_bonus';
    value: number;
}
export interface Army {
    id: string;
    ownerId: string;
    name: string;
    position: Position;
    cards: Card[];
    fatigue: number;
    morale: number;
}
export declare enum BattleStance {
    ATTACK = "attack",// 攻击
    DEFEND = "defend",// 防守
    SUPPORT = "support",// 支援
    IGNORE = "ignore"
}
export interface Player {
    id: string;
    username: string;
    identity: PlayerIdentity;
    identityRevealed: boolean;
    faction: Faction;
    level: number;
    resources: {
        gold: number;
        population: number;
        experience: number;
    };
    territories: string[];
    armies: Army[];
    cardCollection: Card[];
    backpackCapacity: number;
    isReady: boolean;
    isAlive: boolean;
    battleStance: BattleStance;
    identitySkills: IdentitySkill[];
    skillCooldowns: Record<string, number>;
}
export interface Scenario {
    id: string;
    name: string;
    description: string;
    mapSize: {
        width: number;
        height: number;
    };
    maxTurns: number;
    specialRules: ScenarioRule[];
    initialResources: {
        gold: number;
        population: number;
    };
    availableFactions: Faction[];
    weatherEvents: WeatherEvent[];
}
export interface ScenarioRule {
    id: string;
    name: string;
    description: string;
    type: 'resource_bonus' | 'unit_bonus' | 'terrain_effect' | 'victory_condition';
    conditions: any;
    effects: any;
}
export interface WeatherEvent {
    turn: number;
    weather: WeatherType;
    duration: number;
    affectedAreas?: Position[];
}
export interface GameRoom {
    id: string;
    name: string;
    hostId: string;
    scenario: Scenario;
    players: Player[];
    maxPlayers: number;
    gameState: GameState;
    strategicPhase: StrategicPhase;
    currentTurn: number;
    currentPlayer: string;
    map: HexTile[];
    weather: {
        current: WeatherType;
        forecast: WeatherType[];
    };
    createdAt: Date;
    startedAt?: Date;
}
export interface CreateRoomRequest {
    name: string;
    scenarioId: string;
    maxPlayers?: number;
    isPrivate?: boolean;
}
export interface JoinRoomRequest {
    roomId: string;
    password?: string;
}
export declare enum MessageType {
    LOGIN = "login",
    REGISTER = "register",
    LOGOUT = "logout",
    CREATE_ROOM = "create_room",
    JOIN_ROOM = "join_room",
    LEAVE_ROOM = "leave_room",
    ROOM_LIST = "room_list",
    PLAYER_READY = "player_ready",
    START_GAME = "start_game",
    GAME_UPDATE = "game_update",
    TURN_START = "turn_start",
    TURN_END = "turn_end",
    MOVE_ARMY = "move_army",
    BUILD_STRUCTURE = "build_structure",
    RECRUIT_UNITS = "recruit_units",
    USE_IDENTITY_SKILL = "use_identity_skill",
    COMBAT_START = "combat_start",
    DEPLOY_CARDS = "deploy_cards",
    COMBAT_AUTO = "combat_auto",
    COMBAT_END = "combat_end",
    BUY_CARD = "buy_card",
    SELL_CARD = "sell_card",
    UPGRADE_CARD = "upgrade_card",
    FIND_MATCH = "find_match",
    CANCEL_MATCH = "cancel_match",
    MATCH_FOUND = "match_found"
}
export declare const GAME_CONFIG: {
    readonly MAX_PLAYERS_PER_ROOM: 8;
    readonly MIN_PLAYERS_TO_START: 8;
    readonly MAX_TURNS: 120;
    readonly TURN_TIME_LIMIT: 60000;
    readonly MAP_WIDTH: 16;
    readonly MAP_HEIGHT: 8;
    readonly COMBAT_BOARD_WIDTH: 16;
    readonly COMBAT_BOARD_HEIGHT: 8;
    readonly DEPLOY_TIME_LIMIT: 60000;
    readonly INITIAL_GOLD: 50;
    readonly INITIAL_POPULATION: 100;
    readonly INITIAL_BACKPACK_CAPACITY: 30;
    readonly CARD_COST_MULTIPLIER: {
        readonly common: 1;
        readonly rare: 1.5;
        readonly epic: 2;
        readonly legendary: 3;
    };
    readonly MATCH_TIMEOUT: 30000;
};
//# sourceMappingURL=types.d.ts.map