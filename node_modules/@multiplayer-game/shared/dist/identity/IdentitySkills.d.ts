import { IdentitySkill, PlayerIdentity, SkillEffect } from '../types';
/**
 * 身份技能定义
 * 根据设计文档实现主公、忠臣、反贼、内奸的特殊技能
 */
export declare const EMPEROR_SKILLS: IdentitySkill[];
export declare const LOYALIST_SKILLS: IdentitySkill[];
export declare const REBEL_SKILLS: IdentitySkill[];
export declare const TRAITOR_SKILLS: IdentitySkill[];
/**
 * 获取身份对应的技能列表
 */
export declare function getIdentitySkills(identity: PlayerIdentity): IdentitySkill[];
/**
 * 身份技能管理器
 */
export declare class IdentitySkillManager {
    private playerSkills;
    private skillCooldowns;
    /**
     * 初始化玩家技能
     */
    initializePlayerSkills(playerId: string, identity: PlayerIdentity): void;
    /**
     * 获取玩家技能
     */
    getPlayerSkills(playerId: string): IdentitySkill[];
    /**
     * 检查技能是否可用
     */
    canUseSkill(playerId: string, skillId: string): boolean;
    /**
     * 使用技能
     */
    useSkill(playerId: string, skillId: string): boolean;
    /**
     * 更新冷却时间（每回合调用）
     */
    updateCooldowns(playerId: string): void;
    /**
     * 触发被动技能
     */
    triggerPassiveSkill(playerId: string, trigger: 'death' | 'kill' | 'combat_start' | 'turn_start'): SkillEffect[];
    /**
     * 判断技能是否应该触发
     */
    private shouldTriggerSkill;
    /**
     * 获取技能效果
     */
    private getSkillEffects;
    /**
     * 检查胜利条件
     */
    checkVictoryConditions(players: any[]): {
        winner: string | null;
        reason: string;
    };
}
/**
 * 身份分配器
 */
export declare class IdentityAssigner {
    /**
     * 为8人游戏分配身份
     */
    static assignIdentities(playerIds: string[]): Map<string, PlayerIdentity>;
    /**
     * 检查身份分配是否平衡
     */
    static validateAssignment(assignments: Map<string, PlayerIdentity>): boolean;
}
//# sourceMappingURL=IdentitySkills.d.ts.map