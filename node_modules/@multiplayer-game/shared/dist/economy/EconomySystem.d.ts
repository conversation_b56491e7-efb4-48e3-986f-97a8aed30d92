import { Player, HexTile, Building, BuildingEffect, TerrainType, Card } from '../types';
import { EnvironmentSystem } from '../environment/EnvironmentSystem';
/**
 * 经济系统
 * 管理资源生产、建筑建设、贸易等经济活动
 */
export interface BuildingType {
    id: string;
    name: string;
    description: string;
    cost: {
        gold: number;
        population: number;
    };
    buildTime: number;
    maxLevel: number;
    effects: BuildingEffect[];
    requirements: {
        terrain?: TerrainType[];
        adjacentBuildings?: string[];
        playerLevel?: number;
    };
}
export declare const BUILDING_TYPES: Record<string, BuildingType>;
export interface TradeRoute {
    id: string;
    from: string;
    to: string;
    resource: 'gold' | 'population' | 'cards';
    amount: number;
    cost: number;
    duration: number;
}
/**
 * 经济系统管理器
 */
export declare class EconomySystem {
    private environmentSystem;
    private tradeRoutes;
    constructor(environmentSystem: EnvironmentSystem);
    /**
     * 计算地块资源产出
     */
    calculateTileProduction(tile: HexTile): {
        gold: number;
        population: number;
        experience: number;
    };
    /**
     * 计算建筑加成
     */
    private calculateBuildingBonus;
    /**
     * 检查是否可以建造建筑
     */
    canBuildBuilding(player: Player, tile: HexTile, buildingTypeId: string): {
        canBuild: boolean;
        reason?: string;
    };
    /**
     * 建造建筑
     */
    buildBuilding(player: Player, tile: HexTile, buildingTypeId: string): boolean;
    /**
     * 升级建筑
     */
    upgradeBuilding(player: Player, building: Building): boolean;
    /**
     * 计算卡牌购买价格
     */
    calculateCardPrice(card: Card, player: Player): number;
    /**
     * 获取市场折扣
     */
    private getMarketDiscount;
    /**
     * 创建贸易路线
     */
    createTradeRoute(fromPlayerId: string, toPlayerId: string, resource: 'gold' | 'population' | 'cards', amount: number): string | null;
    /**
     * 处理贸易路线
     */
    processTradeRoutes(players: Map<string, Player>): void;
    /**
     * 执行贸易路线
     */
    private executeTradeRoute;
    /**
     * 计算玩家总资产
     */
    calculatePlayerWealth(player: Player): number;
    /**
     * 获取经济报告
     */
    getEconomyReport(player: Player): {
        totalWealth: number;
        incomePerTurn: number;
        expenses: number;
        netIncome: number;
        recommendations: string[];
    };
}
//# sourceMappingURL=EconomySystem.d.ts.map