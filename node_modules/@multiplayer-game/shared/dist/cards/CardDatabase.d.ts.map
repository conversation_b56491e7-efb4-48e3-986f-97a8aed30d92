{"version": 3, "file": "CardDatabase.d.ts", "sourceRoot": "", "sources": ["../../src/cards/CardDatabase.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,QAAQ,EACR,SAAS,EACT,aAAa,EACb,QAAQ,EACR,UAAU,EACV,OAAO,EAOR,MAAM,UAAU,CAAC;AAElB;;;GAGG;AAGH,eAAO,MAAM,UAAU,EAAE,QAAQ,EA4MhC,CAAC;AAGF,eAAO,MAAM,WAAW,EAAE,SAAS,EA8DlC,CAAC;AAGF,eAAO,MAAM,eAAe,EAAE,aAAa,EA6C1C,CAAC;AAEF;;GAEG;AACH,qBAAa,WAAW;IACtB,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAc;IACrC,OAAO,CAAC,SAAS,CAAoC;IACrD,OAAO,CAAC,UAAU,CAAqC;IACvD,OAAO,CAAC,cAAc,CAAyC;IAE/D,OAAO;WAIO,WAAW,IAAI,WAAW;IAOxC;;OAEG;IACH,OAAO,CAAC,eAAe;IAiBvB;;OAEG;IACI,OAAO,CAAC,MAAM,EAAE,MAAM,GAAG,QAAQ,GAAG,SAAS,GAAG,aAAa,GAAG,IAAI;IAO3E;;OAEG;IACI,eAAe,IAAI,QAAQ,EAAE;IAIpC;;OAEG;IACI,qBAAqB,CAAC,OAAO,EAAE,OAAO,GAAG,QAAQ,EAAE;IAI1D;;OAEG;IACI,gBAAgB,CAAC,MAAM,EAAE,UAAU,GAAG,CAAC,QAAQ,GAAG,SAAS,GAAG,aAAa,CAAC,EAAE;IAUrF;;OAEG;IACI,YAAY,CAAC,IAAI,EAAE,QAAQ,GAAG,SAAS,GAAG,aAAa,GAAG,MAAM;IAMvE;;OAEG;IACI,gBAAgB,CAAC,KAAK,GAAE,MAAU,GAAG,CAAC,QAAQ,GAAG,SAAS,GAAG,aAAa,CAAC,EAAE;IAuBpF;;OAEG;IACH,OAAO,CAAC,eAAe;IASvB;;OAEG;IACI,YAAY,CAAC,KAAK,EAAE,CAAC,QAAQ,GAAG,SAAS,GAAG,aAAa,CAAC,EAAE,GAAG;QAAE,KAAK,EAAE,OAAO,CAAC;QAAC,MAAM,EAAE,MAAM,EAAE,CAAA;KAAE;CAyB3G;AAED;;GAEG;AACH,qBAAa,eAAe;IAC1B,OAAO,CAAC,QAAQ,CAAS;IACzB,OAAO,CAAC,KAAK,CAAgD;IAC7D,OAAO,CAAC,QAAQ,CAAS;gBAEb,QAAQ,EAAE,MAAM,EAAE,QAAQ,GAAE,MAA8C;IAKtF;;OAEG;IACI,OAAO,CAAC,IAAI,EAAE,QAAQ,GAAG,SAAS,GAAG,aAAa,GAAG,OAAO;IAWnE;;OAEG;IACI,UAAU,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO;IAU1C;;OAEG;IACI,cAAc,IAAI,MAAM;IAI/B;;OAEG;IACI,oBAAoB,IAAI,MAAM;IAIrC;;OAEG;IACI,WAAW,IAAI,CAAC,QAAQ,GAAG,SAAS,GAAG,aAAa,CAAC,EAAE;IAI9D;;OAEG;IACI,cAAc,CAAC,IAAI,EAAE,QAAQ,GAAG,CAAC,QAAQ,GAAG,SAAS,GAAG,aAAa,CAAC,EAAE;IAI/E;;OAEG;IACI,eAAe,CAAC,kBAAkB,EAAE,MAAM,GAAG,IAAI;IAIxD;;OAEG;IACI,UAAU,CAAC,IAAI,EAAE,QAAQ,GAAG,SAAS,GAAG,aAAa,GAAG,OAAO;IAItE;;OAEG;IACI,SAAS,IAAI;QAClB,QAAQ,EAAE,MAAM,CAAC;QACjB,KAAK,EAAE,CAAC,QAAQ,GAAG,SAAS,GAAG,aAAa,CAAC,EAAE,CAAC;QAChD,WAAW,EAAE,MAAM,CAAC;QACpB,QAAQ,EAAE,MAAM,CAAC;QACjB,iBAAiB,EAAE,MAAM,CAAC;KAC3B;CASF"}