"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BackpackManager = exports.CardManager = exports.STRATEGIC_CARDS = exports.SPELL_CARDS = exports.HERO_CARDS = void 0;
const types_1 = require("../types");
/**
 * 卡牌数据库
 * 包含所有游戏中的卡牌定义
 */
// 英雄卡数据库
exports.HERO_CARDS = [
    // 蜀国英雄
    {
        id: 'hero_liu_bei',
        name: '刘备',
        type: types_1.CardType.HERO,
        rarity: types_1.CardRarity.LEGENDARY,
        cost: 5,
        description: '蜀汉昭烈帝，仁德之君',
        heroName: '刘备',
        faction: types_1.Faction.SHU,
        unitType: types_1.UnitType.INFANTRY,
        element: types_1.Element.EARTH,
        role: types_1.UnitRole.TANK,
        stats: {
            health: 120,
            energy: 100,
            attack: 80,
            armor: 25,
            strategy: 90,
            magicResist: 20,
            speed: 70
        },
        skill: {
            id: 'liu_bei_benevolence',
            name: '仁德',
            type: types_1.SkillType.ACTIVE,
            description: '为所有友军恢复生命值，并提供护甲加成',
            cooldown: 3,
            energyCost: 40,
            effects: [
                {
                    type: 'heal',
                    value: 30,
                    target: 'ally'
                },
                {
                    type: 'buff',
                    value: 15,
                    duration: 3,
                    target: 'ally'
                }
            ]
        },
        synergies: ['君主', '仁德', '蜀汉']
    },
    {
        id: 'hero_guan_yu',
        name: '关羽',
        type: types_1.CardType.HERO,
        rarity: types_1.CardRarity.EPIC,
        cost: 4,
        description: '武圣关云长，义薄云天',
        heroName: '关羽',
        faction: types_1.Faction.SHU,
        unitType: types_1.UnitType.CAVALRY,
        element: types_1.Element.FIRE,
        role: types_1.UnitRole.WARRIOR,
        stats: {
            health: 100,
            energy: 80,
            attack: 110,
            armor: 15,
            strategy: 60,
            magicResist: 10,
            speed: 85
        },
        skill: {
            id: 'guan_yu_charge',
            name: '单骑千里',
            type: types_1.SkillType.ACTIVE,
            description: '冲锋攻击敌方后排，造成大量伤害',
            cooldown: 4,
            energyCost: 50,
            effects: [
                {
                    type: 'damage',
                    value: 150,
                    target: 'enemy',
                    range: 3
                }
            ]
        },
        synergies: ['猛将', '义士', '蜀汉']
    },
    {
        id: 'hero_zhang_fei',
        name: '张飞',
        type: types_1.CardType.HERO,
        rarity: types_1.CardRarity.RARE,
        cost: 3,
        description: '燕人张翼德，勇猛无双',
        heroName: '张飞',
        faction: types_1.Faction.SHU,
        unitType: types_1.UnitType.INFANTRY,
        element: types_1.Element.THUNDER,
        role: types_1.UnitRole.TANK,
        stats: {
            health: 130,
            energy: 70,
            attack: 95,
            armor: 30,
            strategy: 40,
            magicResist: 15,
            speed: 60
        },
        skill: {
            id: 'zhang_fei_roar',
            name: '咆哮',
            type: types_1.SkillType.ACTIVE,
            description: '嘲讽敌军并提升自身攻击力',
            cooldown: 3,
            energyCost: 30,
            effects: [
                {
                    type: 'control',
                    value: 2,
                    duration: 2,
                    target: 'enemy'
                },
                {
                    type: 'buff',
                    value: 25,
                    duration: 3,
                    target: 'self'
                }
            ]
        },
        synergies: ['猛将', '守护', '蜀汉']
    },
    // 魏国英雄
    {
        id: 'hero_cao_cao',
        name: '曹操',
        type: types_1.CardType.HERO,
        rarity: types_1.CardRarity.LEGENDARY,
        cost: 5,
        description: '魏武帝，治世之能臣，乱世之奸雄',
        heroName: '曹操',
        faction: types_1.Faction.WEI,
        unitType: types_1.UnitType.CHARIOT,
        element: types_1.Element.METAL,
        role: types_1.UnitRole.CONTROL,
        stats: {
            health: 110,
            energy: 120,
            attack: 85,
            armor: 20,
            strategy: 100,
            magicResist: 25,
            speed: 75
        },
        skill: {
            id: 'cao_cao_ambition',
            name: '奸雄',
            type: types_1.SkillType.PASSIVE,
            description: '每当敌军死亡时，获得能量和攻击力加成',
            effects: [
                {
                    type: 'buff',
                    value: 10,
                    target: 'self'
                }
            ]
        },
        synergies: ['君主', '奸雄', '魏国']
    },
    {
        id: 'hero_xiahou_dun',
        name: '夏侯惇',
        type: types_1.CardType.HERO,
        rarity: types_1.CardRarity.RARE,
        cost: 3,
        description: '独眼将军，忠勇无双',
        heroName: '夏侯惇',
        faction: types_1.Faction.WEI,
        unitType: types_1.UnitType.CAVALRY,
        element: types_1.Element.FIRE,
        role: types_1.UnitRole.WARRIOR,
        stats: {
            health: 105,
            energy: 75,
            attack: 100,
            armor: 18,
            strategy: 55,
            magicResist: 12,
            speed: 80
        },
        skill: {
            id: 'xiahou_dun_fury',
            name: '拔矢啖睛',
            type: types_1.SkillType.PASSIVE,
            description: '受到伤害时反击，伤害越重反击越强',
            effects: [
                {
                    type: 'damage',
                    value: 80,
                    target: 'enemy'
                }
            ]
        },
        synergies: ['猛将', '忠臣', '魏国']
    }
];
// 法术卡数据库
exports.SPELL_CARDS = [
    {
        id: 'spell_fire_attack',
        name: '火攻',
        type: types_1.CardType.SPELL,
        rarity: types_1.CardRarity.COMMON,
        cost: 2,
        description: '对敌方区域造成火焰伤害',
        school: types_1.SpellSchool.MILITARY,
        energyCost: 30,
        effects: [
            {
                type: 'damage',
                value: 60,
                target: 'area',
                range: 2
            }
        ],
        targetType: 'area'
    },
    {
        id: 'spell_healing_spring',
        name: '甘露',
        type: types_1.CardType.SPELL,
        rarity: types_1.CardRarity.COMMON,
        cost: 2,
        description: '为友军恢复生命值',
        school: types_1.SpellSchool.MYSTIC,
        energyCost: 25,
        effects: [
            {
                type: 'heal',
                value: 50,
                target: 'ally'
            }
        ],
        targetType: 'unit'
    },
    {
        id: 'spell_lightning_storm',
        name: '雷暴',
        type: types_1.CardType.SPELL,
        rarity: types_1.CardRarity.EPIC,
        cost: 4,
        description: '召唤雷暴攻击所有敌军',
        school: types_1.SpellSchool.MYSTIC,
        energyCost: 60,
        effects: [
            {
                type: 'damage',
                value: 40,
                target: 'enemy'
            },
            {
                type: 'control',
                value: 1,
                duration: 1,
                target: 'enemy'
            }
        ],
        targetType: 'global'
    }
];
// 战略指令卡数据库
exports.STRATEGIC_CARDS = [
    {
        id: 'strategic_forced_march',
        name: '强行军',
        type: types_1.CardType.STRATEGIC,
        rarity: types_1.CardRarity.COMMON,
        cost: 2,
        description: '增加军队移动力，但降低士气',
        combatEffect: [
            {
                type: 'buff',
                value: 20,
                target: 'ally',
                duration: 1
            }
        ],
        globalEffect: {
            type: 'movement_block',
            description: '军队可以额外移动一格',
            value: 1,
            duration: 1
        }
    },
    {
        id: 'strategic_supply_line',
        name: '补给线',
        type: types_1.CardType.STRATEGIC,
        rarity: types_1.CardRarity.RARE,
        cost: 3,
        description: '建立补给线，持续恢复军队状态',
        combatEffect: [
            {
                type: 'heal',
                value: 15,
                target: 'ally',
                duration: 3
            }
        ],
        globalEffect: {
            type: 'resource_bonus',
            description: '每回合额外获得2金币',
            value: 2,
            duration: 5
        }
    }
];
/**
 * 卡牌管理器
 */
class CardManager {
    constructor() {
        this.heroCards = new Map();
        this.spellCards = new Map();
        this.strategicCards = new Map();
        this.initializeCards();
    }
    static getInstance() {
        if (!CardManager.instance) {
            CardManager.instance = new CardManager();
        }
        return CardManager.instance;
    }
    /**
     * 初始化卡牌数据
     */
    initializeCards() {
        // 加载英雄卡
        for (const card of exports.HERO_CARDS) {
            this.heroCards.set(card.id, card);
        }
        // 加载法术卡
        for (const card of exports.SPELL_CARDS) {
            this.spellCards.set(card.id, card);
        }
        // 加载战略指令卡
        for (const card of exports.STRATEGIC_CARDS) {
            this.strategicCards.set(card.id, card);
        }
    }
    /**
     * 获取卡牌
     */
    getCard(cardId) {
        return this.heroCards.get(cardId) ||
            this.spellCards.get(cardId) ||
            this.strategicCards.get(cardId) ||
            null;
    }
    /**
     * 获取所有英雄卡
     */
    getAllHeroCards() {
        return Array.from(this.heroCards.values());
    }
    /**
     * 根据阵营获取英雄卡
     */
    getHeroCardsByFaction(faction) {
        return this.getAllHeroCards().filter(card => card.faction === faction);
    }
    /**
     * 根据稀有度获取卡牌
     */
    getCardsByRarity(rarity) {
        const cards = [];
        cards.push(...Array.from(this.heroCards.values()).filter(card => card.rarity === rarity));
        cards.push(...Array.from(this.spellCards.values()).filter(card => card.rarity === rarity));
        cards.push(...Array.from(this.strategicCards.values()).filter(card => card.rarity === rarity));
        return cards;
    }
    /**
     * 计算卡牌购买价格
     */
    getCardPrice(card) {
        const basePrice = card.cost;
        const rarityMultiplier = types_1.GAME_CONFIG.CARD_COST_MULTIPLIER[card.rarity];
        return Math.floor(basePrice * rarityMultiplier);
    }
    /**
     * 生成随机卡牌池
     */
    generateCardPool(count = 5) {
        const allCards = [
            ...Array.from(this.heroCards.values()),
            ...Array.from(this.spellCards.values()),
            ...Array.from(this.strategicCards.values())
        ];
        // 根据稀有度权重随机选择
        const pool = [];
        for (let i = 0; i < count; i++) {
            const rarity = this.getRandomRarity();
            const cardsOfRarity = this.getCardsByRarity(rarity);
            if (cardsOfRarity.length > 0) {
                const randomCard = cardsOfRarity[Math.floor(Math.random() * cardsOfRarity.length)];
                pool.push(randomCard);
            }
        }
        return pool;
    }
    /**
     * 根据权重获取随机稀有度
     */
    getRandomRarity() {
        const random = Math.random();
        if (random < 0.5)
            return types_1.CardRarity.COMMON; // 50%
        if (random < 0.8)
            return types_1.CardRarity.RARE; // 30%
        if (random < 0.95)
            return types_1.CardRarity.EPIC; // 15%
        return types_1.CardRarity.LEGENDARY; // 5%
    }
    /**
     * 验证卡牌组合法性
     */
    validateDeck(cards) {
        const errors = [];
        // 检查背包容量
        const totalCost = cards.reduce((sum, card) => sum + card.cost, 0);
        if (totalCost > types_1.GAME_CONFIG.INITIAL_BACKPACK_CAPACITY) {
            errors.push(`背包容量超限：${totalCost}/${types_1.GAME_CONFIG.INITIAL_BACKPACK_CAPACITY}`);
        }
        // 检查卡牌数量限制
        const cardCounts = new Map();
        for (const card of cards) {
            const count = cardCounts.get(card.id) || 0;
            cardCounts.set(card.id, count + 1);
            if (count >= 3) {
                errors.push(`卡牌 ${card.name} 数量超限（最多3张）`);
            }
        }
        return {
            valid: errors.length === 0,
            errors
        };
    }
}
exports.CardManager = CardManager;
/**
 * 背包管理系统
 */
class BackpackManager {
    constructor(playerId, capacity = types_1.GAME_CONFIG.INITIAL_BACKPACK_CAPACITY) {
        this.cards = [];
        this.playerId = playerId;
        this.capacity = capacity;
    }
    /**
     * 添加卡牌到背包
     */
    addCard(card) {
        const currentCost = this.getCurrentCost();
        if (currentCost + card.cost > this.capacity) {
            return false; // 背包容量不足
        }
        this.cards.push(card);
        return true;
    }
    /**
     * 从背包移除卡牌
     */
    removeCard(cardId) {
        const index = this.cards.findIndex(card => card.id === cardId);
        if (index === -1) {
            return false;
        }
        this.cards.splice(index, 1);
        return true;
    }
    /**
     * 获取当前背包占用
     */
    getCurrentCost() {
        return this.cards.reduce((sum, card) => sum + card.cost, 0);
    }
    /**
     * 获取剩余容量
     */
    getRemainingCapacity() {
        return this.capacity - this.getCurrentCost();
    }
    /**
     * 获取所有卡牌
     */
    getAllCards() {
        return [...this.cards];
    }
    /**
     * 按类型获取卡牌
     */
    getCardsByType(type) {
        return this.cards.filter(card => card.type === type);
    }
    /**
     * 升级背包容量
     */
    upgradeCapacity(additionalCapacity) {
        this.capacity += additionalCapacity;
    }
    /**
     * 检查是否可以添加卡牌
     */
    canAddCard(card) {
        return this.getCurrentCost() + card.cost <= this.capacity;
    }
    /**
     * 获取背包状态
     */
    getStatus() {
        return {
            playerId: this.playerId,
            cards: this.getAllCards(),
            currentCost: this.getCurrentCost(),
            capacity: this.capacity,
            remainingCapacity: this.getRemainingCapacity()
        };
    }
}
exports.BackpackManager = BackpackManager;
//# sourceMappingURL=CardDatabase.js.map