{"version": 3, "file": "FactionSystem.js", "sourceRoot": "", "sources": ["../../src/faction/FactionSystem.ts"], "names": [], "mappings": ";;;AAAA,oCAAmE;AAgBnE,OAAO;AACM,QAAA,UAAU,GAAmB;IACxC;QACE,EAAE,EAAE,iBAAiB;QACrB,IAAI,EAAE,IAAI;QACV,WAAW,EAAE,cAAc;QAC3B,aAAa,EAAE,CAAC;QAChB,OAAO,EAAE;YACP;gBACE,IAAI,EAAE,MAAM;gBACZ,KAAK,EAAE,EAAE;gBACT,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,CAAC,CAAC,CAAC,OAAO;aACrB;SACF;KACF;IACD;QACE,EAAE,EAAE,WAAW;QACf,IAAI,EAAE,IAAI;QACV,WAAW,EAAE,cAAc;QAC3B,aAAa,EAAE,CAAC;QAChB,OAAO,EAAE;YACP;gBACE,IAAI,EAAE,MAAM;gBACZ,KAAK,EAAE,EAAE;gBACT,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,CAAC,CAAC;aACb;SACF;KACF;IACD;QACE,EAAE,EAAE,YAAY;QAChB,IAAI,EAAE,IAAI;QACV,WAAW,EAAE,mBAAmB;QAChC,aAAa,EAAE,CAAC;QAChB,OAAO,EAAE;YACP;gBACE,IAAI,EAAE,MAAM;gBACZ,KAAK,EAAE,EAAE;gBACT,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,CAAC;aACZ;SACF;KACF;CACF,CAAC;AAEF,OAAO;AACM,QAAA,UAAU,GAAmB;IACxC;QACE,EAAE,EAAE,cAAc;QAClB,IAAI,EAAE,IAAI;QACV,WAAW,EAAE,cAAc;QAC3B,aAAa,EAAE,CAAC;QAChB,OAAO,EAAE;YACP;gBACE,IAAI,EAAE,MAAM;gBACZ,KAAK,EAAE,EAAE;gBACT,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,CAAC,CAAC;aACb;SACF;KACF;IACD;QACE,EAAE,EAAE,eAAe;QACnB,IAAI,EAAE,IAAI;QACV,WAAW,EAAE,eAAe;QAC5B,aAAa,EAAE,CAAC;QAChB,OAAO,EAAE;YACP;gBACE,IAAI,EAAE,QAAQ;gBACd,KAAK,EAAE,EAAE;gBACT,MAAM,EAAE,OAAO;gBACf,QAAQ,EAAE,CAAC,CAAC;aACb;SACF;KACF;IACD;QACE,EAAE,EAAE,YAAY;QAChB,IAAI,EAAE,IAAI;QACV,WAAW,EAAE,YAAY;QACzB,aAAa,EAAE,CAAC;QAChB,OAAO,EAAE;YACP;gBACE,IAAI,EAAE,SAAS;gBACf,KAAK,EAAE,GAAG;gBACV,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,CAAC,CAAC;aACb;SACF;KACF;CACF,CAAC;AAEF,OAAO;AACM,QAAA,SAAS,GAAmB;IACvC;QACE,EAAE,EAAE,UAAU;QACd,IAAI,EAAE,IAAI;QACV,WAAW,EAAE,eAAe;QAC5B,aAAa,EAAE,CAAC;QAChB,OAAO,EAAE;YACP;gBACE,IAAI,EAAE,SAAS;gBACf,KAAK,EAAE,EAAE;gBACT,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,CAAC,CAAC;aACb;SACF;KACF;IACD;QACE,EAAE,EAAE,gBAAgB;QACpB,IAAI,EAAE,IAAI;QACV,WAAW,EAAE,cAAc;QAC3B,aAAa,EAAE,CAAC;QAChB,OAAO,EAAE;YACP;gBACE,IAAI,EAAE,MAAM;gBACZ,KAAK,EAAE,EAAE;gBACT,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,CAAC,CAAC;aACb;SACF;KACF;IACD;QACE,EAAE,EAAE,gBAAgB;QACpB,IAAI,EAAE,IAAI;QACV,WAAW,EAAE,YAAY;QACzB,aAAa,EAAE,CAAC;QAChB,OAAO,EAAE;YACP;gBACE,IAAI,EAAE,MAAM;gBACZ,KAAK,EAAE,EAAE;gBACT,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,CAAC;aACZ;SACF;KACF;CACF,CAAC;AAEF,OAAO;AACM,QAAA,UAAU,GAAmB;IACxC;QACE,EAAE,EAAE,WAAW;QACf,IAAI,EAAE,IAAI;QACV,WAAW,EAAE,cAAc;QAC3B,aAAa,EAAE,CAAC;QAChB,OAAO,EAAE;YACP;gBACE,IAAI,EAAE,MAAM;gBACZ,KAAK,EAAE,EAAE;gBACT,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,CAAC,CAAC;aACb;SACF;KACF;IACD;QACE,EAAE,EAAE,kBAAkB;QACtB,IAAI,EAAE,IAAI;QACV,WAAW,EAAE,cAAc;QAC3B,aAAa,EAAE,CAAC;QAChB,OAAO,EAAE;YACP;gBACE,IAAI,EAAE,SAAS;gBACf,KAAK,EAAE,EAAE;gBACT,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,CAAC,CAAC;aACb;SACF;KACF;CACF,CAAC;AAEF,SAAS;AACI,QAAA,mBAAmB,GAA+B;IAC7D,CAAC,eAAO,CAAC,KAAK,CAAC,EAAE,CAAC,eAAO,CAAC,IAAI,EAAE,eAAO,CAAC,OAAO,CAAC;IAChD,CAAC,eAAO,CAAC,IAAI,CAAC,EAAE,CAAC,eAAO,CAAC,KAAK,EAAE,eAAO,CAAC,KAAK,CAAC;IAC9C,CAAC,eAAO,CAAC,KAAK,CAAC,EAAE,CAAC,eAAO,CAAC,IAAI,EAAE,eAAO,CAAC,KAAK,CAAC;IAC9C,CAAC,eAAO,CAAC,IAAI,CAAC,EAAE,CAAC,eAAO,CAAC,KAAK,EAAE,eAAO,CAAC,IAAI,CAAC;IAC7C,CAAC,eAAO,CAAC,KAAK,CAAC,EAAE,CAAC,eAAO,CAAC,KAAK,EAAE,eAAO,CAAC,OAAO,CAAC;IACjD,CAAC,eAAO,CAAC,OAAO,CAAC,EAAE,CAAC,eAAO,CAAC,KAAK,EAAE,eAAO,CAAC,IAAI,CAAC;CACjD,CAAC;AAEF;;GAEG;AACH,MAAa,cAAc;IACzB;;OAEG;IACI,MAAM,CAAC,yBAAyB,CAAC,MAAkB;QACxD,MAAM,aAAa,GAAG,IAAI,GAAG,EAAmB,CAAC;QACjD,MAAM,cAAc,GAAG,IAAI,GAAG,EAAyB,CAAC;QAExD,UAAU;QACV,KAAK,MAAM,IAAI,IAAI,MAAM,EAAE,CAAC;YAC1B,MAAM,KAAK,GAAG,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnD,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;QAC7C,CAAC;QAED,SAAS;QACT,KAAK,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,aAAa,EAAE,CAAC;YAC7C,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YAE9C,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBAC3B,IAAI,KAAK,IAAI,KAAK,CAAC,aAAa,EAAE,CAAC;oBACjC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;gBAC9C,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,wBAAwB,CAAC,QAAkB,EAAE,QAAkB;QAC3E,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC;QACzC,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC;QAEzC,SAAS;QACT,MAAM,QAAQ,GAAG,2BAAmB,CAAC,eAAe,CAAC,CAAC;QACtD,IAAI,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;YACvC,OAAO,GAAG,CAAC,CAAC,aAAa;QAC3B,CAAC;QAED,SAAS;QACT,MAAM,gBAAgB,GAAG,2BAAmB,CAAC,eAAe,CAAC,CAAC;QAC9D,IAAI,gBAAgB,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;YAC/C,OAAO,GAAG,CAAC,CAAC,aAAa;QAC3B,CAAC;QAED,OAAO,GAAG,CAAC,CAAC,QAAQ;IACtB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,gBAAgB,CAAC,OAAgB;QAC9C,QAAQ,OAAO,EAAE,CAAC;YAChB,KAAK,eAAO,CAAC,GAAG;gBACd,OAAO,kBAAU,CAAC;YACpB,KAAK,eAAO,CAAC,GAAG;gBACd,OAAO,kBAAU,CAAC;YACpB,KAAK,eAAO,CAAC,EAAE;gBACb,OAAO,iBAAS,CAAC;YACnB,KAAK,eAAO,CAAC,GAAG;gBACd,OAAO,kBAAU,CAAC;YACpB;gBACE,OAAO,EAAE,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,qBAAqB,CAAC,MAAkB;QACpD,MAAM,SAAS,GAAG,IAAI,GAAG,EAAkB,CAAC;QAC5C,MAAM,cAAc,GAAG,IAAI,GAAG,EAAyB,CAAC;QAExD,SAAS;QACT,KAAK,MAAM,IAAI,IAAI,MAAM,EAAE,CAAC;YAC1B,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjC,MAAM,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBACtC,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;YAChC,CAAC;QACH,CAAC;QAED,WAAW;QACX,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,SAAS,EAAE,CAAC;YACrC,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YAC/C,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACvB,cAAc,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YACnC,CAAC;QACH,CAAC;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,aAAa,CAAC,GAAW,EAAE,KAAa;QACrD,QAAQ,GAAG,EAAE,CAAC;YACZ,KAAK,IAAI;gBACP,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;oBACf,OAAO,CAAC;4BACN,IAAI,EAAE,QAAQ;4BACd,KAAK,EAAE,EAAE;4BACT,MAAM,EAAE,MAAM;4BACd,QAAQ,EAAE,CAAC,CAAC;yBACb,CAAC,CAAC;gBACL,CAAC;gBACD,MAAM;YAER,KAAK,IAAI;gBACP,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;oBACf,OAAO,CAAC;4BACN,IAAI,EAAE,MAAM;4BACZ,KAAK,EAAE,EAAE;4BACT,MAAM,EAAE,MAAM;4BACd,QAAQ,EAAE,CAAC,CAAC;yBACb,CAAC,CAAC;gBACL,CAAC;gBACD,MAAM;YAER,KAAK,IAAI;gBACP,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;oBACf,OAAO,CAAC;4BACN,IAAI,EAAE,MAAM;4BACZ,KAAK,EAAE,EAAE;4BACT,MAAM,EAAE,MAAM;4BACd,QAAQ,EAAE,CAAC,CAAC;yBACb,CAAC,CAAC;gBACL,CAAC;gBACD,MAAM;YAER,KAAK,IAAI;gBACP,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;oBACf,OAAO,CAAC;4BACN,IAAI,EAAE,MAAM;4BACZ,KAAK,EAAE,EAAE;4BACT,MAAM,EAAE,MAAM;4BACd,QAAQ,EAAE,CAAC,CAAC;yBACb,CAAC,CAAC;gBACL,CAAC;gBACD,MAAM;QACV,CAAC;QAED,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,oBAAoB,CAAC,IAAc,EAAE,SAAqC;QACtF,MAAM,YAAY,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;QAEjC,KAAK,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,SAAS,EAAE,CAAC;YAC7C,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;gBAC7B,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;oBACpB,KAAK,QAAQ;wBACX,YAAY,CAAC,KAAK,CAAC,MAAM,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC;wBAC/C,MAAM;oBACR,KAAK,MAAM;wBACT,YAAY,CAAC,KAAK,CAAC,MAAM,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC;wBAC/C,MAAM;oBACR,KAAK,MAAM;wBACT,eAAe;wBACf,YAAY,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;wBACvD,YAAY,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;wBACtD,MAAM;gBACV,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,qBAAqB,CAAC,MAAkB;QACpD,MAAM,YAAY,GAAa,EAAE,CAAC;QAElC,OAAO;QACP,MAAM,gBAAgB,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;QAChE,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,gBAAgB,EAAE,CAAC;YAC3C,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;YAC5C,IAAI,KAAK,EAAE,CAAC;gBACV,YAAY,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC;QAED,OAAO;QACP,MAAM,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QACxD,KAAK,MAAM,CAAC,GAAG,CAAC,IAAI,YAAY,EAAE,CAAC;YACjC,YAAY,CAAC,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,CAAC;QACnC,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,aAAa,CAAC,EAAU;QACrC,MAAM,SAAS,GAAG;YAChB,GAAG,kBAAU;YACb,GAAG,kBAAU;YACb,GAAG,iBAAS;YACZ,GAAG,kBAAU;SACd,CAAC;QAEF,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC,IAAI,IAAI,CAAC;IAC1D,CAAC;CACF;AAnND,wCAmNC"}