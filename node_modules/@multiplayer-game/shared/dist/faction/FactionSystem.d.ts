import { Element, HeroCard, SkillEffect } from '../types';
/**
 * 阵营系统和羁绊效果
 * 实现三国阵营的特色和五行相克系统
 */
export interface FactionTrait {
    id: string;
    name: string;
    description: string;
    requiredCount: number;
    effects: SkillEffect[];
}
export declare const SHU_TRAITS: FactionTrait[];
export declare const WEI_TRAITS: FactionTrait[];
export declare const WU_TRAITS: FactionTrait[];
export declare const QUN_TRAITS: FactionTrait[];
export declare const ELEMENT_COUNTER_MAP: Record<Element, Element[]>;
/**
 * 羁绊系统管理器
 */
export declare class SynergyManager {
    /**
     * 计算阵营羁绊效果
     */
    static calculateFactionSynergies(heroes: HeroCard[]): Map<string, SkillEffect[]>;
    /**
     * 计算五行相克效果
     */
    static calculateElementCounters(attacker: HeroCard, defender: HeroCard): number;
    /**
     * 获取阵营特色
     */
    private static getFactionTraits;
    /**
     * 计算羁绊标签效果（如"猛将"、"谋士"等）
     */
    static calculateTagSynergies(heroes: HeroCard[]): Map<string, SkillEffect[]>;
    /**
     * 获取标签羁绊效果
     */
    private static getTagEffects;
    /**
     * 应用羁绊效果到英雄
     */
    static applySynergiesToHero(hero: HeroCard, synergies: Map<string, SkillEffect[]>): HeroCard;
    /**
     * 获取羁绊描述文本
     */
    static getSynergyDescription(heroes: HeroCard[]): string[];
    /**
     * 根据ID查找特色
     */
    private static findTraitById;
}
//# sourceMappingURL=FactionSystem.d.ts.map