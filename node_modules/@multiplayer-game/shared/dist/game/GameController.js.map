{"version": 3, "file": "GameController.js", "sourceRoot": "", "sources": ["../../src/game/GameController.ts"], "names": [], "mappings": ";;;AAAA,oCASkB;AAClB,iEAA8D;AAC9D,+DAA0E;AAC1E,+DAAoF;AACpF,wDAAqE;AAErE;;;GAGG;AACH,MAAa,cAAc;IAQzB,YAAY,IAAc;QALlB,iBAAY,GAA2B,IAAI,CAAC;QAG5C,oBAAe,GAAiC,IAAI,GAAG,EAAE,CAAC;QAGhE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,gBAAgB,GAAG,IAAI,2CAAoB,CAAC,IAAI,CAAC,CAAC;QACvD,IAAI,CAAC,eAAe,GAAG,IAAI,qCAAoB,EAAE,CAAC;QAClD,IAAI,CAAC,WAAW,GAAG,0BAAW,CAAC,WAAW,EAAE,CAAC;QAE7C,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAED;;OAEG;IACK,cAAc;QACpB,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAE3B,OAAO;QACP,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,UAAU;QACV,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAEjC,SAAS;QACT,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAE9B,SAAS;QACT,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,iBAAS,CAAC,SAAS,CAAC;QAC1C,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,sBAAc,CAAC,mBAAmB,CAAC;QAE9D,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACK,gBAAgB;QACtB,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACnD,MAAM,mBAAmB,GAAG,iCAAgB,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAEzE,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACvC,MAAM,QAAQ,GAAG,mBAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACpD,IAAI,QAAQ,EAAE,CAAC;gBACb,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;gBAC3B,MAAM,CAAC,gBAAgB,GAAG,QAAQ,KAAK,sBAAc,CAAC,OAAO,CAAC,CAAC,SAAS;gBAExE,UAAU;gBACV,IAAI,CAAC,eAAe,CAAC,sBAAsB,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;gBACjE,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBAExE,OAAO,CAAC,GAAG,CAAC,MAAM,MAAM,CAAC,QAAQ,UAAU,QAAQ,EAAE,CAAC,CAAC;YACzD,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,yBAAyB;QAC/B,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACvC,MAAM,QAAQ,GAAG,IAAI,8BAAe,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,gBAAgB,CAAC,CAAC;YACzE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,sBAAsB;QAC5B,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACrD,IAAI,CAAC,QAAQ;gBAAE,SAAS;YAExB,cAAc;YACd,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAC5E,MAAM,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,aAAa;YAE5D,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;gBAChC,IAAI,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC9B,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACvB,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACnC,CAAC;YACH,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,MAAM,MAAM,CAAC,QAAQ,YAAY,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC/F,CAAC;IACH,CAAC;IAED;;OAEG;IACI,SAAS;QACd,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACxB,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC;IACpC,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,QAAgB,EAAE,MAAc,EAAE,IAAS;QACnE,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAC;QAC9D,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YAC/B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,WAAW;gBACd,OAAO,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAC7C,KAAK,oBAAoB;gBACvB,OAAO,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YACrD,KAAK,UAAU;gBACb,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAC5C,KAAK,cAAc;gBACjB,OAAO,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAChD,KAAK,UAAU;gBACb,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YACtC;gBACE,OAAO,KAAK,CAAC;QACjB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,QAAgB,EAAE,IAAkD;QACzF,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;IACpF,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,QAAgB,EAAE,IAA0C;QACzF,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAC;QAC9D,IAAI,CAAC,MAAM;YAAE,OAAO,KAAK,CAAC;QAE1B,WAAW;QACX,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS;QACT,MAAM,KAAK,GAAG,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,OAAO,CAAC,CAAC;QACrE,IAAI,KAAK,EAAE,IAAI,EAAE,CAAC;YAChB,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,MAAM,CAAC,SAAS,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;gBAC/D,OAAO,KAAK,CAAC;YACf,CAAC;YACD,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,IAAI,MAAM,CAAC,SAAS,CAAC,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;gBACjF,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAED,OAAO;QACP,IAAI,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YAC1D,OAAO;YACP,IAAI,KAAK,EAAE,IAAI,EAAE,CAAC;gBAChB,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;oBACpB,MAAM,CAAC,SAAS,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;gBAC3C,CAAC;gBACD,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;oBAC1B,MAAM,CAAC,SAAS,CAAC,UAAU,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;gBACvD,CAAC;YACH,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,QAAQ,YAAY,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YAC3D,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,QAAgB,EAAE,IAAwB;QAC9D,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAC;QAC9D,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,MAAM,IAAI,CAAC,QAAQ;YAAE,OAAO,KAAK,CAAC;QAEvC,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACnD,IAAI,CAAC,IAAI;YAAE,OAAO,KAAK,CAAC;QAExB,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAElD,WAAW;QACX,IAAI,MAAM,CAAC,SAAS,CAAC,IAAI,GAAG,KAAK,EAAE,CAAC;YAClC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS;QACT,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO;QACP,MAAM,CAAC,SAAS,CAAC,IAAI,IAAI,KAAK,CAAC;QAC/B,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACvB,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEjC,OAAO,CAAC,GAAG,CAAC,MAAM,MAAM,CAAC,QAAQ,UAAU,IAAI,CAAC,IAAI,KAAK,KAAK,KAAK,CAAC,CAAC;QACrE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,QAAgB,EAAE,IAAoE;QAC9G,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,KAAK,iBAAS,CAAC,aAAa,EAAE,CAAC;YACpD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAC;QAC9D,IAAI,CAAC,MAAM;YAAE,OAAO,KAAK,CAAC;QAE1B,UAAU;QACV,MAAM,aAAa,GAAe,EAAE,CAAC;QACrC,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YAC1C,MAAM,IAAI,GAAG,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,UAAU,CAAC,MAAM,CAAC,CAAC;YACzE,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;gBACjC,aAAa,CAAC,IAAI,CAAC,IAAgB,CAAC,CAAC;YACvC,CAAC;QACH,CAAC;QAED,gBAAgB;QACf,MAAc,CAAC,aAAa,GAAG,aAAa,CAAC;QAE9C,OAAO,CAAC,GAAG,CAAC,MAAM,MAAM,CAAC,QAAQ,UAAU,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC5F,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,QAAgB;QACpC,WAAW;QACX,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAE/C,WAAW;QACX,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;QAChC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,QAAkB;QACrC,OAAO,CAAC,GAAG,CAAC,WAAW,QAAQ,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;QAE1D,aAAa;QACb,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,iBAAS,CAAC,WAAW,CAAC;QAE5C,YAAY;QACZ,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QAEvD,IAAI,aAAa,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YAC9B,WAAW;YACX,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,QAAkB;QAC1C,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC;QACjG,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK;YAAE,OAAO,EAAE,CAAC;QAEpC,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QAChE,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,OAAiB;QAC7C,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,iBAAS,CAAC,aAAa,CAAC;QAE9C,OAAO,CAAC,GAAG,CAAC,qBAAqB,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAE5E,UAAU;QACV,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QAChC,CAAC,EAAE,mBAAW,CAAC,iBAAiB,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAAC,OAAiB;QAC7C,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,iBAAS,CAAC,WAAW,CAAC;QAE5C,SAAS;QACT,IAAI,CAAC,YAAY,GAAG,IAAI,iCAAe,EAAE,CAAC;QAE1C,SAAS;QACT,MAAM,WAAW,GAAG,IAAI,GAAG,EAAsB,CAAC;QAClD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,MAAM,aAAa,GAAI,MAAc,CAAC,aAAa,IAAI,EAAE,CAAC;YAC1D,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,aAAa,CAAC,CAAC;QAC5C,CAAC;QAED,QAAQ;QACR,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QAEhD,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAEzB,OAAO;QACP,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;QAEzD,SAAS;QACT,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,MAAoB,EAAE,OAAiB;QAChE,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,iBAAS,CAAC,aAAa,CAAC;QAE9C,OAAO,CAAC,GAAG,CAAC,gBAAgB,MAAM,CAAC,MAAM,IAAI,IAAI,EAAE,CAAC,CAAC;QAErD,QAAQ;QACR,KAAK,MAAM,CAAC,QAAQ,EAAE,GAAG,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC;YAChE,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAC;YAC9D,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,CAAC,SAAS,CAAC,UAAU,IAAI,GAAG,CAAC;gBACnC,OAAO,CAAC,GAAG,CAAC,MAAM,MAAM,CAAC,QAAQ,OAAO,GAAG,MAAM,CAAC,CAAC;YACrD,CAAC;QACH,CAAC;QAED,SAAS;QACT,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAClB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;gBAC7B,IAAI,MAAM,CAAC,EAAE,KAAK,MAAM,CAAC,MAAM,EAAE,CAAC;oBAChC,oBAAoB;oBACpB,SAAS;gBACX,CAAC;YACH,CAAC;QACH,CAAC;QAED,WAAW;QACX,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACrF,IAAI,aAAa,CAAC,MAAM,EAAE,CAAC;YACzB,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;QAC3D,CAAC;aAAM,CAAC;YACN,SAAS;YACT,UAAU,CAAC,GAAG,EAAE;gBACd,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,iBAAS,CAAC,SAAS,CAAC;gBAC1C,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,sBAAc,CAAC,aAAa,CAAC;YAC1D,CAAC,EAAE,IAAI,CAAC,CAAC;QACX,CAAC;IACH,CAAC;IAED;;OAEG;IACK,OAAO,CAAC,QAAgB,EAAE,MAAc;QAC9C,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,iBAAS,CAAC,QAAQ,CAAC;QAEzC,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAC;QAC9D,OAAO,CAAC,GAAG,CAAC,gBAAgB,MAAM,EAAE,QAAQ,MAAM,MAAM,EAAE,CAAC,CAAC;QAE5D,SAAS;QACT,kBAAkB;IACpB,CAAC;IAED;;OAEG;IACI,YAAY;QACjB,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,iBAAiB,CAAC,QAAgB;QACvC,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,SAAS,EAAE,CAAC;IACzD,CAAC;IAED;;OAEG;IACI,OAAO;QACZ,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;QACjC,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IAC7B,CAAC;CACF;AA1YD,wCA0YC"}