import { GameRoom, HeroCard, Position } from '../types';
/**
 * 游戏主控制器
 * 统一管理SLG战略层和自走棋战斗层的游戏逻辑
 */
export declare class GameController {
    private room;
    private strategicManager;
    private combatEngine;
    private identityManager;
    private cardManager;
    private playerBackpacks;
    constructor(room: GameRoom);
    /**
     * 初始化游戏
     */
    private initializeGame;
    /**
     * 分配身份
     */
    private assignIdentities;
    /**
     * 初始化玩家背包
     */
    private initializePlayerBackpacks;
    /**
     * 分配初始卡牌
     */
    private distributeInitialCards;
    /**
     * 开始游戏
     */
    startGame(): void;
    /**
     * 处理玩家行动
     */
    handlePlayerAction(playerId: string, action: string, data: any): boolean;
    /**
     * 处理军队移动
     */
    private handleMoveArmy;
    /**
     * 处理身份技能使用
     */
    private handleUseIdentitySkill;
    /**
     * 处理购买卡牌
     */
    private handleBuyCard;
    /**
     * 处理卡牌部署
     */
    private handleDeployCards;
    /**
     * 处理结束回合
     */
    private handleEndTurn;
    /**
     * 触发战斗
     */
    triggerCombat(position: Position): void;
    /**
     * 找到参与战斗的玩家
     */
    private findCombatPlayers;
    /**
     * 开始战斗部署阶段
     */
    private startCombatDeployment;
    /**
     * 开始自动战斗
     */
    private startAutoCombat;
    /**
     * 处理战斗结果
     */
    private handleCombatResult;
    /**
     * 结束游戏
     */
    private endGame;
    /**
     * 获取游戏状态
     */
    getGameState(): GameRoom;
    /**
     * 获取玩家背包状态
     */
    getPlayerBackpack(playerId: string): {
        playerId: string;
        cards: (HeroCard | import("../types").SpellCard | import("../types").StrategicCard)[];
        currentCost: number;
        capacity: number;
        remainingCapacity: number;
    } | undefined;
    /**
     * 清理资源
     */
    dispose(): void;
}
//# sourceMappingURL=GameController.d.ts.map