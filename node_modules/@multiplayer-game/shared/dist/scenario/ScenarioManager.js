"use strict";
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScenarioManager = exports.SCENARIO_YILING = exports.SCENARIO_GUANDU = exports.SCENARIO_RED_CLIFF = exports.SCENARIO_THREE_KINGDOMS = void 0;
const types_1 = require("../types");
/**
 * 剧本管理器
 * 管理不同的游戏剧本，包括三国演义、赤壁之战等
 */
// 三国演义剧本
exports.SCENARIO_THREE_KINGDOMS = {
    id: 'three_kingdoms',
    name: '三国演义',
    description: '经典三国题材，群雄逐鹿，身份暗战',
    mapSize: { width: 16, height: 8 },
    maxTurns: 120,
    specialRules: [
        {
            id: 'emperor_bonus',
            name: '天子威仪',
            description: '主公每回合额外获得1金币',
            type: 'resource_bonus',
            conditions: { identity: 'emperor' },
            effects: { goldPerTurn: 1 }
        },
        {
            id: 'rebel_unity',
            name: '反贼联盟',
            description: '反贼之间不会因为相邻而触发战斗',
            type: 'unit_bonus',
            conditions: { identity: 'rebel' },
            effects: { noFriendlyFire: true }
        }
    ],
    initialResources: { gold: 50, population: 100 },
    availableFactions: [types_1.Faction.SHU, types_1.Faction.WEI, types_1.Faction.WU, types_1.Faction.QUN],
    weatherEvents: [
        { turn: 15, weather: types_1.WeatherType.RAIN, duration: 3 },
        { turn: 30, weather: types_1.WeatherType.SANDSTORM, duration: 2 },
        { turn: 60, weather: types_1.WeatherType.WINTER, duration: 5 },
        { turn: 90, weather: types_1.WeatherType.THUNDER, duration: 2 }
    ]
};
// 赤壁之战剧本
exports.SCENARIO_RED_CLIFF = {
    id: 'red_cliff',
    name: '赤壁之战',
    description: '火攻连环，以少胜多的经典战役',
    mapSize: { width: 12, height: 10 },
    maxTurns: 80,
    specialRules: [
        {
            id: 'fire_attack_bonus',
            name: '火攻战术',
            description: '火系法术伤害提升50%',
            type: 'terrain_effect',
            conditions: { spellType: 'fire' },
            effects: { damageMultiplier: 1.5 }
        },
        {
            id: 'naval_combat',
            name: '水战精通',
            description: '吴国武将在河流地形攻击力+20%',
            type: 'unit_bonus',
            conditions: { faction: 'wu', terrain: 'river' },
            effects: { attackBonus: 0.2 }
        },
        {
            id: 'chain_ships',
            name: '连环船',
            description: '河流地形的单位受到火焰伤害时，伤害会传播到相邻单位',
            type: 'terrain_effect',
            conditions: { terrain: 'river', damageType: 'fire' },
            effects: { spreadDamage: true }
        }
    ],
    initialResources: { gold: 40, population: 80 },
    availableFactions: [types_1.Faction.SHU, types_1.Faction.WEI, types_1.Faction.WU],
    weatherEvents: [
        { turn: 10, weather: types_1.WeatherType.CLEAR, duration: 5 },
        { turn: 25, weather: types_1.WeatherType.RAIN, duration: 3 },
        { turn: 50, weather: types_1.WeatherType.CLEAR, duration: 10 }
    ]
};
// 官渡之战剧本
exports.SCENARIO_GUANDU = {
    id: 'guandu',
    name: '官渡之战',
    description: '曹操vs袁绍，智谋与实力的较量',
    mapSize: { width: 14, height: 6 },
    maxTurns: 60,
    specialRules: [
        {
            id: 'supply_line',
            name: '补给线',
            description: '控制更多粮草地块的一方，全军攻击力+10%',
            type: 'victory_condition',
            conditions: { terrainControl: 'farm' },
            effects: { armyBonus: 0.1 }
        },
        {
            id: 'night_raid',
            name: '夜袭',
            description: '每10回合可以发动一次夜袭，无视地形移动',
            type: 'unit_bonus',
            conditions: { turnMod: 10 },
            effects: { ignoreTerrainMovement: true }
        }
    ],
    initialResources: { gold: 60, population: 120 },
    availableFactions: [types_1.Faction.WEI, types_1.Faction.QUN],
    weatherEvents: [
        { turn: 20, weather: types_1.WeatherType.SANDSTORM, duration: 4 },
        { turn: 40, weather: types_1.WeatherType.WINTER, duration: 3 }
    ]
};
// 夷陵之战剧本
exports.SCENARIO_YILING = {
    id: 'yiling',
    name: '夷陵之战',
    description: '刘备伐吴，陆逊火烧连营',
    mapSize: { width: 18, height: 6 },
    maxTurns: 100,
    specialRules: [
        {
            id: 'forest_fire',
            name: '火烧连营',
            description: '森林地形容易起火，火焰会蔓延',
            type: 'terrain_effect',
            conditions: { terrain: 'forest' },
            effects: { fireSpread: true, fireDamage: 1.5 }
        },
        {
            id: 'revenge_fury',
            name: '复仇之怒',
            description: '蜀军初期攻击力+30%，但每回合递减5%',
            type: 'unit_bonus',
            conditions: { faction: 'shu', turnLimit: 10 },
            effects: { attackBonus: 0.3, decay: 0.05 }
        }
    ],
    initialResources: { gold: 45, population: 90 },
    availableFactions: [types_1.Faction.SHU, types_1.Faction.WU],
    weatherEvents: [
        { turn: 5, weather: types_1.WeatherType.CLEAR, duration: 15 },
        { turn: 35, weather: types_1.WeatherType.RAIN, duration: 5 },
        { turn: 70, weather: types_1.WeatherType.THUNDER, duration: 3 }
    ]
};
/**
 * 剧本管理器类
 */
class ScenarioManager {
    /**
     * 获取所有可用剧本
     */
    static getAllScenarios() {
        return Array.from(this.scenarios.values());
    }
    /**
     * 根据ID获取剧本
     */
    static getScenario(id) {
        return this.scenarios.get(id) || null;
    }
    /**
     * 获取推荐剧本（根据玩家数量）
     */
    static getRecommendedScenario(playerCount) {
        if (playerCount === 8) {
            return exports.SCENARIO_THREE_KINGDOMS; // 8人身份局
        }
        else if (playerCount <= 4) {
            return exports.SCENARIO_RED_CLIFF; // 小规模战斗
        }
        else if (playerCount === 2) {
            return exports.SCENARIO_GUANDU; // 1v1对决
        }
        else {
            return exports.SCENARIO_YILING; // 中等规模
        }
    }
    /**
     * 验证剧本规则
     */
    static validateScenarioRules(scenario, gameState) {
        for (const rule of scenario.specialRules) {
            if (!this.checkRuleConditions(rule, gameState)) {
                return false;
            }
        }
        return true;
    }
    /**
     * 应用剧本规则效果
     */
    static applyScenarioEffects(scenario, gameState) {
        let modifiedState = { ...gameState };
        for (const rule of scenario.specialRules) {
            if (this.checkRuleConditions(rule, gameState)) {
                modifiedState = this.applyRuleEffects(rule, modifiedState);
            }
        }
        return modifiedState;
    }
    /**
     * 检查规则条件
     */
    static checkRuleConditions(rule, gameState) {
        const { conditions } = rule;
        // 检查身份条件
        if (conditions.identity && gameState.currentPlayer?.identity !== conditions.identity) {
            return false;
        }
        // 检查阵营条件
        if (conditions.faction && gameState.currentPlayer?.faction !== conditions.faction) {
            return false;
        }
        // 检查地形条件
        if (conditions.terrain && gameState.currentTerrain !== conditions.terrain) {
            return false;
        }
        // 检查回合条件
        if (conditions.turnMod && gameState.currentTurn % conditions.turnMod !== 0) {
            return false;
        }
        return true;
    }
    /**
     * 应用规则效果
     */
    static applyRuleEffects(rule, gameState) {
        const { effects } = rule;
        const newState = { ...gameState };
        // 应用资源加成
        if (effects.goldPerTurn && newState.currentPlayer) {
            newState.currentPlayer.resources.gold += effects.goldPerTurn;
        }
        // 应用攻击力加成
        if (effects.attackBonus && newState.currentPlayer?.armies) {
            for (const army of newState.currentPlayer.armies) {
                for (const card of army.cards) {
                    if (card.type === 'hero') {
                        card.stats.attack *= (1 + effects.attackBonus);
                    }
                }
            }
        }
        // 应用伤害倍数
        if (effects.damageMultiplier && newState.combatState) {
            newState.combatState.damageMultiplier = effects.damageMultiplier;
        }
        return newState;
    }
    /**
     * 获取剧本的天气事件
     */
    static getWeatherEvents(scenarioId, currentTurn) {
        const scenario = this.getScenario(scenarioId);
        if (!scenario)
            return [];
        return scenario.weatherEvents.filter(event => event.turn <= currentTurn &&
            event.turn + event.duration > currentTurn);
    }
    /**
     * 检查剧本胜利条件
     */
    static checkScenarioVictory(scenario, gameState) {
        // 检查特殊胜利条件
        for (const rule of scenario.specialRules) {
            if (rule.type === 'victory_condition') {
                const result = this.checkVictoryRule(rule, gameState);
                if (result.winner) {
                    return result;
                }
            }
        }
        // 默认胜利条件（回合数限制）
        if (gameState.currentTurn >= scenario.maxTurns) {
            return this.calculateFinalScore(gameState);
        }
        return { winner: null, reason: '' };
    }
    /**
     * 检查胜利规则
     */
    static checkVictoryRule(rule, gameState) {
        // 这里可以实现具体的胜利条件检查逻辑
        // 例如：控制特定地块数量、消灭特定目标等
        return { winner: null, reason: '' };
    }
    /**
     * 计算最终得分
     */
    static calculateFinalScore(gameState) {
        // 根据领土、资源、存活单位等计算得分
        let maxScore = 0;
        let winner = null;
        for (const player of gameState.players) {
            if (!player.isAlive)
                continue;
            const score = player.territories.length * 10 +
                player.resources.gold * 1 +
                player.armies.length * 20;
            if (score > maxScore) {
                maxScore = score;
                winner = player.id;
            }
        }
        return {
            winner,
            reason: winner ? '回合结束，根据综合得分获胜' : '平局'
        };
    }
}
exports.ScenarioManager = ScenarioManager;
_a = ScenarioManager;
ScenarioManager.scenarios = new Map();
(() => {
    // 初始化所有剧本
    _a.scenarios.set(exports.SCENARIO_THREE_KINGDOMS.id, exports.SCENARIO_THREE_KINGDOMS);
    _a.scenarios.set(exports.SCENARIO_RED_CLIFF.id, exports.SCENARIO_RED_CLIFF);
    _a.scenarios.set(exports.SCENARIO_GUANDU.id, exports.SCENARIO_GUANDU);
    _a.scenarios.set(exports.SCENARIO_YILING.id, exports.SCENARIO_YILING);
})();
//# sourceMappingURL=ScenarioManager.js.map