{"version": 3, "file": "ScenarioManager.js", "sourceRoot": "", "sources": ["../../src/scenario/ScenarioManager.ts"], "names": [], "mappings": ";;;;AAAA,oCAQkB;AAElB;;;GAGG;AAEH,SAAS;AACI,QAAA,uBAAuB,GAAa;IAC/C,EAAE,EAAE,gBAAgB;IACpB,IAAI,EAAE,MAAM;IACZ,WAAW,EAAE,kBAAkB;IAC/B,OAAO,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;IACjC,QAAQ,EAAE,GAAG;IACb,YAAY,EAAE;QACZ;YACE,EAAE,EAAE,eAAe;YACnB,IAAI,EAAE,MAAM;YACZ,WAAW,EAAE,cAAc;YAC3B,IAAI,EAAE,gBAAgB;YACtB,UAAU,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE;YACnC,OAAO,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE;SAC5B;QACD;YACE,EAAE,EAAE,aAAa;YACjB,IAAI,EAAE,MAAM;YACZ,WAAW,EAAE,iBAAiB;YAC9B,IAAI,EAAE,YAAY;YAClB,UAAU,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE;YACjC,OAAO,EAAE,EAAE,cAAc,EAAE,IAAI,EAAE;SAClC;KACF;IACD,gBAAgB,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,UAAU,EAAE,GAAG,EAAE;IAC/C,iBAAiB,EAAE,CAAC,eAAO,CAAC,GAAG,EAAE,eAAO,CAAC,GAAG,EAAE,eAAO,CAAC,EAAE,EAAE,eAAO,CAAC,GAAG,CAAC;IACtE,aAAa,EAAE;QACb,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,mBAAW,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,EAAE;QACpD,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,mBAAW,CAAC,SAAS,EAAE,QAAQ,EAAE,CAAC,EAAE;QACzD,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,mBAAW,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,EAAE;QACtD,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,mBAAW,CAAC,OAAO,EAAE,QAAQ,EAAE,CAAC,EAAE;KACxD;CACF,CAAC;AAEF,SAAS;AACI,QAAA,kBAAkB,GAAa;IAC1C,EAAE,EAAE,WAAW;IACf,IAAI,EAAE,MAAM;IACZ,WAAW,EAAE,gBAAgB;IAC7B,OAAO,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE;IAClC,QAAQ,EAAE,EAAE;IACZ,YAAY,EAAE;QACZ;YACE,EAAE,EAAE,mBAAmB;YACvB,IAAI,EAAE,MAAM;YACZ,WAAW,EAAE,aAAa;YAC1B,IAAI,EAAE,gBAAgB;YACtB,UAAU,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;YACjC,OAAO,EAAE,EAAE,gBAAgB,EAAE,GAAG,EAAE;SACnC;QACD;YACE,EAAE,EAAE,cAAc;YAClB,IAAI,EAAE,MAAM;YACZ,WAAW,EAAE,kBAAkB;YAC/B,IAAI,EAAE,YAAY;YAClB,UAAU,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YAC/C,OAAO,EAAE,EAAE,WAAW,EAAE,GAAG,EAAE;SAC9B;QACD;YACE,EAAE,EAAE,aAAa;YACjB,IAAI,EAAE,KAAK;YACX,WAAW,EAAE,2BAA2B;YACxC,IAAI,EAAE,gBAAgB;YACtB,UAAU,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE;YACpD,OAAO,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE;SAChC;KACF;IACD,gBAAgB,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE;IAC9C,iBAAiB,EAAE,CAAC,eAAO,CAAC,GAAG,EAAE,eAAO,CAAC,GAAG,EAAE,eAAO,CAAC,EAAE,CAAC;IACzD,aAAa,EAAE;QACb,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,mBAAW,CAAC,KAAK,EAAE,QAAQ,EAAE,CAAC,EAAE;QACrD,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,mBAAW,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,EAAE;QACpD,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,mBAAW,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAE;KACvD;CACF,CAAC;AAEF,SAAS;AACI,QAAA,eAAe,GAAa;IACvC,EAAE,EAAE,QAAQ;IACZ,IAAI,EAAE,MAAM;IACZ,WAAW,EAAE,iBAAiB;IAC9B,OAAO,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;IACjC,QAAQ,EAAE,EAAE;IACZ,YAAY,EAAE;QACZ;YACE,EAAE,EAAE,aAAa;YACjB,IAAI,EAAE,KAAK;YACX,WAAW,EAAE,uBAAuB;YACpC,IAAI,EAAE,mBAAmB;YACzB,UAAU,EAAE,EAAE,cAAc,EAAE,MAAM,EAAE;YACtC,OAAO,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE;SAC5B;QACD;YACE,EAAE,EAAE,YAAY;YAChB,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,sBAAsB;YACnC,IAAI,EAAE,YAAY;YAClB,UAAU,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;YAC3B,OAAO,EAAE,EAAE,qBAAqB,EAAE,IAAI,EAAE;SACzC;KACF;IACD,gBAAgB,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,UAAU,EAAE,GAAG,EAAE;IAC/C,iBAAiB,EAAE,CAAC,eAAO,CAAC,GAAG,EAAE,eAAO,CAAC,GAAG,CAAC;IAC7C,aAAa,EAAE;QACb,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,mBAAW,CAAC,SAAS,EAAE,QAAQ,EAAE,CAAC,EAAE;QACzD,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,mBAAW,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,EAAE;KACvD;CACF,CAAC;AAEF,SAAS;AACI,QAAA,eAAe,GAAa;IACvC,EAAE,EAAE,QAAQ;IACZ,IAAI,EAAE,MAAM;IACZ,WAAW,EAAE,aAAa;IAC1B,OAAO,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;IACjC,QAAQ,EAAE,GAAG;IACb,YAAY,EAAE;QACZ;YACE,EAAE,EAAE,aAAa;YACjB,IAAI,EAAE,MAAM;YACZ,WAAW,EAAE,gBAAgB;YAC7B,IAAI,EAAE,gBAAgB;YACtB,UAAU,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE;YACjC,OAAO,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,EAAE;SAC/C;QACD;YACE,EAAE,EAAE,cAAc;YAClB,IAAI,EAAE,MAAM;YACZ,WAAW,EAAE,sBAAsB;YACnC,IAAI,EAAE,YAAY;YAClB,UAAU,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,EAAE,EAAE;YAC7C,OAAO,EAAE,EAAE,WAAW,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE;SAC3C;KACF;IACD,gBAAgB,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE;IAC9C,iBAAiB,EAAE,CAAC,eAAO,CAAC,GAAG,EAAE,eAAO,CAAC,EAAE,CAAC;IAC5C,aAAa,EAAE;QACb,EAAE,IAAI,EAAE,CAAC,EAAE,OAAO,EAAE,mBAAW,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAE;QACrD,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,mBAAW,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,EAAE;QACpD,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,mBAAW,CAAC,OAAO,EAAE,QAAQ,EAAE,CAAC,EAAE;KACxD;CACF,CAAC;AAEF;;GAEG;AACH,MAAa,eAAe;IAW1B;;OAEG;IACI,MAAM,CAAC,eAAe;QAC3B,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,WAAW,CAAC,EAAU;QAClC,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC;IACxC,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,sBAAsB,CAAC,WAAmB;QACtD,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC;YACtB,OAAO,+BAAuB,CAAC,CAAC,QAAQ;QAC1C,CAAC;aAAM,IAAI,WAAW,IAAI,CAAC,EAAE,CAAC;YAC5B,OAAO,0BAAkB,CAAC,CAAC,QAAQ;QACrC,CAAC;aAAM,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC;YAC7B,OAAO,uBAAe,CAAC,CAAC,QAAQ;QAClC,CAAC;aAAM,CAAC;YACN,OAAO,uBAAe,CAAC,CAAC,OAAO;QACjC,CAAC;IACH,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,qBAAqB,CAAC,QAAkB,EAAE,SAAc;QACpE,KAAK,MAAM,IAAI,IAAI,QAAQ,CAAC,YAAY,EAAE,CAAC;YACzC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,CAAC;gBAC/C,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,oBAAoB,CAAC,QAAkB,EAAE,SAAc;QACnE,IAAI,aAAa,GAAG,EAAE,GAAG,SAAS,EAAE,CAAC;QAErC,KAAK,MAAM,IAAI,IAAI,QAAQ,CAAC,YAAY,EAAE,CAAC;YACzC,IAAI,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,CAAC;gBAC9C,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;YAC7D,CAAC;QACH,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,mBAAmB,CAAC,IAAkB,EAAE,SAAc;QACnE,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;QAE5B,SAAS;QACT,IAAI,UAAU,CAAC,QAAQ,IAAI,SAAS,CAAC,aAAa,EAAE,QAAQ,KAAK,UAAU,CAAC,QAAQ,EAAE,CAAC;YACrF,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS;QACT,IAAI,UAAU,CAAC,OAAO,IAAI,SAAS,CAAC,aAAa,EAAE,OAAO,KAAK,UAAU,CAAC,OAAO,EAAE,CAAC;YAClF,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS;QACT,IAAI,UAAU,CAAC,OAAO,IAAI,SAAS,CAAC,cAAc,KAAK,UAAU,CAAC,OAAO,EAAE,CAAC;YAC1E,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS;QACT,IAAI,UAAU,CAAC,OAAO,IAAI,SAAS,CAAC,WAAW,GAAG,UAAU,CAAC,OAAO,KAAK,CAAC,EAAE,CAAC;YAC3E,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,gBAAgB,CAAC,IAAkB,EAAE,SAAc;QAChE,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;QACzB,MAAM,QAAQ,GAAG,EAAE,GAAG,SAAS,EAAE,CAAC;QAElC,SAAS;QACT,IAAI,OAAO,CAAC,WAAW,IAAI,QAAQ,CAAC,aAAa,EAAE,CAAC;YAClD,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,IAAI,OAAO,CAAC,WAAW,CAAC;QAC/D,CAAC;QAED,UAAU;QACV,IAAI,OAAO,CAAC,WAAW,IAAI,QAAQ,CAAC,aAAa,EAAE,MAAM,EAAE,CAAC;YAC1D,KAAK,MAAM,IAAI,IAAI,QAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;gBACjD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;oBAC9B,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;wBACzB,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,CAAC,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC;oBACjD,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,SAAS;QACT,IAAI,OAAO,CAAC,gBAAgB,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;YACrD,QAAQ,CAAC,WAAW,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;QACnE,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,gBAAgB,CAAC,UAAkB,EAAE,WAAmB;QACpE,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAC9C,IAAI,CAAC,QAAQ;YAAE,OAAO,EAAE,CAAC;QAEzB,OAAO,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAC3C,KAAK,CAAC,IAAI,IAAI,WAAW;YACzB,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,QAAQ,GAAG,WAAW,CAC1C,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,oBAAoB,CAAC,QAAkB,EAAE,SAAc;QACnE,WAAW;QACX,KAAK,MAAM,IAAI,IAAI,QAAQ,CAAC,YAAY,EAAE,CAAC;YACzC,IAAI,IAAI,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;gBACtC,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;gBACtD,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;oBAClB,OAAO,MAAM,CAAC;gBAChB,CAAC;YACH,CAAC;QACH,CAAC;QAED,gBAAgB;QAChB,IAAI,SAAS,CAAC,WAAW,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;YAC/C,OAAO,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;QAC7C,CAAC;QAED,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;IACtC,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,gBAAgB,CAAC,IAAkB,EAAE,SAAc;QAChE,oBAAoB;QACpB,sBAAsB;QACtB,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;IACtC,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,mBAAmB,CAAC,SAAc;QAC/C,oBAAoB;QACpB,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAI,MAAM,GAAG,IAAI,CAAC;QAElB,KAAK,MAAM,MAAM,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;YACvC,IAAI,CAAC,MAAM,CAAC,OAAO;gBAAE,SAAS;YAE9B,MAAM,KAAK,GACT,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,EAAE;gBAC9B,MAAM,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC;gBACzB,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,EAAE,CAAC;YAE5B,IAAI,KAAK,GAAG,QAAQ,EAAE,CAAC;gBACrB,QAAQ,GAAG,KAAK,CAAC;gBACjB,MAAM,GAAG,MAAM,CAAC,EAAE,CAAC;YACrB,CAAC;QACH,CAAC;QAED,OAAO;YACL,MAAM;YACN,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI;SACxC,CAAC;IACJ,CAAC;;AArMH,0CAsMC;;AArMgB,yBAAS,GAA0B,IAAI,GAAG,EAAE,AAAnC,CAAoC;AAE5D;IACE,UAAU;IACV,GAAK,SAAS,CAAC,GAAG,CAAC,+BAAuB,CAAC,EAAE,EAAE,+BAAuB,CAAC,CAAC;IACxE,GAAK,SAAS,CAAC,GAAG,CAAC,0BAAkB,CAAC,EAAE,EAAE,0BAAkB,CAAC,CAAC;IAC9D,GAAK,SAAS,CAAC,GAAG,CAAC,uBAAe,CAAC,EAAE,EAAE,uBAAe,CAAC,CAAC;IACxD,GAAK,SAAS,CAAC,GAAG,CAAC,uBAAe,CAAC,EAAE,EAAE,uBAAe,CAAC,CAAC;AAC1D,CAAC,GAAA,CAAA"}