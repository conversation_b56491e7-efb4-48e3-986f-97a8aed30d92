import { Scenario, WeatherEvent } from '../types';
/**
 * 剧本管理器
 * 管理不同的游戏剧本，包括三国演义、赤壁之战等
 */
export declare const SCENARIO_THREE_KINGDOMS: Scenario;
export declare const SCENARIO_RED_CLIFF: Scenario;
export declare const SCENARIO_GUANDU: Scenario;
export declare const SCENARIO_YILING: Scenario;
/**
 * 剧本管理器类
 */
export declare class ScenarioManager {
    private static scenarios;
    /**
     * 获取所有可用剧本
     */
    static getAllScenarios(): Scenario[];
    /**
     * 根据ID获取剧本
     */
    static getScenario(id: string): Scenario | null;
    /**
     * 获取推荐剧本（根据玩家数量）
     */
    static getRecommendedScenario(playerCount: number): Scenario;
    /**
     * 验证剧本规则
     */
    static validateScenarioRules(scenario: Scenario, gameState: any): boolean;
    /**
     * 应用剧本规则效果
     */
    static applyScenarioEffects(scenario: Scenario, gameState: any): any;
    /**
     * 检查规则条件
     */
    private static checkRuleConditions;
    /**
     * 应用规则效果
     */
    private static applyRuleEffects;
    /**
     * 获取剧本的天气事件
     */
    static getWeatherEvents(scenarioId: string, currentTurn: number): WeatherEvent[];
    /**
     * 检查剧本胜利条件
     */
    static checkScenarioVictory(scenario: Scenario, gameState: any): {
        winner: string | null;
        reason: string;
    };
    /**
     * 检查胜利规则
     */
    private static checkVictoryRule;
    /**
     * 计算最终得分
     */
    private static calculateFinalScore;
}
//# sourceMappingURL=ScenarioManager.d.ts.map