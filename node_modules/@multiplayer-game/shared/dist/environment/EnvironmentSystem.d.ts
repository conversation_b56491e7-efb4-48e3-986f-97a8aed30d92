import { WeatherType, TerrainType, HeroCard, SkillEffect } from '../types';
/**
 * 环境系统
 * 管理天气和地形对游戏的影响
 */
export interface WeatherEffect {
    type: WeatherType;
    name: string;
    description: string;
    resourceMultiplier: {
        gold: number;
        population: number;
    };
    combatEffects: SkillEffect[];
    movementEffects: {
        costMultiplier: number;
        rangeModifier: number;
    };
    specialEffects: string[];
}
export interface TerrainEffect {
    type: TerrainType;
    name: string;
    description: string;
    movementCost: number;
    defensiveBonus: number;
    resourceProduction: {
        gold: number;
        population: number;
    };
    combatModifiers: {
        attackBonus: number;
        defenseBonus: number;
        speedModifier: number;
    };
    specialProperties: string[];
}
export declare const WEATHER_EFFECTS: Record<WeatherType, WeatherEffect>;
export declare const TERRAIN_EFFECTS: Record<TerrainType, TerrainEffect>;
/**
 * 环境系统管理器
 */
export declare class EnvironmentSystem {
    private currentWeather;
    private weatherDuration;
    /**
     * 设置当前天气
     */
    setWeather(weather: WeatherType, duration?: number): void;
    /**
     * 更新天气（每回合调用）
     */
    updateWeather(): void;
    /**
     * 获取当前天气效果
     */
    getCurrentWeatherEffect(): WeatherEffect;
    /**
     * 获取地形效果
     */
    getTerrainEffect(terrain: TerrainType): TerrainEffect;
    /**
     * 计算移动成本
     */
    calculateMovementCost(terrain: TerrainType, weather?: WeatherType): number;
    /**
     * 应用环境效果到英雄
     */
    applyEnvironmentEffects(hero: HeroCard, terrain: TerrainType): HeroCard;
    /**
     * 检查是否应该应用天气效果
     */
    private shouldApplyWeatherEffect;
    /**
     * 计算资源产出修正
     */
    calculateResourceProduction(terrain: TerrainType, baseProduction: {
        gold: number;
        population: number;
    }): {
        gold: number;
        population: number;
    };
    /**
     * 检查地形特殊效果
     */
    checkTerrainSpecialEffects(terrain: TerrainType, action: string): boolean;
    /**
     * 获取环境描述
     */
    getEnvironmentDescription(): string;
    /**
     * 预测天气变化
     */
    generateWeatherForecast(turns?: number): WeatherType[];
}
//# sourceMappingURL=EnvironmentSystem.d.ts.map