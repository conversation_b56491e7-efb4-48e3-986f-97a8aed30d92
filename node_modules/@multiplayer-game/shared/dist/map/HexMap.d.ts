import { HexTile, Position } from '../types';
/**
 * 六边形地图系统
 * 实现六边形网格的坐标转换、邻居查找、路径计算等功能
 */
export declare class HexMap {
    private tiles;
    private width;
    private height;
    constructor(width?: number, height?: number);
    /**
     * 生成六边形地图
     */
    private generateMap;
    /**
     * 生成地形类型（基于位置的程序化生成）
     */
    private generateTerrain;
    /**
     * 简单噪声函数
     */
    private simpleNoise;
    /**
     * 生成资源产出
     */
    private generateResources;
    /**
     * 获取六边形ID
     */
    getHexId(position: Position): string;
    /**
     * 获取地块
     */
    getTile(position: Position): HexTile | undefined;
    /**
     * 获取所有地块
     */
    getAllTiles(): HexTile[];
    /**
     * 获取六边形的邻居坐标
     */
    getNeighbors(position: Position): Position[];
    /**
     * 计算两个六边形之间的距离
     */
    getDistance(pos1: Position, pos2: Position): number;
    /**
     * 检查位置是否相邻
     */
    areAdjacent(pos1: Position, pos2: Position): boolean;
    /**
     * 获取从起点到终点的路径
     */
    getPath(start: Position, end: Position): Position[];
    /**
     * 更新地块
     */
    updateTile(position: Position, updates: Partial<HexTile>): void;
    /**
     * 获取地图尺寸
     */
    getSize(): {
        width: number;
        height: number;
    };
}
//# sourceMappingURL=HexMap.d.ts.map