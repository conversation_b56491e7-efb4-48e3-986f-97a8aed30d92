# 🎮 三国SLG+自走棋 多人在线游戏

一个创新的多人在线游戏，结合了SLG（战略模拟）和自走棋的玩法，以三国为背景，支持8人同时对战。基于 Phaser3、Colyseus 和 Vite 构建。

## 技术栈

- **前端**: Vite + Phaser3 + TypeScript
- **后端**: Node.js + Colyseus + Express
- **实时通信**: WebSocket (Colyseus)
- **构建工具**: Vite

## 项目结构

```
├── server/          # Colyseus 游戏服务器
├── client/          # Vite + Phaser3 游戏客户端
├── shared/          # 共享类型定义和常量
├── package.json     # 根目录配置
└── README.md        # 项目说明
```

## 🌟 游戏特色

### 🎭 身份系统
- **主公**: 公开身份，拥有强大的领导技能和特殊胜利条件
- **忠臣**: 保护主公，获得团队加成，与主公共同胜利
- **反贼**: 推翻主公，数量优势，击杀主公即可获胜
- **内奸**: 伺机而动，需要在最后时刻获得胜利

### 🗺️ SLG战略层
- **六边形地图**: 丰富的地形系统（平原、山脉、森林、河流等）
- **资源管理**: 金币、人口、经验值的收集和分配
- **领土扩张**: 占领地块，建设建筑，发展经济
- **回合制策略**: 120个大回合，每回合60秒思考时间

### ♟️ 自走棋战斗
- **卡牌部署**: 60秒部署时间，策略性摆放英雄
- **自动战斗**: AI控制战斗，玩家观看精彩对决
- **兵种相克**: 步兵、骑兵、弓兵、战车、法师的相克关系
- **羁绊系统**: 阵营羁绊、标签羁绊提供强力加成

### 🎴 卡牌系统
- **英雄卡**: 三国名将，每个英雄都有独特技能和属性
- **法术卡**: 兵家权谋、玄门道法、墨家机关三大流派
- **战略指令卡**: 影响全局的宏观策略卡牌
- **背包系统**: 容量限制，需要合理规划卡牌携带

### 🌦️ 动态系统
- **天气系统**: 晴天、暴雨、沙暴、极光、严冬、雷暴
- **地形效果**: 不同地形对战斗和经济的影响
- **五行相克**: 金木水火土雷的相克关系

## ✅ 已实现功能

- ✅ 用户注册和登录系统
- ✅ 房间创建和管理
- ✅ 8人匹配系统
- ✅ 身份分配系统
- ✅ 六边形地图生成
- ✅ 战略回合制系统
- ✅ 自走棋战斗引擎
- ✅ 卡牌数据库和管理
- ✅ 羁绊和相克系统
- ✅ 天气和地形系统
- ✅ 机器人测试系统

## 快速开始

### 安装依赖

```bash
npm run install:all
```

### 开发模式

```bash
npm run dev
```

这将同时启动服务器（端口 2567）和客户端开发服务器（端口 5173）。

### 生产构建

```bash
npm run build
```

### 启动生产服务器

```bash
npm start
```

## 开发指南

### 服务器端 (server/)

服务器使用 Colyseus 框架，主要包含：
- 用户认证系统
- 房间管理逻辑
- 游戏状态同步
- 匹配系统

### 客户端 (client/)

客户端使用 Phaser3 游戏引擎，包含：
- 登录注册场景
- 游戏大厅场景
- 多人游戏场景
- 网络通信管理

### 共享代码 (shared/)

包含前后端共享的：
- 类型定义
- 常量配置
- 消息协议

## 许可证



ai 画画

https://www.lovart.ai/canvas?agent=1&inviteCode=DRjtFjq&projectId=25e36fb8adaf4d2ca9978ca3fcfedad1



ui


https://justin3go.com/posts/2025/03/03-front-end-self-sufficient-ui-design-claude-ai-version

https://ew6rccvpnmz.feishu.cn/wiki/ILO2waqXLi1EvqkuKHvcceMOnVd?fromScene=spaceOverview


cd server && npm run dev
启动客户端：cd client && npm run dev
运行演示：cd demo && npx ts-node game-demo.ts

MIT License
