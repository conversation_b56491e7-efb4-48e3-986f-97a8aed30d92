# 多人在线游戏框架

基于 Phaser3、Colyseus 和 Vite 构建的多人在线游戏框架，支持用户注册登录、房间创建、8人匹配等功能。

## 技术栈

- **前端**: Vite + Phaser3 + TypeScript
- **后端**: Node.js + Colyseus + Express
- **实时通信**: WebSocket (Colyseus)
- **构建工具**: Vite

## 项目结构

```
├── server/          # Colyseus 游戏服务器
├── client/          # Vite + Phaser3 游戏客户端
├── shared/          # 共享类型定义和常量
├── package.json     # 根目录配置
└── README.md        # 项目说明
```

## 功能特性

- ✅ 用户注册和登录系统
- ✅ 房间创建和管理
- ✅ 8人匹配系统
- ✅ 实时多人游戏同步
- ✅ 游戏状态管理
- ✅ 响应式游戏界面

## 快速开始

### 安装依赖

```bash
npm run install:all
```

### 开发模式

```bash
npm run dev
```

这将同时启动服务器（端口 2567）和客户端开发服务器（端口 5173）。

### 生产构建

```bash
npm run build
```

### 启动生产服务器

```bash
npm start
```

## 开发指南

### 服务器端 (server/)

服务器使用 Colyseus 框架，主要包含：
- 用户认证系统
- 房间管理逻辑
- 游戏状态同步
- 匹配系统

### 客户端 (client/)

客户端使用 Phaser3 游戏引擎，包含：
- 登录注册场景
- 游戏大厅场景
- 多人游戏场景
- 网络通信管理

### 共享代码 (shared/)

包含前后端共享的：
- 类型定义
- 常量配置
- 消息协议

## 许可证

MIT License
