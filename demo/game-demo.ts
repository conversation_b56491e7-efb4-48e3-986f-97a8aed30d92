import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Faction,
  PlayerIdentity,
  BattleStance,
  GAME_CONFIG
} from '../shared/src/types';
import { GameController } from '../shared/src/game/GameController';
import { ScenarioManager } from '../shared/src/scenario/ScenarioManager';
import { EnvironmentSystem } from '../shared/src/environment/EnvironmentSystem';
import { EconomySystem } from '../shared/src/economy/EconomySystem';

/**
 * 游戏演示
 * 展示完整的SLG+自走棋游戏流程
 */

// 创建演示剧本
const demoScenario: Scenario = {
  id: 'demo_three_kingdoms',
  name: '三国演义',
  description: '经典三国题材，8人身份局',
  mapSize: { width: 16, height: 8 },
  maxTurns: 120,
  specialRules: [],
  initialResources: { gold: 50, population: 100 },
  availableFactions: [Faction.SHU, Faction.WEI, Faction.WU, Faction.QUN],
  weatherEvents: [
    { turn: 10, weather: 'rain', duration: 3 },
    { turn: 25, weather: 'sandstorm', duration: 2 },
    { turn: 50, weather: 'winter', duration: 5 }
  ]
};

// 创建演示玩家
const demoPlayers: Player[] = [
  {
    id: 'player_1',
    username: '刘备',
    identity: PlayerIdentity.EMPEROR,
    identityRevealed: true,
    faction: Faction.SHU,
    level: 1,
    resources: { gold: 50, population: 100, experience: 0 },
    territories: [],
    armies: [],
    cardCollection: [],
    backpackCapacity: 30,
    isReady: true,
    isAlive: true,
    battleStance: BattleStance.DEFEND,
    identitySkills: [],
    skillCooldowns: {}
  },
  {
    id: 'player_2',
    username: '曹操',
    identity: PlayerIdentity.TRAITOR,
    identityRevealed: false,
    faction: Faction.WEI,
    level: 1,
    resources: { gold: 50, population: 100, experience: 0 },
    territories: [],
    armies: [],
    cardCollection: [],
    backpackCapacity: 30,
    isReady: true,
    isAlive: true,
    battleStance: BattleStance.ATTACK,
    identitySkills: [],
    skillCooldowns: {}
  },
  {
    id: 'player_3',
    username: '孙权',
    identity: PlayerIdentity.REBEL,
    identityRevealed: false,
    faction: Faction.WU,
    level: 1,
    resources: { gold: 50, population: 100, experience: 0 },
    territories: [],
    armies: [],
    cardCollection: [],
    backpackCapacity: 30,
    isReady: true,
    isAlive: true,
    battleStance: BattleStance.ATTACK,
    identitySkills: [],
    skillCooldowns: {}
  },
  {
    id: 'player_4',
    username: '关羽',
    identity: PlayerIdentity.LOYALIST,
    identityRevealed: false,
    faction: Faction.SHU,
    level: 1,
    resources: { gold: 50, population: 100, experience: 0 },
    territories: [],
    armies: [],
    cardCollection: [],
    backpackCapacity: 30,
    isReady: true,
    isAlive: true,
    battleStance: BattleStance.SUPPORT,
    identitySkills: [],
    skillCooldowns: {}
  },
  {
    id: 'player_5',
    username: '张飞',
    identity: PlayerIdentity.LOYALIST,
    identityRevealed: false,
    faction: Faction.SHU,
    level: 1,
    resources: { gold: 50, population: 100, experience: 0 },
    territories: [],
    armies: [],
    cardCollection: [],
    backpackCapacity: 30,
    isReady: true,
    isAlive: true,
    battleStance: BattleStance.ATTACK,
    identitySkills: [],
    skillCooldowns: {}
  },
  {
    id: 'player_6',
    username: '董卓',
    identity: PlayerIdentity.REBEL,
    identityRevealed: false,
    faction: Faction.QUN,
    level: 1,
    resources: { gold: 50, population: 100, experience: 0 },
    territories: [],
    armies: [],
    cardCollection: [],
    backpackCapacity: 30,
    isReady: true,
    isAlive: true,
    battleStance: BattleStance.ATTACK,
    identitySkills: [],
    skillCooldowns: {}
  },
  {
    id: 'player_7',
    username: '袁绍',
    identity: PlayerIdentity.REBEL,
    identityRevealed: false,
    faction: Faction.QUN,
    level: 1,
    resources: { gold: 50, population: 100, experience: 0 },
    territories: [],
    armies: [],
    cardCollection: [],
    backpackCapacity: 30,
    isReady: true,
    isAlive: true,
    battleStance: BattleStance.ATTACK,
    identitySkills: [],
    skillCooldowns: {}
  },
  {
    id: 'player_8',
    username: '吕布',
    identity: PlayerIdentity.REBEL,
    identityRevealed: false,
    faction: Faction.QUN,
    level: 1,
    resources: { gold: 50, population: 100, experience: 0 },
    territories: [],
    armies: [],
    cardCollection: [],
    backpackCapacity: 30,
    isReady: true,
    isAlive: true,
    battleStance: BattleStance.ATTACK,
    identitySkills: [],
    skillCooldowns: {}
  }
];

// 创建演示房间
const demoRoom: GameRoom = {
  id: 'demo_room_001',
  name: '三国演义 - 演示房间',
  hostId: 'player_1',
  scenario: demoScenario,
  players: demoPlayers,
  maxPlayers: 8,
  gameState: 'lobby',
  strategicPhase: 'resource_collection',
  currentTurn: 1,
  currentPlayer: 'player_1',
  map: [], // 将由HexMap生成
  weather: {
    current: 'clear',
    forecast: ['rain', 'clear', 'sandstorm', 'clear', 'winter']
  },
  createdAt: new Date(),
  startedAt: new Date()
};

/**
 * 运行游戏演示
 */
async function runGameDemo() {
  console.log('🎮 开始游戏演示');
  console.log('=====================================');

  // 展示剧本系统
  console.log('\n📚 剧本系统演示:');
  const scenarios = ScenarioManager.getAllScenarios();
  scenarios.forEach(scenario => {
    console.log(`- ${scenario.name}: ${scenario.description}`);
  });

  const selectedScenario = ScenarioManager.getRecommendedScenario(8);
  console.log(`\n🎯 选择剧本: ${selectedScenario.name}`);
  demoRoom.scenario = selectedScenario;

  // 创建游戏控制器
  const gameController = new GameController(demoRoom);
  
  console.log('\n📋 游戏信息:');
  console.log(`剧本: ${demoRoom.scenario.name}`);
  console.log(`玩家数量: ${demoRoom.players.length}`);
  console.log(`地图大小: ${demoRoom.scenario.mapSize.width}x${demoRoom.scenario.mapSize.height}`);
  
  console.log('\n👥 玩家身份分配:');
  for (const player of demoRoom.players) {
    const identityText = player.identityRevealed ? player.identity : '隐藏';
    console.log(`${player.username} (${player.faction}) - 身份: ${identityText}`);
  }
  
  // 开始游戏
  console.log('\n🚀 游戏开始！');
  gameController.startGame();
  
  // 模拟游戏流程
  await simulateGameFlow(gameController);
  
  console.log('\n🎉 演示结束');
}

/**
 * 模拟游戏流程
 */
async function simulateGameFlow(controller: GameController) {
  console.log('\n=== 第1回合 ===');
  
  // 模拟玩家行动
  console.log('\n📍 玩家行动阶段:');
  
  // 刘备移动军队
  console.log('刘备移动军队...');
  controller.handlePlayerAction('player_1', 'move_army', {
    armyId: 'army_player_1_1',
    targetPosition: { x: 1, y: 1 }
  });
  
  // 曹操购买卡牌
  console.log('曹操购买卡牌...');
  controller.handlePlayerAction('player_2', 'buy_card', {
    cardId: 'hero_xiahou_dun'
  });
  
  // 孙权使用身份技能
  console.log('孙权尝试使用身份技能...');
  controller.handlePlayerAction('player_3', 'use_identity_skill', {
    skillId: 'rebel_uprising'
  });
  
  // 模拟战斗触发
  console.log('\n⚔️ 触发战斗:');
  controller.triggerCombat({ x: 8, y: 4 });
  
  // 模拟战斗部署
  console.log('玩家部署卡牌...');
  controller.handlePlayerAction('player_1', 'deploy_cards', {
    deployments: [
      { cardId: 'hero_liu_bei', position: { x: 1, y: 3 } },
      { cardId: 'hero_guan_yu', position: { x: 2, y: 3 } },
      { cardId: 'hero_zhang_fei', position: { x: 1, y: 4 } }
    ]
  });
  
  controller.handlePlayerAction('player_2', 'deploy_cards', {
    deployments: [
      { cardId: 'hero_cao_cao', position: { x: 14, y: 3 } },
      { cardId: 'hero_xiahou_dun', position: { x: 13, y: 3 } }
    ]
  });
  
  // 等待战斗结果
  console.log('自动战斗进行中...');
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  console.log('\n📊 游戏状态:');
  const gameState = controller.getGameState();
  console.log(`当前回合: ${gameState.currentTurn}`);
  console.log(`游戏阶段: ${gameState.gameState}`);
  console.log(`天气: ${gameState.weather.current}`);
  
  console.log('\n💰 玩家资源:');
  for (const player of gameState.players) {
    if (player.isAlive) {
      console.log(`${player.username}: 金币${player.resources.gold}, 人口${player.resources.population}, 经验${player.resources.experience}`);
    }
  }
  
  // 清理资源
  controller.dispose();
}

/**
 * 展示游戏特色
 */
function showGameFeatures() {
  console.log('\n🌟 游戏特色展示:');
  console.log('=====================================');
  
  console.log('\n1. 🎭 身份系统:');
  console.log('   - 主公: 公开身份，拥有强大的领导技能');
  console.log('   - 忠臣: 保护主公，获得团队加成');
  console.log('   - 反贼: 推翻主公，数量优势');
  console.log('   - 内奸: 伺机而动，最后获胜');
  
  console.log('\n2. 🗺️ SLG战略层:');
  console.log('   - 六边形地图，丰富地形');
  console.log('   - 资源管理，领土扩张');
  console.log('   - 回合制策略，深度思考');
  
  console.log('\n3. ♟️ 自走棋战斗:');
  console.log('   - 卡牌部署，策略搭配');
  console.log('   - 自动战斗，观赏性强');
  console.log('   - 兵种相克，羁绊系统');
  
  console.log('\n4. 🎴 卡牌系统:');
  console.log('   - 英雄卡: 三国名将，各具特色');
  console.log('   - 法术卡: 奇门遁甲，扭转战局');
  console.log('   - 战略卡: 宏观调控，影响全局');
  
  console.log('\n5. 🌦️ 动态要素:');
  console.log('   - 天气系统，影响战斗');
  console.log('   - 地形效果，战术考量');
  console.log('   - 随机事件，增加变数');
}

// 运行演示
if (require.main === module) {
  showGameFeatures();
  runGameDemo().catch(console.error);
}

export { runGameDemo, showGameFeatures };
