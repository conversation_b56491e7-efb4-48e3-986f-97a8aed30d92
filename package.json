{"name": "multiplayer-game-framework", "version": "1.0.0", "description": "多人在线游戏框架 - 基于Phaser3、Colyseus和Vite", "private": true, "workspaces": ["server", "client", "shared"], "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "dev:server": "npm run dev --workspace=server", "dev:client": "npm run dev --workspace=client", "build": "npm run build --workspace=client && npm run build --workspace=server", "start": "npm run start --workspace=server", "install:all": "npm install && npm install --workspace=server && npm install --workspace=client && npm install --workspace=shared"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=16.0.0"}}